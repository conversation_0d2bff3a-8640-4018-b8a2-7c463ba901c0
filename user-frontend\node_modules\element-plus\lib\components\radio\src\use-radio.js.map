{"version": 3, "file": "use-radio.js", "sources": ["../../../../../../packages/components/radio/src/use-radio.ts"], "sourcesContent": ["import { computed, inject, ref } from 'vue'\nimport { UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport { useFormDisabled, useFormSize } from '@element-plus/components/form'\nimport { useDeprecated } from '@element-plus/hooks'\nimport { isPropAbsent } from '@element-plus/utils'\nimport { radioGroupKey } from './constants'\n\nimport type { RadioButtonProps } from './radio-button'\nimport type { SetupContext } from 'vue'\nimport type { RadioEmits, RadioProps } from './radio'\n\nexport const useRadio = (\n  props: RadioProps | RadioButtonProps,\n  emit?: SetupContext<RadioEmits>['emit']\n) => {\n  const radioRef = ref<HTMLInputElement>()\n  const radioGroup = inject(radioGroupKey, undefined)\n  const isGroup = computed(() => !!radioGroup)\n  const actualValue = computed(() => {\n    // In version 2.x, if there's no props.value, props.label will act as props.value\n    // In version 3.x, remove this computed value, use props.value instead.\n    if (!isPropAbsent(props.value)) {\n      return props.value\n    }\n    return props.label\n  })\n  const modelValue = computed<RadioProps['modelValue']>({\n    get() {\n      return isGroup.value ? radioGroup!.modelValue : props.modelValue!\n    },\n    set(val) {\n      if (isGroup.value) {\n        radioGroup!.changeEvent(val)\n      } else {\n        emit && emit(UPDATE_MODEL_EVENT, val)\n      }\n      radioRef.value!.checked = props.modelValue === actualValue.value\n    },\n  })\n\n  const size = useFormSize(computed(() => radioGroup?.size))\n  const disabled = useFormDisabled(computed(() => radioGroup?.disabled))\n  const focus = ref(false)\n  const tabIndex = computed(() => {\n    return disabled.value ||\n      (isGroup.value && modelValue.value !== actualValue.value)\n      ? -1\n      : 0\n  })\n\n  useDeprecated(\n    {\n      from: 'label act as value',\n      replacement: 'value',\n      version: '3.0.0',\n      scope: 'el-radio',\n      ref: 'https://element-plus.org/en-US/component/radio.html',\n    },\n    computed(() => isGroup.value && isPropAbsent(props.value))\n  )\n\n  return {\n    radioRef,\n    isGroup,\n    radioGroup,\n    focus,\n    size,\n    disabled,\n    tabIndex,\n    modelValue,\n    actualValue,\n  }\n}\n"], "names": ["ref", "inject", "radioGroupKey", "computed", "isPropAbsent", "UPDATE_MODEL_EVENT", "useFormSize", "useFormDisabled", "useDeprecated"], "mappings": ";;;;;;;;;;;AAMY,MAAC,QAAQ,GAAG,CAAC,KAAK,EAAE,IAAI,KAAK;AACzC,EAAE,MAAM,QAAQ,GAAGA,OAAG,EAAE,CAAC;AACzB,EAAE,MAAM,UAAU,GAAGC,UAAM,CAACC,uBAAa,EAAE,KAAK,CAAC,CAAC,CAAC;AACnD,EAAE,MAAM,OAAO,GAAGC,YAAQ,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,CAAC;AAC/C,EAAE,MAAM,WAAW,GAAGA,YAAQ,CAAC,MAAM;AACrC,IAAI,IAAI,CAACC,kBAAY,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;AACpC,MAAM,OAAO,KAAK,CAAC,KAAK,CAAC;AACzB,KAAK;AACL,IAAI,OAAO,KAAK,CAAC,KAAK,CAAC;AACvB,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,UAAU,GAAGD,YAAQ,CAAC;AAC9B,IAAI,GAAG,GAAG;AACV,MAAM,OAAO,OAAO,CAAC,KAAK,GAAG,UAAU,CAAC,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC;AACtE,KAAK;AACL,IAAI,GAAG,CAAC,GAAG,EAAE;AACb,MAAM,IAAI,OAAO,CAAC,KAAK,EAAE;AACzB,QAAQ,UAAU,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;AACpC,OAAO,MAAM;AACb,QAAQ,IAAI,IAAI,IAAI,CAACE,wBAAkB,EAAE,GAAG,CAAC,CAAC;AAC9C,OAAO;AACP,MAAM,QAAQ,CAAC,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,UAAU,KAAK,WAAW,CAAC,KAAK,CAAC;AACtE,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,IAAI,GAAGC,8BAAW,CAACH,YAAQ,CAAC,MAAM,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;AAC1F,EAAE,MAAM,QAAQ,GAAGI,kCAAe,CAACJ,YAAQ,CAAC,MAAM,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC;AACtG,EAAE,MAAM,KAAK,GAAGH,OAAG,CAAC,KAAK,CAAC,CAAC;AAC3B,EAAE,MAAM,QAAQ,GAAGG,YAAQ,CAAC,MAAM;AAClC,IAAI,OAAO,QAAQ,CAAC,KAAK,IAAI,OAAO,CAAC,KAAK,IAAI,UAAU,CAAC,KAAK,KAAK,WAAW,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AAC9F,GAAG,CAAC,CAAC;AACL,EAAEK,mBAAa,CAAC;AAChB,IAAI,IAAI,EAAE,oBAAoB;AAC9B,IAAI,WAAW,EAAE,OAAO;AACxB,IAAI,OAAO,EAAE,OAAO;AACpB,IAAI,KAAK,EAAE,UAAU;AACrB,IAAI,GAAG,EAAE,qDAAqD;AAC9D,GAAG,EAAEL,YAAQ,CAAC,MAAM,OAAO,CAAC,KAAK,IAAIC,kBAAY,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACjE,EAAE,OAAO;AACT,IAAI,QAAQ;AACZ,IAAI,OAAO;AACX,IAAI,UAAU;AACd,IAAI,KAAK;AACT,IAAI,IAAI;AACR,IAAI,QAAQ;AACZ,IAAI,QAAQ;AACZ,IAAI,UAAU;AACd,IAAI,WAAW;AACf,GAAG,CAAC;AACJ;;;;"}