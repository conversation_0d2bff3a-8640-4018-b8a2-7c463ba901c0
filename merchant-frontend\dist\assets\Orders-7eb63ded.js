import{u as oe,r as S,a as w,o as le,b as d,j as ne,c as N,d as de,f as t,w as a,e as l,A as k,h as _,t as i,k as re,y as z,l as I,z as $,E as m,H as ce,C as q}from"./index-2c4e09fa.js";import{c as T,m as V}from"./api-7a2a6e2d.js";import{a as ie}from"./utils-750ce239.js";import{_ as pe}from"./_plugin-vue_export-helper-c27b6911.js";const ue={class:"orders-page"},_e={class:"page-header"},me={class:"header-actions"},ge={class:"stats-section"},fe={class:"stat-content"},ve={class:"stat-value"},he={class:"stat-content"},ye={class:"stat-value pending"},xe={class:"stat-content"},be={class:"stat-value processing"},Ce={class:"stat-content"},Ne={class:"stat-value completed"},ke={class:"search-section"},Oe={class:"product-info"},Se={class:"product-name"},we={class:"product-spec"},ze={class:"amount"},Te={class:"pagination"},Ve={__name:"Orders",setup(Ae){const E=oe(),O=S(!1),g=S([]),U=S([]),x=w({total:0,pending:0,processing:0,completed:0}),p=w({orderNo:"",status:"",dateRange:null}),n=w({page:1,size:10,total:0}),A={pending:{text:"待付款",type:"warning"},paid:{text:"待发货",type:"info"},shipped:{text:"已发货",type:"primary"},completed:{text:"已完成",type:"success"},cancelled:{text:"已取消",type:"danger"}},j=s=>{var e;return((e=A[s])==null?void 0:e.text)||s},M=s=>{var e;return((e=A[s])==null?void 0:e.type)||"info"},f=[{id:1,orderNo:"ORD202507070001",productName:"新鲜有机西红柿",productSpec:"500g/袋",productImage:"https://via.placeholder.com/40x40?text=西红柿",quantity:2,totalAmount:25.6,customerName:"张三",status:"paid",createTime:"2025-07-07 10:30:00"},{id:2,orderNo:"ORD202507070002",productName:"优质苹果",productSpec:"1kg/袋",productImage:"https://via.placeholder.com/40x40?text=苹果",quantity:1,totalAmount:8.5,customerName:"李四",status:"shipped",createTime:"2025-07-07 09:15:00"},{id:3,orderNo:"ORD202507070003",productName:"东北大米",productSpec:"5kg/袋",productImage:"https://via.placeholder.com/40x40?text=大米",quantity:1,totalAmount:25,customerName:"王五",status:"completed",createTime:"2025-07-06 16:45:00"},{id:4,orderNo:"ORD202507070004",productName:"新鲜猪肉",productSpec:"1kg",productImage:"https://via.placeholder.com/40x40?text=猪肉",quantity:1,totalAmount:35,customerName:"赵六",status:"pending",createTime:"2025-07-07 14:20:00"}],v=async()=>{O.value=!0;try{const s={page:n.page,size:n.size,...p},e=T(),r=await V.orders.list(e,s);r.code===0?(g.value=r.data.list||f,n.total=r.data.total||f.length):(g.value=f,n.total=f.length),B()}catch(s){console.error("获取订单列表失败:",s),g.value=f,n.total=f.length,B()}finally{O.value=!1}},B=()=>{const s={total:g.value.length,pending:0,processing:0,completed:0};g.value.forEach(e=>{e.status==="pending"?s.pending++:e.status==="paid"||e.status==="shipped"?s.processing++:e.status==="completed"&&s.completed++}),Object.assign(x,s)},b=()=>{n.page=1,v()},L=()=>{Object.assign(p,{orderNo:"",status:"",dateRange:null}),b()},F=s=>{U.value=s},H=s=>{n.size=s,n.page=1,v()},P=s=>{n.page=s,v()},G=s=>{E.push(`/orders/detail/${s.id}`)},J=async s=>{try{await $.confirm(`确定要发货订单"${s.orderNo}"吗？`,"发货确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=T();(await V.orders.updateStatus(e,{id:s.id,nongchanpinOrderTypes:3})).code===0?(m.success("订单已发货"),v()):m.error("发货失败")}catch(e){e!=="cancel"&&(console.error("发货失败:",e),m.error("发货失败"))}},K=async s=>{try{await $.confirm(`确定要取消订单"${s.orderNo}"吗？`,"取消确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=T();(await V.orders.updateStatus(e,{id:s.id,nongchanpinOrderTypes:5})).code===0?(m.success("订单已取消"),v()):m.error("取消失败")}catch(e){e!=="cancel"&&(console.error("取消失败:",e),m.error("取消失败"))}},Q=()=>{m.info("导出功能开发中...")};return le(()=>{v()}),(s,e)=>{const r=d("el-icon"),h=d("el-button"),C=d("el-card"),u=d("el-col"),R=d("el-row"),W=d("el-input"),y=d("el-option"),X=d("el-select"),Y=d("el-date-picker"),c=d("el-table-column"),Z=d("el-image"),ee=d("el-tag"),te=d("el-table"),ae=d("el-pagination"),se=ne("loading");return N(),de("div",ue,[t(C,null,{header:a(()=>[l("div",_e,[e[6]||(e[6]=l("h2",null,"订单管理",-1)),l("div",me,[t(h,{onClick:Q},{default:a(()=>[t(r,null,{default:a(()=>[t(k(ce))]),_:1}),e[5]||(e[5]=_(" 导出订单 "))]),_:1,__:[5]})])])]),default:a(()=>[l("div",ge,[t(R,{gutter:20},{default:a(()=>[t(u,{span:6},{default:a(()=>[t(C,{class:"stat-card"},{default:a(()=>[l("div",fe,[l("div",ve,i(x.total),1),e[7]||(e[7]=l("div",{class:"stat-label"},"总订单数",-1))])]),_:1})]),_:1}),t(u,{span:6},{default:a(()=>[t(C,{class:"stat-card"},{default:a(()=>[l("div",he,[l("div",ye,i(x.pending),1),e[8]||(e[8]=l("div",{class:"stat-label"},"待处理",-1))])]),_:1})]),_:1}),t(u,{span:6},{default:a(()=>[t(C,{class:"stat-card"},{default:a(()=>[l("div",xe,[l("div",be,i(x.processing),1),e[9]||(e[9]=l("div",{class:"stat-label"},"处理中",-1))])]),_:1})]),_:1}),t(u,{span:6},{default:a(()=>[t(C,{class:"stat-card"},{default:a(()=>[l("div",Ce,[l("div",Ne,i(x.completed),1),e[10]||(e[10]=l("div",{class:"stat-label"},"已完成",-1))])]),_:1})]),_:1})]),_:1})]),l("div",ke,[t(R,{gutter:20},{default:a(()=>[t(u,{span:6},{default:a(()=>[t(W,{modelValue:p.orderNo,"onUpdate:modelValue":e[0]||(e[0]=o=>p.orderNo=o),placeholder:"搜索订单号",clearable:"",onChange:b},{prefix:a(()=>[t(r,null,{default:a(()=>[t(k(q))]),_:1})]),_:1},8,["modelValue"])]),_:1}),t(u,{span:4},{default:a(()=>[t(X,{modelValue:p.status,"onUpdate:modelValue":e[1]||(e[1]=o=>p.status=o),placeholder:"订单状态",clearable:"",onChange:b},{default:a(()=>[t(y,{label:"全部状态",value:""}),t(y,{label:"待付款",value:"pending"}),t(y,{label:"待发货",value:"paid"}),t(y,{label:"已发货",value:"shipped"}),t(y,{label:"已完成",value:"completed"}),t(y,{label:"已取消",value:"cancelled"})]),_:1},8,["modelValue"])]),_:1}),t(u,{span:6},{default:a(()=>[t(Y,{modelValue:p.dateRange,"onUpdate:modelValue":e[2]||(e[2]=o=>p.dateRange=o),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",onChange:b},null,8,["modelValue"])]),_:1}),t(u,{span:4},{default:a(()=>[t(h,{type:"primary",onClick:b},{default:a(()=>[t(r,null,{default:a(()=>[t(k(q))]),_:1}),e[11]||(e[11]=_(" 搜索 "))]),_:1,__:[11]}),t(h,{onClick:L},{default:a(()=>e[12]||(e[12]=[_("重置")])),_:1,__:[12]})]),_:1})]),_:1})]),re((N(),z(te,{data:g.value,style:{width:"100%"},onSelectionChange:F},{default:a(()=>[t(c,{type:"selection",width:"55"}),t(c,{prop:"orderNo",label:"订单号",width:"180"}),t(c,{label:"商品信息","min-width":"200"},{default:a(({row:o})=>[l("div",Oe,[t(Z,{src:o.productImage,style:{width:"40px",height:"40px","margin-right":"10px"},fit:"cover"},null,8,["src"]),l("div",null,[l("div",Se,i(o.productName),1),l("div",we,i(o.productSpec),1)])])]),_:1}),t(c,{prop:"quantity",label:"数量",width:"80"}),t(c,{prop:"totalAmount",label:"订单金额",width:"100"},{default:a(({row:o})=>[l("span",ze,"¥"+i(o.totalAmount),1)]),_:1}),t(c,{prop:"customerName",label:"客户",width:"100"}),t(c,{prop:"status",label:"状态",width:"100"},{default:a(({row:o})=>[t(ee,{type:M(o.status)},{default:a(()=>[_(i(j(o.status)),1)]),_:2},1032,["type"])]),_:1}),t(c,{prop:"createTime",label:"下单时间",width:"150"},{default:a(({row:o})=>[_(i(k(ie)(o.createTime)),1)]),_:1}),t(c,{label:"操作",width:"200",fixed:"right"},{default:a(({row:o})=>[t(h,{type:"primary",size:"small",onClick:D=>G(o)},{default:a(()=>e[13]||(e[13]=[_(" 查看 ")])),_:2,__:[13]},1032,["onClick"]),o.status==="paid"?(N(),z(h,{key:0,type:"success",size:"small",onClick:D=>J(o)},{default:a(()=>e[14]||(e[14]=[_(" 发货 ")])),_:2,__:[14]},1032,["onClick"])):I("",!0),o.status==="pending"?(N(),z(h,{key:1,type:"danger",size:"small",onClick:D=>K(o)},{default:a(()=>e[15]||(e[15]=[_(" 取消 ")])),_:2,__:[15]},1032,["onClick"])):I("",!0)]),_:1})]),_:1},8,["data"])),[[se,O.value]]),l("div",Te,[t(ae,{"current-page":n.page,"onUpdate:currentPage":e[3]||(e[3]=o=>n.page=o),"page-size":n.size,"onUpdate:pageSize":e[4]||(e[4]=o=>n.size=o),"page-sizes":[10,20,50,100],total:n.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:H,onCurrentChange:P},null,8,["current-page","page-size","total"])])]),_:1})])}}},$e=pe(Ve,[["__scopeId","data-v-78a42ca4"]]);export{$e as default};
