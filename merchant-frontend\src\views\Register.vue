<template>
  <div class="register-container">
    <div class="register-form">
      <div class="form-header">
        <h2>🏪 商家入驻申请</h2>
        <p class="subtitle">农产品销售系统</p>
      </div>
      
      <form @submit.prevent="handleRegister">
        <!-- 基本信息 -->
        <div class="section">
          <h3>📋 基本信息</h3>
          <div class="form-group">
            <label>商家名称 *</label>
            <input 
              v-model="form.merchantName" 
              type="text" 
              placeholder="请输入商家名称"
              required
              :class="{ 'error': errors.merchantName }"
            >
            <span v-if="errors.merchantName" class="error-text">{{ errors.merchantName }}</span>
          </div>
          
          <div class="form-group">
            <label>联系人姓名 *</label>
            <input 
              v-model="form.contactName" 
              type="text" 
              placeholder="请输入联系人姓名"
              required
              :class="{ 'error': errors.contactName }"
            >
            <span v-if="errors.contactName" class="error-text">{{ errors.contactName }}</span>
          </div>
          
          <div class="form-row">
            <div class="form-group">
              <label>手机号 *</label>
              <input 
                v-model="form.phone" 
                type="tel" 
                placeholder="请输入手机号"
                required
                :class="{ 'error': errors.phone }"
              >
              <span v-if="errors.phone" class="error-text">{{ errors.phone }}</span>
            </div>
            
            <div class="form-group">
              <label>邮箱 *</label>
              <input 
                v-model="form.email" 
                type="email" 
                placeholder="请输入邮箱地址"
                required
                :class="{ 'error': errors.email }"
              >
              <span v-if="errors.email" class="error-text">{{ errors.email }}</span>
            </div>
          </div>
        </div>

        <!-- 店铺信息 -->
        <div class="section">
          <h3>🏬 店铺信息</h3>
          <div class="form-group">
            <label>店铺名称 *</label>
            <input 
              v-model="form.shopName" 
              type="text" 
              placeholder="请输入店铺名称"
              required
              :class="{ 'error': errors.shopName }"
            >
            <span v-if="errors.shopName" class="error-text">{{ errors.shopName }}</span>
          </div>
          
          <div class="form-group">
            <label>经营类别 *</label>
            <select v-model="form.category" required :class="{ 'error': errors.category }">
              <option value="">请选择经营类别</option>
              <option value="fruits">水果类</option>
              <option value="vegetables">蔬菜类</option>
              <option value="grains">粮食类</option>
              <option value="meat">肉类</option>
              <option value="dairy">乳制品</option>
              <option value="seafood">水产类</option>
              <option value="other">其他</option>
            </select>
            <span v-if="errors.category" class="error-text">{{ errors.category }}</span>
          </div>
          
          <div class="form-group">
            <label>详细地址 *</label>
            <textarea 
              v-model="form.address" 
              placeholder="请输入详细地址"
              required
              :class="{ 'error': errors.address }"
            ></textarea>
            <span v-if="errors.address" class="error-text">{{ errors.address }}</span>
          </div>
        </div>

        <!-- 账号信息 -->
        <div class="section">
          <h3>🔐 账号信息</h3>
          <div class="form-group">
            <label>登录用户名 *</label>
            <input 
              v-model="form.username" 
              type="text" 
              placeholder="请输入登录用户名"
              required
              :class="{ 'error': errors.username }"
            >
            <span v-if="errors.username" class="error-text">{{ errors.username }}</span>
          </div>
          
          <div class="form-row">
            <div class="form-group">
              <label>密码 *</label>
              <input 
                v-model="form.password" 
                type="password" 
                placeholder="请输入密码"
                required
                :class="{ 'error': errors.password }"
              >
              <span v-if="errors.password" class="error-text">{{ errors.password }}</span>
            </div>
            
            <div class="form-group">
              <label>确认密码 *</label>
              <input 
                v-model="form.confirmPassword" 
                type="password" 
                placeholder="请再次输入密码"
                required
                :class="{ 'error': errors.confirmPassword }"
              >
              <span v-if="errors.confirmPassword" class="error-text">{{ errors.confirmPassword }}</span>
            </div>
          </div>
        </div>

        <!-- 资质信息 -->
        <div class="section">
          <h3>📄 资质信息</h3>
          <div class="form-group">
            <label>营业执照号</label>
            <input 
              v-model="form.businessLicense" 
              type="text" 
              placeholder="请输入营业执照号（可选）"
            >
          </div>
          
          <div class="form-group">
            <label>食品经营许可证号</label>
            <input 
              v-model="form.foodLicense" 
              type="text" 
              placeholder="请输入食品经营许可证号（可选）"
            >
          </div>
          
          <div class="form-group">
            <label>备注说明</label>
            <textarea 
              v-model="form.description" 
              placeholder="请简要描述您的经营情况、产品特色等"
              rows="3"
            ></textarea>
          </div>
        </div>

        <!-- 协议同意 -->
        <div class="agreement">
          <label class="checkbox-label">
            <input type="checkbox" v-model="form.agreeTerms" required>
            <span class="checkmark"></span>
            我已阅读并同意 <a href="#" @click.prevent="showTerms">《商家入驻协议》</a>
          </label>
        </div>

        <button type="submit" class="register-btn" :disabled="isSubmitting">
          <span v-if="isSubmitting">提交中...</span>
          <span v-else">🚀 提交入驻申请</span>
        </button>
      </form>
      
      <div class="login-link">
        已有账号？<router-link to="/login">立即登录</router-link>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 响应式数据
const isSubmitting = ref(false)
const errors = ref({})

const form = reactive({
  merchantName: '',
  contactName: '',
  phone: '',
  email: '',
  shopName: '',
  category: '',
  address: '',
  username: '',
  password: '',
  confirmPassword: '',
  businessLicense: '',
  foodLicense: '',
  description: '',
  agreeTerms: false
})

// 表单验证
const validateForm = () => {
  errors.value = {}
  
  if (!form.merchantName.trim()) {
    errors.value.merchantName = '请输入商家名称'
  }
  
  if (!form.contactName.trim()) {
    errors.value.contactName = '请输入联系人姓名'
  }
  
  if (!form.phone.trim()) {
    errors.value.phone = '请输入手机号'
  } else if (!/^1[3-9]\d{9}$/.test(form.phone)) {
    errors.value.phone = '请输入正确的手机号'
  }
  
  if (!form.email.trim()) {
    errors.value.email = '请输入邮箱'
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.email)) {
    errors.value.email = '请输入正确的邮箱格式'
  }
  
  if (!form.shopName.trim()) {
    errors.value.shopName = '请输入店铺名称'
  }
  
  if (!form.category) {
    errors.value.category = '请选择经营类别'
  }
  
  if (!form.address.trim()) {
    errors.value.address = '请输入详细地址'
  }
  
  if (!form.username.trim()) {
    errors.value.username = '请输入登录用户名'
  } else if (form.username.length < 3) {
    errors.value.username = '用户名至少3个字符'
  }
  
  if (!form.password) {
    errors.value.password = '请输入密码'
  } else if (form.password.length < 6) {
    errors.value.password = '密码至少6个字符'
  }
  
  if (!form.confirmPassword) {
    errors.value.confirmPassword = '请确认密码'
  } else if (form.password !== form.confirmPassword) {
    errors.value.confirmPassword = '两次输入的密码不一致'
  }
  
  return Object.keys(errors.value).length === 0
}

// 提交注册
const handleRegister = async () => {
  if (!validateForm()) {
    return
  }
  
  if (!form.agreeTerms) {
    alert('请先同意商家入驻协议')
    return
  }
  
  isSubmitting.value = true
  
  try {
    // 模拟API调用
    console.log('商家注册申请:', form)
    
    // 模拟延迟
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    alert('🎉 入驻申请已提交成功！\n\n我们将在1-3个工作日内完成审核，请耐心等待。\n审核结果将通过邮件和短信通知您。')
    
    router.push('/login')
  } catch (error) {
    console.error('注册失败:', error)
    alert('提交失败，请稍后重试')
  } finally {
    isSubmitting.value = false
  }
}

// 显示协议
const showTerms = () => {
  alert('商家入驻协议内容...\n（这里应该显示完整的协议内容）')
}
</script>

<style scoped>
.register-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.register-form {
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
}

.form-header {
  text-align: center;
  margin-bottom: 30px;
}

.form-header h2 {
  color: #333;
  margin-bottom: 8px;
  font-size: 28px;
  font-weight: 600;
}

.subtitle {
  color: #666;
  font-size: 14px;
  margin: 0;
}

.section {
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.section h3 {
  color: #333;
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: 600;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: #555;
  font-weight: 500;
  font-size: 14px;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group input.error,
.form-group select.error,
.form-group textarea.error {
  border-color: #e74c3c;
}

.error-text {
  color: #e74c3c;
  font-size: 12px;
  margin-top: 4px;
  display: block;
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.agreement {
  margin: 30px 0;
  padding: 20px;
  background: #f0f7ff;
  border-radius: 8px;
  border: 1px solid #b3d9ff;
}

.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
  color: #555;
}

.checkbox-label input[type="checkbox"] {
  margin-right: 10px;
  transform: scale(1.2);
}

.checkbox-label a {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
}

.checkbox-label a:hover {
  text-decoration: underline;
}

.register-btn {
  width: 100%;
  padding: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 20px;
}

.register-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.register-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.login-link {
  text-align: center;
  color: #666;
  font-size: 14px;
}

.login-link a {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
}

.login-link a:hover {
  text-decoration: underline;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .register-form {
    padding: 20px;
    margin: 10px;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 0;
  }

  .section {
    padding: 15px;
  }
}

@media (max-width: 480px) {
  .form-header h2 {
    font-size: 24px;
  }

  .register-form {
    padding: 15px;
  }
}
</style>
