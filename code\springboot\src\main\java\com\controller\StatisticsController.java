package com.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.entity.NongchanpinEntity;
import com.entity.NongchanpinOrderEntity;
import com.entity.ShangjiaEntity;
import com.entity.YonghuEntity;
import com.service.NongchanpinOrderService;
import com.service.NongchanpinService;
import com.service.ShangjiaService;
import com.service.YonghuService;
import com.utils.R;

/**
 * 统计数据控制器
 * 提供各种统计数据接口
 */
@RequestMapping("/statistics")
@RestController
public class StatisticsController {
    
    @Autowired
    private YonghuService yonghuService;
    
    @Autowired
    private ShangjiaService shangjiaService;
    
    @Autowired
    private NongchanpinService nongchanpinService;
    
    @Autowired
    private NongchanpinOrderService nongchanpinOrderService;

    /**
     * 管理员仪表板统计
     */
    @RequestMapping("/admin/dashboard")
    public R adminDashboard(HttpServletRequest request) {
        String role = String.valueOf(request.getSession().getAttribute("role"));
        if (!"管理员".equals(role)) {
            return R.error("权限不足");
        }
        
        Map<String, Object> result = new HashMap<>();
        
        // 总用户数
        int totalUsers = yonghuService.selectCount(new EntityWrapper<YonghuEntity>());
        result.put("totalUsers", totalUsers);
        
        // 总商家数（只统计未删除的）
        int totalMerchants = shangjiaService.selectCount(
            new EntityWrapper<ShangjiaEntity>().eq("shangjia_delete", 1)
        );
        result.put("totalMerchants", totalMerchants);
        
        // 总产品数
        int totalProducts = nongchanpinService.selectCount(new EntityWrapper<NongchanpinEntity>());
        result.put("totalProducts", totalProducts);
        
        // 总订单数
        int totalOrders = nongchanpinOrderService.selectCount(new EntityWrapper<NongchanpinOrderEntity>());
        result.put("totalOrders", totalOrders);
        
        // 待审核商家数
        int pendingMerchants = shangjiaService.selectCount(
            new EntityWrapper<ShangjiaEntity>()
                .eq("shangjia_delete", 1)
                .eq("shangjia_yesno_types", 1)
        );
        result.put("pendingMerchants", pendingMerchants);
        
        return R.ok().put("data", result);
    }
    
    /**
     * 商家仪表板统计
     */
    @RequestMapping("/merchant/dashboard")
    public R merchantDashboard(HttpServletRequest request) {
        String role = String.valueOf(request.getSession().getAttribute("role"));
        if (!"商家".equals(role)) {
            return R.error("权限不足");
        }
        
        Integer merchantId = (Integer) request.getSession().getAttribute("userId");
        Map<String, Object> result = new HashMap<>();
        
        // 商家的产品数
        int myProducts = nongchanpinService.selectCount(
            new EntityWrapper<NongchanpinEntity>().eq("shangjia_id", merchantId)
        );
        result.put("myProducts", myProducts);
        
        // 商家的订单数
        int myOrders = nongchanpinOrderService.selectCount(
            new EntityWrapper<NongchanpinOrderEntity>().eq("shangjia_id", merchantId)
        );
        result.put("myOrders", myOrders);
        
        // 待处理订单数
        int pendingOrders = nongchanpinOrderService.selectCount(
            new EntityWrapper<NongchanpinOrderEntity>()
                .eq("shangjia_id", merchantId)
                .eq("nongchanpin_order_types", 1)
        );
        result.put("pendingOrders", pendingOrders);
        
        // 已完成订单数
        int completedOrders = nongchanpinOrderService.selectCount(
            new EntityWrapper<NongchanpinOrderEntity>()
                .eq("shangjia_id", merchantId)
                .eq("nongchanpin_order_types", 3)
        );
        result.put("completedOrders", completedOrders);
        
        // 计算总销售额（这里简化处理，实际应该根据订单金额计算）
        List<NongchanpinOrderEntity> orders = nongchanpinOrderService.selectList(
            new EntityWrapper<NongchanpinOrderEntity>()
                .eq("shangjia_id", merchantId)
                .eq("nongchanpin_order_types", 3)
        );
        
        double totalSales = 0.0;
        for (NongchanpinOrderEntity order : orders) {
            if (order.getNongchanpinOrderTruePrice() != null) {
                totalSales += order.getNongchanpinOrderTruePrice();
            }
        }
        result.put("totalSales", totalSales);
        
        return R.ok().put("data", result);
    }
    
    /**
     * 用户统计
     */
    @RequestMapping("/user/dashboard")
    public R userDashboard(HttpServletRequest request) {
        String role = String.valueOf(request.getSession().getAttribute("role"));
        if (!"用户".equals(role)) {
            return R.error("权限不足");
        }
        
        Integer userId = (Integer) request.getSession().getAttribute("userId");
        Map<String, Object> result = new HashMap<>();
        
        // 用户的订单数
        int myOrders = nongchanpinOrderService.selectCount(
            new EntityWrapper<NongchanpinOrderEntity>().eq("yonghu_id", userId)
        );
        result.put("myOrders", myOrders);
        
        // 待支付订单数
        int pendingPayment = nongchanpinOrderService.selectCount(
            new EntityWrapper<NongchanpinOrderEntity>()
                .eq("yonghu_id", userId)
                .eq("nongchanpin_order_types", 1)
        );
        result.put("pendingPayment", pendingPayment);
        
        // 已完成订单数
        int completedOrders = nongchanpinOrderService.selectCount(
            new EntityWrapper<NongchanpinOrderEntity>()
                .eq("yonghu_id", userId)
                .eq("nongchanpin_order_types", 3)
        );
        result.put("completedOrders", completedOrders);
        
        return R.ok().put("data", result);
    }
    
    /**
     * 订单状态统计
     */
    @RequestMapping("/orders/status")
    public R orderStatusStatistics(@RequestParam Map<String, Object> params, HttpServletRequest request) {
        String role = String.valueOf(request.getSession().getAttribute("role"));
        Integer userId = (Integer) request.getSession().getAttribute("userId");
        
        EntityWrapper<NongchanpinOrderEntity> wrapper = new EntityWrapper<>();
        
        // 根据角色过滤数据
        if ("商家".equals(role)) {
            wrapper.eq("shangjia_id", userId);
        } else if ("用户".equals(role)) {
            wrapper.eq("yonghu_id", userId);
        } else if (!"管理员".equals(role)) {
            return R.error("权限不足");
        }
        
        Map<String, Object> result = new HashMap<>();
        
        // 各状态订单数量统计
        result.put("pending", nongchanpinOrderService.selectCount(wrapper.clone().eq("nongchanpin_order_types", 1)));
        result.put("paid", nongchanpinOrderService.selectCount(wrapper.clone().eq("nongchanpin_order_types", 2)));
        result.put("completed", nongchanpinOrderService.selectCount(wrapper.clone().eq("nongchanpin_order_types", 3)));
        result.put("cancelled", nongchanpinOrderService.selectCount(wrapper.clone().eq("nongchanpin_order_types", 5)));
        
        return R.ok().put("data", result);
    }
    
    /**
     * 产品分类统计
     */
    @RequestMapping("/products/category")
    public R productCategoryStatistics(@RequestParam Map<String, Object> params, HttpServletRequest request) {
        String role = String.valueOf(request.getSession().getAttribute("role"));
        Integer userId = (Integer) request.getSession().getAttribute("userId");
        
        EntityWrapper<NongchanpinEntity> wrapper = new EntityWrapper<>();
        
        // 根据角色过滤数据
        if ("商家".equals(role)) {
            wrapper.eq("shangjia_id", userId);
        } else if (!"管理员".equals(role)) {
            return R.error("权限不足");
        }
        
        // 这里简化处理，实际应该根据产品分类字段进行统计
        List<NongchanpinEntity> products = nongchanpinService.selectList(wrapper);
        
        Map<String, Object> result = new HashMap<>();
        result.put("totalProducts", products.size());
        result.put("categories", products); // 简化返回，实际应该按分类统计
        
        return R.ok().put("data", result);
    }
}
