package com.controller;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.annotation.IgnoreAuth;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.entity.UsersEntity;
import com.entity.YonghuEntity;
import com.entity.ShangjiaEntity;
import com.service.TokenService;
import com.service.UsersService;
import com.service.YonghuService;
import com.service.ShangjiaService;
import com.utils.R;

/**
 * 统一认证控制器
 * 处理用户、商家、管理员的登录认证
 */
@RequestMapping("/api/auth")
@RestController
public class AuthController {
    
    @Autowired
    private UsersService usersService;
    
    @Autowired
    private YonghuService yonghuService;
    
    @Autowired
    private ShangjiaService shangjiaService;
    
    @Autowired
    private TokenService tokenService;

    /**
     * 统一登录接口
     * @param loginRequest 登录请求
     * @param request HTTP请求
     * @return 登录结果
     */
    @IgnoreAuth
    @PostMapping("/login")
    public R login(@RequestBody LoginRequest loginRequest, HttpServletRequest request) {
        String userType = loginRequest.getUserType();
        String username = loginRequest.getUsername();
        String password = loginRequest.getPassword();
        
        if (username == null || password == null || userType == null) {
            return R.error("用户名、密码和用户类型不能为空");
        }
        
        try {
            switch (userType) {
                case "user":
                    return loginUser(username, password, request);
                case "merchant":
                    return loginMerchant(username, password, request);
                case "admin":
                    return loginAdmin(username, password, request);
                default:
                    return R.error("不支持的用户类型");
            }
        } catch (Exception e) {
            return R.error("登录失败：" + e.getMessage());
        }
    }
    
    /**
     * 用户登录
     */
    private R loginUser(String username, String password, HttpServletRequest request) {
        YonghuEntity user = yonghuService.selectOne(
            new EntityWrapper<YonghuEntity>().eq("username", username)
        );
        
        if (user == null || !user.getPassword().equals(password)) {
            return R.error("账号或密码不正确");
        }
        
        String token = tokenService.generateToken(user.getId(), username, "yonghu", "用户");
        
        Map<String, Object> result = new HashMap<>();
        result.put("token", token);
        result.put("userType", "user");
        result.put("userInfo", createUserInfo(user));
        result.put("redirectUrl", "/");
        result.put("permissions", getUserPermissions());
        
        return R.ok().put("data", result);
    }
    
    /**
     * 商家登录
     */
    private R loginMerchant(String username, String password, HttpServletRequest request) {
        ShangjiaEntity merchant = shangjiaService.selectOne(
            new EntityWrapper<ShangjiaEntity>().eq("username", username)
        );
        
        if (merchant == null || !merchant.getPassword().equals(password)) {
            return R.error("账号或密码不正确");
        }
        
        if (merchant.getShangjiaDelete() != 1) {
            return R.error("账户已被删除");
        }
        
        String token = tokenService.generateToken(merchant.getId(), username, "shangjia", "商家");
        
        Map<String, Object> result = new HashMap<>();
        result.put("token", token);
        result.put("userType", "merchant");
        result.put("userInfo", createMerchantInfo(merchant));
        result.put("redirectUrl", "/merchant");
        result.put("permissions", getMerchantPermissions());
        
        return R.ok().put("data", result);
    }
    
    /**
     * 管理员登录
     */
    private R loginAdmin(String username, String password, HttpServletRequest request) {
        UsersEntity admin = usersService.selectOne(
            new EntityWrapper<UsersEntity>().eq("username", username)
        );
        
        if (admin == null || !admin.getPassword().equals(password)) {
            return R.error("账号或密码不正确");
        }
        
        String token = tokenService.generateToken(admin.getId(), username, "users", admin.getRole());
        
        Map<String, Object> result = new HashMap<>();
        result.put("token", token);
        result.put("userType", "admin");
        result.put("userInfo", createAdminInfo(admin));
        result.put("redirectUrl", "/admin");
        result.put("permissions", getAdminPermissions());
        
        return R.ok().put("data", result);
    }
    
    /**
     * 创建用户信息
     */
    private Map<String, Object> createUserInfo(YonghuEntity user) {
        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("id", user.getId());
        userInfo.put("username", user.getYonghuName());
        userInfo.put("phone", user.getYonghuPhone());
        userInfo.put("email", user.getYonghuEmail());
        userInfo.put("avatar", user.getYonghuPhoto());
        return userInfo;
    }
    
    /**
     * 创建商家信息
     */
    private Map<String, Object> createMerchantInfo(ShangjiaEntity merchant) {
        Map<String, Object> merchantInfo = new HashMap<>();
        merchantInfo.put("id", merchant.getId());
        merchantInfo.put("username", merchant.getShangjiaName());
        merchantInfo.put("phone", merchant.getShangjiaPhone());
        merchantInfo.put("email", merchant.getShangjiaEmail());
        merchantInfo.put("avatar", merchant.getShangjiaPhoto());
        merchantInfo.put("shopName", merchant.getShangjiaName());
        return merchantInfo;
    }
    
    /**
     * 创建管理员信息
     */
    private Map<String, Object> createAdminInfo(UsersEntity admin) {
        Map<String, Object> adminInfo = new HashMap<>();
        adminInfo.put("id", admin.getId());
        adminInfo.put("username", admin.getUsername());
        adminInfo.put("role", admin.getRole());
        return adminInfo;
    }
    
    /**
     * 获取用户权限
     */
    private String[] getUserPermissions() {
        return new String[]{
            "product:view", "cart:manage", "order:create", "order:view", "profile:edit"
        };
    }
    
    /**
     * 获取商家权限
     */
    private String[] getMerchantPermissions() {
        return new String[]{
            "product:create", "product:edit", "product:delete", "product:view",
            "order:view", "order:process", "shop:manage", "statistics:view"
        };
    }
    
    /**
     * 获取管理员权限
     */
    private String[] getAdminPermissions() {
        return new String[]{
            "user:manage", "merchant:manage", "product:manage", "order:manage",
            "system:config", "statistics:view", "all:access"
        };
    }
    
    /**
     * 登录请求实体
     */
    public static class LoginRequest {
        private String username;
        private String password;
        private String userType;
        private String captcha;
        
        // Getters and Setters
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }
        
        public String getPassword() { return password; }
        public void setPassword(String password) { this.password = password; }
        
        public String getUserType() { return userType; }
        public void setUserType(String userType) { this.userType = userType; }
        
        public String getCaptcha() { return captcha; }
        public void setCaptcha(String captcha) { this.captcha = captcha; }
    }
}
