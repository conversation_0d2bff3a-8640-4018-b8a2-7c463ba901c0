import{s as ne,u as ae,r as p,x as N,o as se,b as o,c as _,d as f,f as e,w as t,e as c,l as v,h as s,y as g,t as b,E as x,z as le}from"./index-2c4e09fa.js";import{_ as re}from"./_plugin-vue_export-helper-c27b6911.js";const ue={class:"layout-container"},_e={class:"logo"},ce={key:0,class:"logo-icon"},de={key:1},me={class:"header-left"},ie={class:"header-right"},pe={class:"user-info"},fe={class:"username"},ve={__name:"Layout",setup(ge){const T={getToken(){return localStorage.getItem("merchant_token")},setToken(a){localStorage.setItem("merchant_token",a)},removeToken(){localStorage.removeItem("merchant_token")}},k={getUser(){const a=localStorage.getItem("merchant_user");return a?JSON.parse(a):null},setUser(a){localStorage.setItem("merchant_user",JSON.stringify(a))},removeUser(){localStorage.removeItem("merchant_user")}},w=ne(),y=ae(),r=p(!1),I=p(5),m=p({name:"商家用户",avatar:"",shopName:"我的店铺"}),U=N(()=>w.path),h=N(()=>w),D=()=>{r.value=!r.value},E=()=>{x.info("暂无新通知")},M=a=>{switch(a){case"profile":y.push("/profile");break;case"settings":x.info("系统设置功能开发中");break;case"logout":A();break}},A=()=>{le.confirm("确定要退出登录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{T.removeToken(),k.removeUser(),x.success("退出登录成功"),y.push("/login")}).catch(()=>{})};return se(()=>{const a=k.getUser();a&&(m.value={name:a.name||a.username||"商家用户",avatar:a.avatar||"",shopName:a.shopName||"我的店铺"})}),(a,n)=>{const F=o("DataAnalysis"),l=o("el-icon"),u=o("el-menu-item"),L=o("Goods"),V=o("el-sub-menu"),R=o("Document"),z=o("TrendCharts"),S=o("Setting"),G=o("el-menu"),J=o("el-aside"),O=o("Expand"),q=o("Fold"),B=o("el-button"),C=o("el-breadcrumb-item"),j=o("el-breadcrumb"),H=o("Bell"),K=o("el-badge"),P=o("el-avatar"),Q=o("ArrowDown"),W=o("User"),i=o("el-dropdown-item"),X=o("SwitchButton"),Y=o("el-dropdown-menu"),Z=o("el-dropdown"),$=o("el-header"),ee=o("router-view"),te=o("el-main"),oe=o("el-container");return _(),f("div",ue,[e(J,{width:r.value?"64px":"200px",class:"sidebar"},{default:t(()=>[c("div",_e,[r.value?v("",!0):(_(),f("span",ce,"🏪")),r.value?v("",!0):(_(),f("span",de,"商家后台"))]),e(G,{"default-active":U.value,collapse:r.value,"unique-opened":!0,"background-color":"#304156","text-color":"#bfcbd9","active-text-color":"#409EFF",router:""},{default:t(()=>[e(u,{index:"/dashboard"},{title:t(()=>n[0]||(n[0]=[s("数据概览")])),default:t(()=>[e(l,null,{default:t(()=>[e(F)]),_:1})]),_:1}),e(V,{index:"products"},{title:t(()=>[e(l,null,{default:t(()=>[e(L)]),_:1}),n[1]||(n[1]=c("span",null,"商品管理",-1))]),default:t(()=>[e(u,{index:"/products"},{default:t(()=>n[2]||(n[2]=[s("商品列表")])),_:1,__:[2]}),e(u,{index:"/products/add"},{default:t(()=>n[3]||(n[3]=[s("添加商品")])),_:1,__:[3]}),e(u,{index:"/inventory"},{default:t(()=>n[4]||(n[4]=[s("库存管理")])),_:1,__:[4]})]),_:1}),e(u,{index:"/orders"},{title:t(()=>n[5]||(n[5]=[s("订单管理")])),default:t(()=>[e(l,null,{default:t(()=>[e(R)]),_:1})]),_:1}),e(u,{index:"/analytics"},{title:t(()=>n[6]||(n[6]=[s("数据分析")])),default:t(()=>[e(l,null,{default:t(()=>[e(z)]),_:1})]),_:1}),e(u,{index:"/profile"},{title:t(()=>n[7]||(n[7]=[s("店铺设置")])),default:t(()=>[e(l,null,{default:t(()=>[e(S)]),_:1})]),_:1})]),_:1},8,["default-active","collapse"])]),_:1},8,["width"]),e(oe,{class:"main-container"},{default:t(()=>[e($,{class:"header"},{default:t(()=>[c("div",me,[e(B,{type:"text",onClick:D,class:"collapse-btn"},{default:t(()=>[e(l,null,{default:t(()=>[r.value?(_(),g(O,{key:0})):(_(),g(q,{key:1}))]),_:1})]),_:1}),e(j,{separator:"/"},{default:t(()=>{var d;return[e(C,{to:{path:"/"}},{default:t(()=>n[8]||(n[8]=[s("首页")])),_:1,__:[8]}),(d=h.value.meta)!=null&&d.title?(_(),g(C,{key:0},{default:t(()=>[s(b(h.value.meta.title),1)]),_:1})):v("",!0)]}),_:1})]),c("div",ie,[e(K,{value:I.value,class:"notification-badge"},{default:t(()=>[e(B,{type:"text",onClick:E},{default:t(()=>[e(l,null,{default:t(()=>[e(H)]),_:1})]),_:1})]),_:1},8,["value"]),e(Z,{onCommand:M},{dropdown:t(()=>[e(Y,null,{default:t(()=>[e(i,{command:"profile"},{default:t(()=>[e(l,null,{default:t(()=>[e(W)]),_:1}),n[9]||(n[9]=s(" 个人资料 "))]),_:1,__:[9]}),e(i,{command:"settings"},{default:t(()=>[e(l,null,{default:t(()=>[e(S)]),_:1}),n[10]||(n[10]=s(" 系统设置 "))]),_:1,__:[10]}),e(i,{divided:"",command:"logout"},{default:t(()=>[e(l,null,{default:t(()=>[e(X)]),_:1}),n[11]||(n[11]=s(" 退出登录 "))]),_:1,__:[11]})]),_:1})]),default:t(()=>[c("div",pe,[e(P,{size:32,src:m.value.avatar},{default:t(()=>{var d;return[s(b((d=m.value.name)==null?void 0:d.charAt(0)),1)]}),_:1},8,["src"]),c("span",fe,b(m.value.name),1),e(l,null,{default:t(()=>[e(Q)]),_:1})])]),_:1})])]),_:1}),e(te,{class:"main-content"},{default:t(()=>[e(ee)]),_:1})]),_:1})])}}},ke=re(ve,[["__scopeId","data-v-2a38cf7a"]]);export{ke as default};
