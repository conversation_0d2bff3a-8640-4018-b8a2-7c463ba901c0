<template>
  <div class="users-page">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>用户管理</span>
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增用户
          </el-button>
        </div>
      </template>
      
      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :model="searchForm" inline>
          <el-form-item label="用户名">
            <el-input v-model="searchForm.username" placeholder="请输入用户名" clearable />
          </el-form-item>
          <el-form-item label="手机号">
            <el-input v-model="searchForm.phone" placeholder="请输入手机号" clearable />
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
              <el-option label="启用" value="1" />
              <el-option label="禁用" value="0" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      
      <!-- 批量操作 -->
      <div class="batch-actions" v-if="selectedUsers.length > 0">
        <el-alert
          :title="`已选择 ${selectedUsers.length} 个用户`"
          type="info"
          show-icon
          :closable="false"
        >
          <template #default>
            <el-button type="success" size="small" @click="handleBatchEnable">批量启用</el-button>
            <el-button type="warning" size="small" @click="handleBatchDisable">批量禁用</el-button>
            <el-button type="danger" size="small" @click="handleBatchDelete">批量删除</el-button>
          </template>
        </el-alert>
      </div>
      
      <!-- 用户表格 -->
      <el-table
        :data="users"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="username" label="用户名" />
        <el-table-column prop="realName" label="真实姓名" />
        <el-table-column prop="phone" label="手机号" />
        <el-table-column prop="email" label="邮箱" />
        <el-table-column label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="注册时间" width="180" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button type="primary" size="small" @click="handleView(scope.row)">
              查看
            </el-button>
            <el-button type="warning" size="small" @click="handleEdit(scope.row)">
              编辑
            </el-button>
            <el-button
              :type="scope.row.status === 1 ? 'warning' : 'success'"
              size="small"
              @click="handleToggleStatus(scope.row)"
            >
              {{ scope.row.status === 1 ? '禁用' : '启用' }}
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 用户详情/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      @close="handleDialogClose"
    >
      <el-form
        ref="userFormRef"
        :model="userForm"
        :rules="userRules"
        label-width="100px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="userForm.username" :disabled="dialogMode === 'view'" />
        </el-form-item>
        <el-form-item label="真实姓名" prop="realName">
          <el-input v-model="userForm.realName" :disabled="dialogMode === 'view'" />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="userForm.phone" :disabled="dialogMode === 'view'" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="userForm.email" :disabled="dialogMode === 'view'" />
        </el-form-item>
        <el-form-item label="状态" prop="status" v-if="dialogMode !== 'add'">
          <el-radio-group v-model="userForm.status" :disabled="dialogMode === 'view'">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="密码" prop="password" v-if="dialogMode === 'add'">
          <el-input v-model="userForm.password" type="password" show-password />
        </el-form-item>
      </el-form>
      
      <template #footer v-if="dialogMode !== 'view'">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { createApiClient, adminApi } from '../../../shared/api.js'

// 响应式数据
const loading = ref(false)
const users = ref([])
const selectedUsers = ref([])
const dialogVisible = ref(false)
const dialogMode = ref('add') // add, edit, view
const dialogTitle = ref('新增用户')
const submitLoading = ref(false)
const userFormRef = ref(null)

// 搜索表单
const searchForm = reactive({
  username: '',
  phone: '',
  status: ''
})

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 用户表单
const userForm = reactive({
  id: null,
  username: '',
  realName: '',
  phone: '',
  email: '',
  status: 1,
  password: ''
})

// 表单验证规则
const userRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ]
}

// 模拟数据
const mockUsers = [
  {
    id: 1,
    username: 'user001',
    realName: '张三',
    phone: '13800138001',
    email: '<EMAIL>',
    status: 1,
    createTime: '2025-07-01 10:30:00'
  },
  {
    id: 2,
    username: 'user002',
    realName: '李四',
    phone: '13800138002',
    email: '<EMAIL>',
    status: 1,
    createTime: '2025-07-02 14:20:00'
  },
  {
    id: 3,
    username: 'user003',
    realName: '王五',
    phone: '13800138003',
    email: '<EMAIL>',
    status: 0,
    createTime: '2025-07-03 09:15:00'
  }
]

// 加载用户数据
const loadUsers = async () => {
  loading.value = true
  try {
    const apiClient = createApiClient()
    const params = {
      page: pagination.page,
      limit: pagination.size,
      ...searchForm
    }

    const response = await adminApi.users.list(apiClient, params)

    if (response.code === 0) {
      users.value = response.data.list || mockUsers
      pagination.total = response.data.total || mockUsers.length
    } else {
      users.value = mockUsers
      pagination.total = mockUsers.length
      ElMessage.warning('使用模拟数据：' + (response.msg || '无法连接到服务器'))
    }
  } catch (error) {
    console.error('加载用户数据失败:', error)
    users.value = mockUsers
    pagination.total = mockUsers.length
    ElMessage.warning('使用模拟数据：无法连接到服务器')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  loadUsers()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    username: '',
    phone: '',
    status: ''
  })
  handleSearch()
}

// 选择变化
const handleSelectionChange = (selection) => {
  selectedUsers.value = selection
}

// 新增用户
const handleAdd = () => {
  dialogMode.value = 'add'
  dialogTitle.value = '新增用户'
  resetUserForm()
  dialogVisible.value = true
}

// 查看用户
const handleView = (user) => {
  dialogMode.value = 'view'
  dialogTitle.value = '用户详情'
  Object.assign(userForm, user)
  dialogVisible.value = true
}

// 编辑用户
const handleEdit = (user) => {
  dialogMode.value = 'edit'
  dialogTitle.value = '编辑用户'
  Object.assign(userForm, user)
  dialogVisible.value = true
}

// 切换状态
const handleToggleStatus = async (user) => {
  try {
    await ElMessageBox.confirm(
      `确定要${user.status === 1 ? '禁用' : '启用'}用户 ${user.username} 吗？`,
      '提示',
      { type: 'warning' }
    )
    
    user.status = user.status === 1 ? 0 : 1
    ElMessage.success(`${user.status === 1 ? '启用' : '禁用'}成功`)
  } catch (error) {
    // 用户取消
  }
}

// 删除用户
const handleDelete = async (user) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 ${user.username} 吗？此操作不可恢复！`,
      '警告',
      { type: 'warning' }
    )
    
    const index = users.value.findIndex(u => u.id === user.id)
    if (index > -1) {
      users.value.splice(index, 1)
      pagination.total--
    }
    ElMessage.success('删除成功')
  } catch (error) {
    // 用户取消
  }
}

// 批量启用
const handleBatchEnable = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要启用选中的 ${selectedUsers.value.length} 个用户吗？`,
      '提示',
      { type: 'warning' }
    )
    
    selectedUsers.value.forEach(user => {
      user.status = 1
    })
    ElMessage.success('批量启用成功')
  } catch (error) {
    // 用户取消
  }
}

// 批量禁用
const handleBatchDisable = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要禁用选中的 ${selectedUsers.value.length} 个用户吗？`,
      '提示',
      { type: 'warning' }
    )
    
    selectedUsers.value.forEach(user => {
      user.status = 0
    })
    ElMessage.success('批量禁用成功')
  } catch (error) {
    // 用户取消
  }
}

// 批量删除
const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedUsers.value.length} 个用户吗？此操作不可恢复！`,
      '警告',
      { type: 'warning' }
    )
    
    const selectedIds = selectedUsers.value.map(user => user.id)
    users.value = users.value.filter(user => !selectedIds.includes(user.id))
    pagination.total -= selectedUsers.value.length
    selectedUsers.value = []
    ElMessage.success('批量删除成功')
  } catch (error) {
    // 用户取消
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    await userFormRef.value.validate()
    
    submitLoading.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    if (dialogMode.value === 'add') {
      const newUser = {
        ...userForm,
        id: Date.now(),
        createTime: new Date().toLocaleString()
      }
      users.value.unshift(newUser)
      pagination.total++
      ElMessage.success('新增用户成功')
    } else if (dialogMode.value === 'edit') {
      const index = users.value.findIndex(u => u.id === userForm.id)
      if (index > -1) {
        Object.assign(users.value[index], userForm)
      }
      ElMessage.success('编辑用户成功')
    }
    
    dialogVisible.value = false
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    submitLoading.value = false
  }
}

// 重置表单
const resetUserForm = () => {
  Object.assign(userForm, {
    id: null,
    username: '',
    realName: '',
    phone: '',
    email: '',
    status: 1,
    password: ''
  })
  userFormRef.value?.clearValidate()
}

// 关闭对话框
const handleDialogClose = () => {
  resetUserForm()
}

// 分页变化
const handleSizeChange = (size) => {
  pagination.size = size
  loadUsers()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  loadUsers()
}

// 初始化
onMounted(() => {
  loadUsers()
})
</script>

<style scoped>
.users-page {
  padding: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-area {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.batch-actions {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

:deep(.el-form--inline .el-form-item) {
  margin-right: 20px;
}
</style>
