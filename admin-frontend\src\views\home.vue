<template>
<div class="content">
  <div class="home-container">
    <div class="welcome-section">
      <div class="welcome-header">
        <svg-icon icon-class="home" class="welcome-icon"></svg-icon>
        <div class="text main-text">欢迎使用 {{this.$project.projectName}}</div>
      </div>
      <div class="welcome-card">
        <div class="welcome-info">
          <div class="welcome-user">
            <div class="welcome-title">您好，{{this.$storage.get('adminName') || '用户'}}</div>
            <div class="welcome-subtitle">{{currentTime}}</div>
          </div>
          <div class="welcome-stats">
            <div class="stat-item">
              <div class="stat-number">{{orderCount}}</div>
              <div class="stat-label">今日订单</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{productCount}}</div>
              <div class="stat-label">产品总数</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="chart-container" v-if="sessionTable=='users' && false">
      <div class="chart-item">
        <div class="chart-header">
          <div class="chart-title">销售统计</div>
        </div>
        <div id="statistic1" style="width:100%;height:400px;"></div>
      </div>
      <div class="chart-item">
        <div class="chart-header">
          <div class="chart-title">月度数据</div>
          <div class="chart-actions">
            <el-date-picker
                v-model="echartsDate"
                type="month"
                placeholder="选择月">
            </el-date-picker>
            <el-button type="primary" size="small" @click="chartDialog2()">查询</el-button>
          </div>
        </div>
        <div id="statistic2" style="width:100%;height:400px;"></div>
      </div>
    </div>
  </div>
</div>
</template>
<script>
import router from '@/router/router-static'
export default {
  data() {
    return {
      sessionTable : "",//登录账户所在表名
      role : "",//权限
      userId:"",//当前登录人的id

      echartsDate: new Date(),//echarts的时间查询字段
      currentTime: '',
      orderCount: 0,
      productCount: 0
    };
  },
  mounted(){
    //获取当前登录用户的信息
    this.sessionTable = this.$storage.get("sessionTable");
    this.role = this.$storage.get("role");
    this.userId = this.$storage.get("userId");

    this.init();
    this.chartDialog1();
    this.chartDialog2();
    this.getCurrentTime();
    this.getStatistics();
    
    // 每分钟更新一次时间
    setInterval(this.getCurrentTime, 60000);
  },
  methods:{
    getCurrentTime() {
      const now = new Date();
      const year = now.getFullYear();
      const month = now.getMonth() + 1;
      const date = now.getDate();
      const hours = now.getHours().toString().padStart(2, '0');
      const minutes = now.getMinutes().toString().padStart(2, '0');
      const day = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'][now.getDay()];
      this.currentTime = `${year}年${month}月${date}日 ${hours}:${minutes} ${day}`;
    },
    getStatistics() {
      // 这里只是模拟数据，实际项目中可以从后端获取
      this.orderCount = Math.floor(Math.random() * 100);
      this.productCount = Math.floor(Math.random() * 1000);
    },
    
    
    init(){
        if(this.$storage.get('Token')){
        this.$http({
            url: `${this.$storage.get('sessionTable')}/session`,
            method: "get"
        }).then(({ data }) => {
            if (data && data.code != 0) {
            router.push({ name: 'login' })
            }
        });
        }else{
            router.push({ name: 'login' })
        }
    }
  }
};
</script>

<style lang="scss" scoped>
.content {
  padding: 20px;
}

.home-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.welcome-section {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  padding: 30px;
  margin-bottom: 30px;
}

.welcome-header {
  display: flex;
  align-items: center;
  margin-bottom: 25px;
  
  .welcome-icon {
    font-size: 28px;
    color: #67a3ff;
    margin-right: 15px;
  }
  
  .main-text {
    font-size: 24px;
    font-weight: bold;
    color: #333;
  }
}

.welcome-card {
  background: linear-gradient(to right, #67a3ff, #94bcff);
  border-radius: 12px;
  color: white;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(103, 163, 255, 0.2);
}

.welcome-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-user {
  .welcome-title {
    font-size: 20px;
    font-weight: 500;
    margin-bottom: 8px;
  }
  
  .welcome-subtitle {
    opacity: 0.8;
  }
}

.welcome-stats {
  display: flex;
  gap: 40px;
  
  .stat-item {
    text-align: center;
    
    .stat-number {
      font-size: 28px;
      font-weight: bold;
      margin-bottom: 4px;
    }
    
    .stat-label {
      opacity: 0.8;
    }
  }
}

.chart-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 20px;
  
  .chart-item {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    padding: 20px;
  }
  
  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .chart-title {
      font-size: 16px;
      font-weight: bold;
      color: #333;
    }
    
    .chart-actions {
      display: flex;
      gap: 10px;
    }
  }
}
</style>