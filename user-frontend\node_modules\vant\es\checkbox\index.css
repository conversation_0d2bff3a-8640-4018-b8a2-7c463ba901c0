:root,:host{--van-checkbox-size: 20px;--van-checkbox-border-color: var(--van-gray-5);--van-checkbox-duration: var(--van-duration-fast);--van-checkbox-label-margin: var(--van-padding-xs);--van-checkbox-label-color: var(--van-text-color);--van-checkbox-checked-icon-color: var(--van-primary-color);--van-checkbox-disabled-icon-color: var(--van-gray-5);--van-checkbox-disabled-label-color: var(--van-text-color-3);--van-checkbox-disabled-background: var(--van-border-color)}.van-checkbox{display:flex;align-items:center;overflow:hidden;cursor:pointer;-webkit-user-select:none;user-select:none}.van-checkbox--disabled{cursor:not-allowed}.van-checkbox--label-disabled{cursor:default}.van-checkbox--horizontal{margin-right:var(--van-padding-sm)}.van-checkbox__icon{flex:none;height:1em;font-size:var(--van-checkbox-size);line-height:1em;cursor:pointer}.van-checkbox__icon .van-icon{display:block;box-sizing:border-box;width:1.25em;height:1.25em;color:transparent;font-size:.8em;line-height:1.25;text-align:center;border:1px solid var(--van-checkbox-border-color);transition-duration:var(--van-checkbox-duration);transition-property:color,border-color,background-color}.van-checkbox__icon--round .van-icon{border-radius:100%}.van-checkbox__icon--indeterminate .van-icon{display:flex;align-items:center;justify-content:center;color:var(--van-white);border-color:var(--van-checkbox-checked-icon-color);background-color:var(--van-checkbox-checked-icon-color)}.van-checkbox__icon--checked .van-icon{color:var(--van-white);background-color:var(--van-checkbox-checked-icon-color);border-color:var(--van-checkbox-checked-icon-color)}.van-checkbox__icon--disabled{cursor:not-allowed}.van-checkbox__icon--disabled .van-icon{background-color:var(--van-checkbox-disabled-background);border-color:var(--van-checkbox-disabled-icon-color)}.van-checkbox__icon--disabled.van-checkbox__icon--checked .van-icon{color:var(--van-checkbox-disabled-icon-color)}.van-checkbox__label{margin-left:var(--van-checkbox-label-margin);color:var(--van-checkbox-label-color);line-height:var(--van-checkbox-size)}.van-checkbox__label--left{margin:0 var(--van-checkbox-label-margin) 0 0}.van-checkbox__label--disabled{color:var(--van-checkbox-disabled-label-color)}
