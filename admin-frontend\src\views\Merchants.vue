<template>
  <div class="merchants-page">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>商家管理</span>
          <div>
            <el-badge :value="pendingCount" class="badge-item">
              <el-button type="warning" @click="showPendingOnly">
                <el-icon><Clock /></el-icon>
                待审核
              </el-button>
            </el-badge>
          </div>
        </div>
      </template>
      
      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :model="searchForm" inline>
          <el-form-item label="商家名称">
            <el-input v-model="searchForm.name" placeholder="请输入商家名称" clearable />
          </el-form-item>
          <el-form-item label="联系人">
            <el-input v-model="searchForm.contact" placeholder="请输入联系人" clearable />
          </el-form-item>
          <el-form-item label="审核状态">
            <el-select v-model="searchForm.auditStatus" placeholder="请选择审核状态" clearable>
              <el-option label="待审核" value="pending" />
              <el-option label="已通过" value="approved" />
              <el-option label="已拒绝" value="rejected" />
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
              <el-option label="启用" value="1" />
              <el-option label="禁用" value="0" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      
      <!-- 商家表格 -->
      <el-table
        :data="merchants"
        v-loading="loading"
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="商家名称" />
        <el-table-column prop="contact" label="联系人" />
        <el-table-column prop="phone" label="联系电话" />
        <el-table-column label="审核状态" width="120">
          <template #default="scope">
            <el-tag :type="getAuditStatusType(scope.row.auditStatus)">
              {{ getAuditStatusText(scope.row.auditStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="申请时间" width="180" />
        <el-table-column label="操作" width="300" fixed="right">
          <template #default="scope">
            <el-button type="primary" size="small" @click="handleView(scope.row)">
              查看详情
            </el-button>
            
            <template v-if="scope.row.auditStatus === 'pending'">
              <el-button type="success" size="small" @click="handleApprove(scope.row)">
                通过
              </el-button>
              <el-button type="danger" size="small" @click="handleReject(scope.row)">
                拒绝
              </el-button>
            </template>
            
            <template v-else-if="scope.row.auditStatus === 'approved'">
              <el-button
                :type="scope.row.status === 1 ? 'warning' : 'success'"
                size="small"
                @click="handleToggleStatus(scope.row)"
              >
                {{ scope.row.status === 1 ? '禁用' : '启用' }}
              </el-button>
            </template>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 商家详情对话框 -->
    <el-dialog
      v-model="detailVisible"
      title="商家详情"
      width="800px"
    >
      <div class="merchant-detail" v-if="currentMerchant">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="商家名称">
            {{ currentMerchant.name }}
          </el-descriptions-item>
          <el-descriptions-item label="联系人">
            {{ currentMerchant.contact }}
          </el-descriptions-item>
          <el-descriptions-item label="联系电话">
            {{ currentMerchant.phone }}
          </el-descriptions-item>
          <el-descriptions-item label="邮箱">
            {{ currentMerchant.email }}
          </el-descriptions-item>
          <el-descriptions-item label="地址" :span="2">
            {{ currentMerchant.address }}
          </el-descriptions-item>
          <el-descriptions-item label="营业执照号">
            {{ currentMerchant.licenseNumber }}
          </el-descriptions-item>
          <el-descriptions-item label="审核状态">
            <el-tag :type="getAuditStatusType(currentMerchant.auditStatus)">
              {{ getAuditStatusText(currentMerchant.auditStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="申请时间">
            {{ currentMerchant.createTime }}
          </el-descriptions-item>
          <el-descriptions-item label="审核时间" v-if="currentMerchant.auditTime">
            {{ currentMerchant.auditTime }}
          </el-descriptions-item>
          <el-descriptions-item label="商家简介" :span="2">
            {{ currentMerchant.description || '暂无简介' }}
          </el-descriptions-item>
        </el-descriptions>
        
        <!-- 营业执照图片 -->
        <div class="license-images" v-if="currentMerchant.licenseImages">
          <h4>营业执照</h4>
          <el-image
            v-for="(image, index) in currentMerchant.licenseImages"
            :key="index"
            :src="image"
            :preview-src-list="currentMerchant.licenseImages"
            class="license-image"
            fit="cover"
          />
        </div>
        
        <!-- 审核操作 -->
        <div class="audit-actions" v-if="currentMerchant.auditStatus === 'pending'">
          <el-divider>审核操作</el-divider>
          <el-form :model="auditForm" label-width="100px">
            <el-form-item label="审核意见">
              <el-input
                v-model="auditForm.remark"
                type="textarea"
                :rows="3"
                placeholder="请输入审核意见（可选）"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="success" @click="handleApprove(currentMerchant)">
                通过审核
              </el-button>
              <el-button type="danger" @click="handleReject(currentMerchant)">
                拒绝审核
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { createApiClient, adminApi } from '../../../shared/api.js'

// 响应式数据
const loading = ref(false)
const merchants = ref([])
const detailVisible = ref(false)
const currentMerchant = ref(null)

// 搜索表单
const searchForm = reactive({
  name: '',
  contact: '',
  auditStatus: '',
  status: ''
})

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 审核表单
const auditForm = reactive({
  remark: ''
})

// 待审核数量
const pendingCount = computed(() => {
  return merchants.value.filter(m => m.auditStatus === 'pending').length
})

// 模拟数据
const mockMerchants = [
  {
    id: 1,
    name: '绿色农场',
    contact: '张三',
    phone: '13800138001',
    email: '<EMAIL>',
    address: '山东省济南市历城区农业园区1号',
    licenseNumber: '91370100MA3K1234567',
    auditStatus: 'pending',
    status: 1,
    createTime: '2025-07-07 10:30:00',
    description: '专业从事有机蔬菜种植，产品绿色健康',
    licenseImages: [
      'https://via.placeholder.com/300x200?text=营业执照正面',
      'https://via.placeholder.com/300x200?text=营业执照反面'
    ]
  },
  {
    id: 2,
    name: '有机蔬菜园',
    contact: '李四',
    phone: '13800138002',
    email: '<EMAIL>',
    address: '河北省石家庄市藁城区有机农场2号',
    licenseNumber: '91130100MA3K2345678',
    auditStatus: 'approved',
    status: 1,
    createTime: '2025-07-06 14:20:00',
    auditTime: '2025-07-06 16:30:00',
    description: '致力于有机蔬菜种植，无农药无化肥',
    licenseImages: [
      'https://via.placeholder.com/300x200?text=营业执照正面'
    ]
  },
  {
    id: 3,
    name: '山区果园',
    contact: '王五',
    phone: '13800138003',
    email: '<EMAIL>',
    address: '陕西省西安市长安区山区果园3号',
    licenseNumber: '91610100MA3K3456789',
    auditStatus: 'rejected',
    status: 0,
    createTime: '2025-07-05 09:15:00',
    auditTime: '2025-07-05 11:20:00',
    description: '山区水果种植，主营苹果、梨等',
    licenseImages: []
  }
]

// 获取审核状态类型
const getAuditStatusType = (status) => {
  const types = {
    pending: 'warning',
    approved: 'success',
    rejected: 'danger'
  }
  return types[status] || 'info'
}

// 获取审核状态文本
const getAuditStatusText = (status) => {
  const texts = {
    pending: '待审核',
    approved: '已通过',
    rejected: '已拒绝'
  }
  return texts[status] || '未知'
}

// 加载商家数据
const loadMerchants = async () => {
  loading.value = true
  try {
    const apiClient = createApiClient()
    const params = {
      page: pagination.page,
      limit: pagination.size,
      ...searchForm
    }

    const response = await adminApi.merchants.list(apiClient, params)

    if (response.code === 0) {
      merchants.value = response.data.list || mockMerchants
      pagination.total = response.data.total || mockMerchants.length
    } else {
      merchants.value = mockMerchants
      pagination.total = mockMerchants.length
      ElMessage.warning('使用模拟数据：' + (response.msg || '无法连接到服务器'))
    }
  } catch (error) {
    console.error('加载商家数据失败:', error)
    merchants.value = mockMerchants
    pagination.total = mockMerchants.length
    ElMessage.warning('使用模拟数据：无法连接到服务器')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  loadMerchants()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    name: '',
    contact: '',
    auditStatus: '',
    status: ''
  })
  handleSearch()
}

// 只显示待审核
const showPendingOnly = () => {
  searchForm.auditStatus = 'pending'
  handleSearch()
}

// 查看详情
const handleView = (merchant) => {
  currentMerchant.value = merchant
  detailVisible.value = true
}

// 审核通过
const handleApprove = async (merchant) => {
  try {
    await ElMessageBox.confirm(
      `确定要通过商家 ${merchant.name} 的审核吗？`,
      '确认审核',
      { type: 'success' }
    )
    
    merchant.auditStatus = 'approved'
    merchant.auditTime = new Date().toLocaleString()
    merchant.status = 1
    
    ElMessage.success('审核通过成功')
    detailVisible.value = false
  } catch (error) {
    // 用户取消
  }
}

// 审核拒绝
const handleReject = async (merchant) => {
  try {
    await ElMessageBox.confirm(
      `确定要拒绝商家 ${merchant.name} 的审核吗？`,
      '确认审核',
      { type: 'warning' }
    )
    
    merchant.auditStatus = 'rejected'
    merchant.auditTime = new Date().toLocaleString()
    merchant.status = 0
    
    ElMessage.success('审核拒绝成功')
    detailVisible.value = false
  } catch (error) {
    // 用户取消
  }
}

// 切换状态
const handleToggleStatus = async (merchant) => {
  try {
    await ElMessageBox.confirm(
      `确定要${merchant.status === 1 ? '禁用' : '启用'}商家 ${merchant.name} 吗？`,
      '提示',
      { type: 'warning' }
    )
    
    merchant.status = merchant.status === 1 ? 0 : 1
    ElMessage.success(`${merchant.status === 1 ? '启用' : '禁用'}成功`)
  } catch (error) {
    // 用户取消
  }
}

// 分页变化
const handleSizeChange = (size) => {
  pagination.size = size
  loadMerchants()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  loadMerchants()
}

// 初始化
onMounted(() => {
  loadMerchants()
})
</script>

<style scoped>
.merchants-page {
  padding: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.badge-item {
  margin-right: 10px;
}

.search-area {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.merchant-detail {
  max-height: 600px;
  overflow-y: auto;
}

.license-images {
  margin-top: 20px;
}

.license-images h4 {
  margin-bottom: 10px;
  color: #303133;
}

.license-image {
  width: 150px;
  height: 100px;
  margin-right: 10px;
  margin-bottom: 10px;
  border-radius: 4px;
}

.audit-actions {
  margin-top: 20px;
}

:deep(.el-form--inline .el-form-item) {
  margin-right: 20px;
}

:deep(.el-descriptions__label) {
  font-weight: 600;
}
</style>
