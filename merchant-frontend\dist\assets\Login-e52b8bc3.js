import{u as U,r as k,a as V,o as T,b as u,c as C,d as N,e as n,f as a,w as i,g as L,h as b,E as p,i as P,t as R}from"./index-2c4e09fa.js";import{_ as E}from"./_plugin-vue_export-helper-c27b6911.js";const j={class:"login-container"},A={class:"login-box"},M={__name:"Login",setup(O){const y=()=>{const r="http://localhost:8081/nongchanpinxiaoshou";return{post:async(e,o)=>{try{console.log("API调用:",r+e,o);try{const t=new URLSearchParams;Object.keys(o).forEach(g=>{t.append(g,o[g])});const l=await fetch(r+e,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:t});if(!l.ok)throw new Error(`HTTP error! status: ${l.status}`);const c=await l.json();return console.log("API响应:",c),c}catch(t){if(console.warn("后端服务未启动，使用模拟数据:",t),e.includes("/shangjia/login")){const l={a2:{id:2,name:"商家名称2",phone:"17703786902"},a3:{id:3,name:"商家名称3",phone:"17703786903"}};if(l[o.username]&&o.password==="123456"){const c=l[o.username];return{code:0,token:"mock-token-"+Date.now(),role:"商家",username:c.name,userId:c.id,tableName:"shangjia"}}else return{code:1,msg:"账号或密码不正确"}}throw t}}catch(t){throw console.error("API调用失败:",t),t}}}},w={getToken(){return localStorage.getItem("merchant_token")},setToken(r){localStorage.setItem("merchant_token",r)},removeToken(){localStorage.removeItem("merchant_token")}},v={getUser(){const r=localStorage.getItem("merchant_user");return r?JSON.parse(r):null},setUser(r){localStorage.setItem("merchant_user",JSON.stringify(r))},removeUser(){localStorage.removeItem("merchant_user")}},f=U(),_=k(),d=k(!1),s=V({username:"",password:"",remember:!1}),S={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"用户名长度在 3 到 20 个字符",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,max:20,message:"密码长度在 6 到 20 个字符",trigger:"blur"}]},h=async()=>{if(_.value)try{if(!await _.value.validate())return;d.value=!0;const o=await y().post("/shangjia/login",{username:s.username,password:s.password});console.log("登录响应:",o),o.code===0?(w.setToken(o.token),v.setUser({id:o.userId,username:o.username,role:o.role,tableName:o.tableName}),s.remember?localStorage.setItem("rememberedUsername",s.username):localStorage.removeItem("rememberedUsername"),p.success("登录成功"),f.push("/")):p.error(o.msg||"登录失败")}catch(r){console.error("登录错误:",r),p.error("登录失败，请检查网络连接")}finally{d.value=!1}},x=()=>{f.push("/register")},I=()=>{p.info("忘记密码功能开发中")};return T(()=>{const r=w.getToken(),e=v.getUser();if(r&&(e==null?void 0:e.role)==="merchant"){f.push("/");return}const o=localStorage.getItem("rememberedUsername");o&&(s.username=o,s.remember=!0)}),(r,e)=>{const o=u("el-input"),t=u("el-form-item"),l=u("el-checkbox"),c=u("el-button"),g=u("el-form");return C(),N("div",j,[n("div",A,[e[5]||(e[5]=n("div",{class:"login-header"},[n("h2",null,"商家管理后台"),n("p",null,"农产品销售系统")],-1)),a(g,{ref_key:"loginFormRef",ref:_,model:s,rules:S,class:"login-form",onSubmit:L(h,["prevent"])},{default:i(()=>[a(t,{prop:"username"},{default:i(()=>[a(o,{modelValue:s.username,"onUpdate:modelValue":e[0]||(e[0]=m=>s.username=m),placeholder:"请输入用户名（可用测试账号：a2, a3）",size:"large","prefix-icon":"User",clearable:""},null,8,["modelValue"])]),_:1}),a(t,{prop:"password"},{default:i(()=>[a(o,{modelValue:s.password,"onUpdate:modelValue":e[1]||(e[1]=m=>s.password=m),type:"password",placeholder:"请输入密码（测试密码：123456）",size:"large","prefix-icon":"Lock","show-password":"",clearable:"",onKeyup:P(h,["enter"])},null,8,["modelValue"])]),_:1}),a(t,null,{default:i(()=>[a(l,{modelValue:s.remember,"onUpdate:modelValue":e[2]||(e[2]=m=>s.remember=m)},{default:i(()=>e[3]||(e[3]=[b(" 记住密码 ")])),_:1,__:[3]},8,["modelValue"])]),_:1}),a(t,null,{default:i(()=>[a(c,{type:"primary",size:"large",loading:d.value,onClick:h,class:"login-btn"},{default:i(()=>[b(R(d.value?"登录中...":"登录"),1)]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"]),n("div",{class:"login-footer"},[n("p",null,[e[4]||(e[4]=b("还没有账号？")),n("a",{href:"#",onClick:x},"立即注册")]),n("p",null,[n("a",{href:"#",onClick:I},"忘记密码？")])])]),e[6]||(e[6]=n("div",{class:"bg-decoration"},[n("div",{class:"circle circle-1"}),n("div",{class:"circle circle-2"}),n("div",{class:"circle circle-3"})],-1))])}}},D=E(M,[["__scopeId","data-v-90c7bd30"]]);export{D as default};
