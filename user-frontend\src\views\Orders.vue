<template>
  <div class="orders-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>我的订单</h1>
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item>我的订单</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 订单筛选 -->
    <div class="order-filters">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <el-tab-pane label="全部订单" name="all" />
        <el-tab-pane label="待付款" name="pending" />
        <el-tab-pane label="待发货" name="paid" />
        <el-tab-pane label="待收货" name="shipped" />
        <el-tab-pane label="已完成" name="completed" />
        <el-tab-pane label="已取消" name="cancelled" />
      </el-tabs>
    </div>

    <!-- 订单列表 -->
    <div class="orders-content" v-loading="loading">
      <div v-if="orders.length === 0 && !loading" class="empty-orders">
        <el-empty description="暂无订单">
          <el-button type="primary" @click="router.push('/products')">
            去购物
          </el-button>
        </el-empty>
      </div>

      <div v-else class="orders-list">
        <div v-for="order in orders" :key="order.id" class="order-card">
          <!-- 订单头部 -->
          <div class="order-header">
            <div class="order-info">
              <span class="order-number">订单号：{{ order.orderid }}</span>
              <span class="order-date">{{ formatDate(order.addtime) }}</span>
            </div>
            <div class="order-status">
              <el-tag :type="getStatusType(order.status)">
                {{ getStatusText(order.status) }}
              </el-tag>
            </div>
          </div>

          <!-- 订单商品 -->
          <div class="order-items">
            <div
              v-for="item in order.items"
              :key="item.id"
              class="order-item"
              @click="goToProduct(item.nongchanpinId)"
            >
              <div class="item-image">
                <img :src="getImageUrl(item.nongchanpinPhoto)" :alt="item.nongchanpinName">
              </div>
              <div class="item-info">
                <h4 class="item-name">{{ item.nongchanpinName }}</h4>
                <p class="item-desc">{{ item.nongchanpinContent }}</p>
                <div class="item-meta">
                  <span class="item-price">¥{{ item.nongchanpinNewMoney }}</span>
                  <span class="item-quantity">x{{ item.buynumber }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 订单总价 -->
          <div class="order-total">
            <span class="total-label">订单总额：</span>
            <span class="total-price">¥{{ order.total }}</span>
          </div>

          <!-- 订单操作 -->
          <div class="order-actions">
            <el-button
              v-if="order.status === 'pending'"
              type="primary"
              size="small"
              @click="payOrder(order.id)"
            >
              立即付款
            </el-button>
            <el-button
              v-if="order.status === 'shipped'"
              type="success"
              size="small"
              @click="confirmReceive(order.id)"
            >
              确认收货
            </el-button>
            <el-button
              v-if="['pending', 'paid'].includes(order.status)"
              type="danger"
              size="small"
              @click="cancelOrder(order.id)"
            >
              取消订单
            </el-button>
            <el-button
              size="small"
              @click="viewOrderDetail(order.id)"
            >
              查看详情
            </el-button>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="pagination.total > 0" class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.limit"
          :page-sizes="[10, 20, 30, 50]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { createApiClient, userApi } from '../../shared/api.js'
import { getImageUrl, formatDate, orderStatusMap } from '../../shared/utils.js'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const orders = ref([])
const activeTab = ref('all')

// 分页信息
const pagination = reactive({
  page: 1,
  limit: 10,
  total: 0
})

// 获取订单状态类型
const getStatusType = (status) => {
  const typeMap = {
    pending: 'warning',
    paid: 'info',
    shipped: 'primary',
    completed: 'success',
    cancelled: 'danger'
  }
  return typeMap[status] || 'info'
}

// 获取订单状态文本
const getStatusText = (status) => {
  return orderStatusMap[status] || '未知状态'
}

// 加载订单列表
const loadOrders = async () => {
  try {
    loading.value = true
    const apiClient = createApiClient()
    
    const params = {
      page: pagination.page,
      limit: pagination.limit
    }
    
    // 添加状态筛选
    if (activeTab.value !== 'all') {
      params.status = activeTab.value
    }
    
    const response = await userApi.orders.list(apiClient, params)
    
    if (response.code === 0) {
      orders.value = response.data.list || []
      pagination.total = response.data.total || 0
    } else {
      ElMessage.error(response.msg || '加载订单失败')
    }
  } catch (error) {
    console.error('加载订单失败:', error)
    ElMessage.error('加载订单失败，请重试')
  } finally {
    loading.value = false
  }
}

// 处理标签页变化
const handleTabChange = (tabName) => {
  activeTab.value = tabName
  pagination.page = 1
  loadOrders()
}

// 处理分页变化
const handlePageChange = (page) => {
  pagination.page = page
  loadOrders()
}

// 处理每页数量变化
const handleSizeChange = (size) => {
  pagination.limit = size
  pagination.page = 1
  loadOrders()
}

// 支付订单
const payOrder = async (orderId) => {
  try {
    await ElMessageBox.confirm('确定要支付这个订单吗？', '提示', {
      confirmButtonText: '确定支付',
      cancelButtonText: '取消',
      type: 'info'
    })
    
    const apiClient = createApiClient()
    const response = await userApi.orders.pay(apiClient, orderId)
    
    if (response.code === 0) {
      ElMessage.success('支付成功')
      loadOrders()
    } else {
      ElMessage.error(response.msg || '支付失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('支付失败:', error)
      ElMessage.error('支付失败，请重试')
    }
  }
}

// 确认收货
const confirmReceive = async (orderId) => {
  try {
    await ElMessageBox.confirm('确定已收到商品吗？', '提示', {
      confirmButtonText: '确认收货',
      cancelButtonText: '取消',
      type: 'info'
    })
    
    const apiClient = createApiClient()
    const response = await userApi.orders.receive(apiClient, orderId)
    
    if (response.code === 0) {
      ElMessage.success('确认收货成功')
      loadOrders()
    } else {
      ElMessage.error(response.msg || '操作失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('确认收货失败:', error)
      ElMessage.error('操作失败，请重试')
    }
  }
}

// 取消订单
const cancelOrder = async (orderId) => {
  try {
    await ElMessageBox.confirm('确定要取消这个订单吗？', '提示', {
      confirmButtonText: '确定取消',
      cancelButtonText: '不取消',
      type: 'warning'
    })
    
    const apiClient = createApiClient()
    const response = await userApi.orders.cancel(apiClient, orderId)
    
    if (response.code === 0) {
      ElMessage.success('订单已取消')
      loadOrders()
    } else {
      ElMessage.error(response.msg || '取消失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消订单失败:', error)
      ElMessage.error('取消失败，请重试')
    }
  }
}

// 查看订单详情
const viewOrderDetail = (orderId) => {
  router.push(`/order/${orderId}`)
}

// 跳转到商品详情
const goToProduct = (productId) => {
  router.push(`/product/${productId}`)
}

// 组件挂载时加载数据
onMounted(() => {
  loadOrders()
})
</script>

<style scoped>
.orders-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* 页面头部 */
.page-header {
  margin-bottom: 30px;
}

.page-header h1 {
  font-size: 28px;
  color: #333;
  margin-bottom: 10px;
}

/* 订单筛选 */
.order-filters {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  padding: 0 20px;
}

/* 订单内容 */
.orders-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.empty-orders {
  padding: 60px 20px;
  text-align: center;
}

.orders-list {
  padding: 20px;
}

/* 订单卡片 */
.order-card {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  margin-bottom: 20px;
  overflow: hidden;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #f0f0f0;
}

.order-info {
  display: flex;
  gap: 20px;
  align-items: center;
}

.order-number {
  font-weight: 500;
  color: #333;
}

.order-date {
  color: #666;
  font-size: 14px;
}

/* 订单商品 */
.order-items {
  padding: 20px;
}

.order-item {
  display: flex;
  gap: 15px;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.order-item:hover {
  background-color: #f8f9fa;
}

.order-item:last-child {
  border-bottom: none;
}

.item-image {
  width: 80px;
  height: 80px;
  border-radius: 6px;
  overflow: hidden;
  flex-shrink: 0;
}

.item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.item-info {
  flex: 1;
}

.item-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.item-desc {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.item-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.item-price {
  font-size: 16px;
  font-weight: 600;
  color: #e74c3c;
}

.item-quantity {
  color: #666;
  font-size: 14px;
}

/* 订单总价 */
.order-total {
  padding: 15px 20px;
  text-align: right;
  border-top: 1px solid #f0f0f0;
  background: #f8f9fa;
}

.total-label {
  color: #666;
  margin-right: 10px;
}

.total-price {
  font-size: 18px;
  font-weight: 600;
  color: #e74c3c;
}

/* 订单操作 */
.order-actions {
  padding: 15px 20px;
  text-align: right;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 分页 */
.pagination-container {
  padding: 20px;
  display: flex;
  justify-content: center;
  border-top: 1px solid #f0f0f0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .order-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
  
  .order-info {
    flex-direction: column;
    gap: 5px;
    align-items: flex-start;
  }
  
  .order-item {
    flex-direction: column;
    text-align: center;
  }
  
  .item-image {
    align-self: center;
  }
  
  .item-meta {
    justify-content: center;
    gap: 20px;
  }
  
  .order-actions {
    flex-direction: column;
    align-items: stretch;
  }
}
</style>
