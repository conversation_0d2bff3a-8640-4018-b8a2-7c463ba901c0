<template>
  <div class="dashboard">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon sales">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="stats-info">
              <h3>{{ formatCurrency(todaySales) }}</h3>
              <p>今日销售额</p>
              <span class="stats-change positive">+12.5%</span>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon orders">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stats-info">
              <h3>{{ todayOrders }}</h3>
              <p>今日订单数</p>
              <span class="stats-change positive">+8.2%</span>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon products">
              <el-icon><Goods /></el-icon>
            </div>
            <div class="stats-info">
              <h3>{{ totalProducts }}</h3>
              <p>商品总数</p>
              <span class="stats-change neutral">0%</span>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon visitors">
              <el-icon><View /></el-icon>
            </div>
            <div class="stats-info">
              <h3>{{ todayVisitors }}</h3>
              <p>今日访客数</p>
              <span class="stats-change positive">+15.3%</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :span="16">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>销售趋势</span>
              <el-radio-group v-model="salesPeriod" size="small">
                <el-radio-button label="7days">近7天</el-radio-button>
                <el-radio-button label="30days">近30天</el-radio-button>
                <el-radio-button label="90days">近90天</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div class="chart-container">
            <div class="chart-placeholder">销售趋势图表 (开发中)</div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card class="chart-card">
          <template #header>
            <span>商品分类销售占比</span>
          </template>
          <div class="chart-container">
            <div class="chart-placeholder">分类销售占比图表 (开发中)</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快捷操作和最新订单 -->
    <el-row :gutter="20" class="bottom-row">
      <el-col :span="8">
        <el-card class="quick-actions-card">
          <template #header>
            <span>快捷操作</span>
          </template>
          <div class="quick-actions">
            <el-button type="primary" @click="goToAddProduct">
              <el-icon><Plus /></el-icon>
              添加商品
            </el-button>
            <el-button type="success" @click="goToOrders">
              <el-icon><Document /></el-icon>
              查看订单
            </el-button>
            <el-button type="warning" @click="goToInventory">
              <el-icon><Warning /></el-icon>
              库存预警
            </el-button>
            <el-button type="info" @click="goToAnalytics">
              <el-icon><DataAnalysis /></el-icon>
              数据分析
            </el-button>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="16">
        <el-card class="recent-orders-card">
          <template #header>
            <div class="card-header">
              <span>最新订单</span>
              <el-button type="text" @click="goToOrders">查看全部</el-button>
            </div>
          </template>
          <el-table :data="recentOrders" style="width: 100%">
            <el-table-column prop="orderNo" label="订单号" width="120" />
            <el-table-column prop="customerName" label="客户" width="100" />
            <el-table-column prop="amount" label="金额" width="100">
              <template #default="scope">
                {{ formatCurrency(scope.row.amount) }}
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="getStatusType(scope.row.status)">
                  {{ getStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="下单时间">
              <template #default="scope">
                {{ formatDate(scope.row.createTime) }}
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
// import VChart from 'vue-echarts'
// import { use } from 'echarts/core'
// import { CanvasRenderer } from 'echarts/renderers'
// import { LineChart, PieChart } from 'echarts/charts'
// import {
//   TitleComponent,
//   TooltipComponent,
//   LegendComponent,
//   GridComponent
// } from 'echarts/components'
import { createApiClient, merchantApi } from '../../../shared/api.js'
import { formatCurrency, formatDate } from '../../../shared/utils.js'

// 注册 ECharts 组件
// use([
//   CanvasRenderer,
//   LineChart,
//   PieChart,
//   TitleComponent,
//   TooltipComponent,
//   LegendComponent,
//   GridComponent
// ])

const router = useRouter()

// 响应式数据
const salesPeriod = ref('7days')
const todaySales = ref(12580.50)
const todayOrders = ref(45)
const totalProducts = ref(128)
const todayVisitors = ref(1250)

const recentOrders = ref([
  {
    orderNo: 'ORD001',
    customerName: '张三',
    amount: 158.50,
    status: 'pending',
    createTime: new Date()
  },
  {
    orderNo: 'ORD002',
    customerName: '李四',
    amount: 299.00,
    status: 'paid',
    createTime: new Date(Date.now() - 3600000)
  },
  {
    orderNo: 'ORD003',
    customerName: '王五',
    amount: 89.90,
    status: 'shipped',
    createTime: new Date(Date.now() - 7200000)
  }
])

// 销售趋势图表配置
const salesChartOption = computed(() => ({
  tooltip: {
    trigger: 'axis'
  },
  xAxis: {
    type: 'category',
    data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
  },
  yAxis: {
    type: 'value'
  },
  series: [{
    data: [1200, 1500, 1800, 1400, 2200, 2800, 2500],
    type: 'line',
    smooth: true,
    itemStyle: {
      color: '#409EFF'
    }
  }]
}))

// 分类销售占比图表配置
const categoryChartOption = computed(() => ({
  tooltip: {
    trigger: 'item'
  },
  series: [{
    type: 'pie',
    radius: '70%',
    data: [
      { value: 35, name: '蔬菜' },
      { value: 25, name: '水果' },
      { value: 20, name: '粮食' },
      { value: 15, name: '肉类' },
      { value: 5, name: '其他' }
    ],
    emphasis: {
      itemStyle: {
        shadowBlur: 10,
        shadowOffsetX: 0,
        shadowColor: 'rgba(0, 0, 0, 0.5)'
      }
    }
  }]
}))

// 方法
const getStatusType = (status) => {
  const statusMap = {
    pending: 'warning',
    paid: 'success',
    shipped: 'info',
    completed: 'success',
    cancelled: 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    pending: '待付款',
    paid: '已付款',
    shipped: '已发货',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || '未知'
}

const goToAddProduct = () => {
  router.push('/products/add')
}

const goToOrders = () => {
  router.push('/orders')
}

const goToInventory = () => {
  router.push('/inventory')
}

const goToAnalytics = () => {
  router.push('/analytics')
}

// 生命周期
onMounted(() => {
  // 加载数据
  loadDashboardData()
})

const loadDashboardData = async () => {
  try {
    const apiClient = createApiClient()

    // 加载仪表板统计数据
    const response = await merchantApi.dashboard.stats(apiClient)

    if (response.code === 0) {
      // 更新统计数据（这里使用模拟数据，因为后端可能还没有对应的统计接口）
      console.log('仪表板数据加载成功:', response.data)
    } else {
      console.log('使用模拟数据')
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    console.log('使用模拟数据')
  }
}
</script>

<style scoped>
.dashboard {
  padding: 0;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  height: 120px;
}

.stats-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stats-icon.sales {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.orders {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-icon.products {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-icon.visitors {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-info h3 {
  margin: 0 0 4px 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.stats-info p {
  margin: 0 0 4px 0;
  font-size: 14px;
  color: #666;
}

.stats-change {
  font-size: 12px;
  font-weight: 500;
}

.stats-change.positive {
  color: #67c23a;
}

.stats-change.negative {
  color: #f56c6c;
}

.stats-change.neutral {
  color: #909399;
}

.charts-row {
  margin-bottom: 20px;
}

.chart-card {
  height: 400px;
}

.chart-container {
  height: 300px;
}

.chart-placeholder {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  color: #999;
  font-size: 16px;
  border-radius: 4px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.bottom-row {
  margin-bottom: 20px;
}

.quick-actions-card {
  height: 300px;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.quick-actions .el-button {
  justify-content: flex-start;
  height: 44px;
}

.recent-orders-card {
  height: 300px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-row .el-col {
    margin-bottom: 16px;
  }
  
  .charts-row .el-col {
    margin-bottom: 16px;
  }
  
  .bottom-row .el-col {
    margin-bottom: 16px;
  }
}
</style>
