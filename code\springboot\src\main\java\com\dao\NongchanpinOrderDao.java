package com.dao;

import com.entity.NongchanpinOrderEntity;
import com.baomidou.mybatisplus.mapper.BaseMapper;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.plugins.pagination.Pagination;

import org.apache.ibatis.annotations.Param;
import com.entity.view.NongchanpinOrderView;

/**
 * 农产品订单 Dao 接口
 *
 * <AUTHOR>
public interface NongchanpinOrderDao extends BaseMapper<NongchanpinOrderEntity> {

   List<NongchanpinOrderView> selectListView(Pagination page,@Param("params")Map<String,Object> params);

}
