{"version": 3, "sources": ["content/default/content.css"], "names": [], "mappings": ";;;;;;AAMA,KACE,YAAa,aAAa,CAAE,kBAAkB,CAAE,UAAU,CAAE,MAAM,CAAE,MAAM,CAAE,MAAM,CAAE,SAAS,CAAE,WAAW,CAAE,gBAAgB,CAAE,WAC9H,YAAa,IACb,OAAQ,KAEV,MACE,gBAAiB,SAGnB,SADA,SAEE,OAAQ,IAAI,MAAM,KAClB,QAAS,MAEX,OACE,QAAS,MACT,OAAQ,KAAK,KAEf,kBACE,MAAO,KACP,QAAS,MACT,WAAY,OACZ,WAAY,OAEd,GACE,aAAc,KACd,aAAc,MACd,aAAc,IAAI,EAAE,EAAE,EAExB,KACE,iBAAkB,QAClB,cAAe,IACf,QAAS,MAAO,MAElB,4CACE,YAAa,IAAI,MAAM,KACvB,YAAa,OACb,aAAc,KAEhB,sCACE,aAAc,IAAI,MAAM,KACxB,aAAc,OACd,cAAe", "file": "content.min.css", "sourcesContent": ["/**\n * Copyright (c) Tiny Technologies, Inc. All rights reserved.\n * Licensed under the LGPL or a commercial license.\n * For LGPL see License.txt in the project root for license information.\n * For commercial licenses see https://www.tiny.cloud/\n */\nbody {\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;\n  line-height: 1.4;\n  margin: 1rem;\n}\ntable {\n  border-collapse: collapse;\n}\ntable th,\ntable td {\n  border: 1px solid #ccc;\n  padding: 0.4rem;\n}\nfigure {\n  display: table;\n  margin: 1rem auto;\n}\nfigure figcaption {\n  color: #999;\n  display: block;\n  margin-top: 0.25rem;\n  text-align: center;\n}\nhr {\n  border-color: #ccc;\n  border-style: solid;\n  border-width: 1px 0 0 0;\n}\ncode {\n  background-color: #e8e8e8;\n  border-radius: 3px;\n  padding: 0.1rem 0.2rem;\n}\n.mce-content-body:not([dir=rtl]) blockquote {\n  border-left: 2px solid #ccc;\n  margin-left: 1.5rem;\n  padding-left: 1rem;\n}\n.mce-content-body[dir=rtl] blockquote {\n  border-right: 2px solid #ccc;\n  margin-right: 1.5rem;\n  padding-right: 1rem;\n}\n"]}