<template>
  <div class="layout">
    <!-- 顶部导航栏 -->
    <header class="header">
      <div class="header-content">
        <div class="logo">
          <router-link to="/">
            <img src="/logo.png" alt="农产品销售平台" class="logo-img">
            <span class="logo-text">农产品销售平台</span>
          </router-link>
        </div>
        
        <!-- 搜索框 -->
        <div class="search-box">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索农产品..."
            class="search-input"
            @keyup.enter="handleSearch"
          >
            <template #append>
              <el-button icon="Search" @click="handleSearch" />
            </template>
          </el-input>
        </div>
        
        <!-- 用户操作区 -->
        <div class="user-actions">
          <router-link to="/cart" class="cart-link">
            <el-badge :value="cartCount" class="cart-badge">
              <el-icon size="24"><ShoppingCart /></el-icon>
            </el-badge>
            <span>购物车</span>
          </router-link>
          
          <div v-if="isLoggedIn" class="user-menu">
            <el-dropdown @command="handleUserCommand">
              <span class="user-info">
                <el-avatar :src="userInfo.avatar" size="small">
                  {{ userInfo.username?.charAt(0) }}
                </el-avatar>
                <span>{{ userInfo.username }}</span>
                <el-icon><ArrowDown /></el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">个人中心</el-dropdown-item>
                  <el-dropdown-item command="orders">我的订单</el-dropdown-item>
                  <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
          
          <div v-else class="auth-buttons">
            <router-link to="/login">
              <el-button type="primary" size="small">登录</el-button>
            </router-link>
            <router-link to="/register">
              <el-button size="small">注册</el-button>
            </router-link>
          </div>
        </div>
      </div>
    </header>
    
    <!-- 导航菜单 -->
    <nav class="nav-menu">
      <div class="nav-content">
        <el-menu
          :default-active="activeMenu"
          mode="horizontal"
          router
          class="main-menu"
        >
          <el-menu-item index="/">首页</el-menu-item>
          <el-menu-item index="/products">全部商品</el-menu-item>
          <el-sub-menu index="categories">
            <template #title>商品分类</template>
            <el-menu-item index="/products?category=蔬菜">蔬菜类</el-menu-item>
            <el-menu-item index="/products?category=水果">水果类</el-menu-item>
            <el-menu-item index="/products?category=粮食">粮食类</el-menu-item>
            <el-menu-item index="/products?category=其他">其他类</el-menu-item>
          </el-sub-menu>
          <el-menu-item index="/merchants">商家入驻</el-menu-item>
        </el-menu>
      </div>
    </nav>
    
    <!-- 主要内容区域 -->
    <main class="main-content">
      <router-view />
    </main>
    
    <!-- 底部 -->
    <footer class="footer">
      <div class="footer-content">
        <div class="footer-section">
          <h4>关于我们</h4>
          <p>专业的农产品销售平台，为您提供新鲜、优质的农产品</p>
        </div>
        <div class="footer-section">
          <h4>客户服务</h4>
          <ul>
            <li><a href="#">帮助中心</a></li>
            <li><a href="#">联系我们</a></li>
            <li><a href="#">售后服务</a></li>
          </ul>
        </div>
        <div class="footer-section">
          <h4>商家服务</h4>
          <ul>
            <li><a href="#">商家入驻</a></li>
            <li><a href="#">商家中心</a></li>
            <li><a href="#">合作洽谈</a></li>
          </ul>
        </div>
        <div class="footer-section">
          <h4>联系方式</h4>
          <p>客服热线：400-123-4567</p>
          <p>邮箱：<EMAIL></p>
        </div>
      </div>
      <div class="footer-bottom">
        <p>&copy; 2025 农产品销售平台. All rights reserved.</p>
      </div>
    </footer>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { userManager, tokenManager } from '../../../shared/utils.js'
import { useCartStore } from '../stores/cart.js'

const router = useRouter()
const route = useRoute()
const cartStore = useCartStore()

// 搜索关键词
const searchKeyword = ref('')

// 用户信息
const userInfo = computed(() => userManager.get() || {})
const isLoggedIn = computed(() => tokenManager.isValid() && userInfo.value.userType === 'user')

// 购物车数量
const cartCount = computed(() => cartStore.totalCount)

// 当前激活的菜单
const activeMenu = computed(() => route.path)

// 搜索处理
const handleSearch = () => {
  if (searchKeyword.value.trim()) {
    router.push({
      path: '/products',
      query: { search: searchKeyword.value.trim() }
    })
  }
}

// 用户菜单命令处理
const handleUserCommand = (command) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'orders':
      router.push('/orders')
      break
    case 'logout':
      handleLogout()
      break
  }
}

// 退出登录
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // 清除用户信息和token
    userManager.remove()
    tokenManager.remove()
    cartStore.clearCart()
    
    ElMessage.success('已退出登录')
    router.push('/')
  } catch {
    // 用户取消
  }
}

// 组件挂载时初始化购物车
onMounted(() => {
  if (isLoggedIn.value) {
    cartStore.loadCart()
  }
})
</script>

<style scoped>
.layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 头部样式 */
.header {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo {
  display: flex;
  align-items: center;
}

.logo a {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #333;
}

.logo-img {
  height: 40px;
  margin-right: 10px;
}

.logo-text {
  font-size: 20px;
  font-weight: 600;
  color: #2c5530;
}

.search-box {
  flex: 1;
  max-width: 500px;
  margin: 0 40px;
}

.search-input {
  width: 100%;
}

.user-actions {
  display: flex;
  align-items: center;
  gap: 20px;
}

.cart-link {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-decoration: none;
  color: #666;
  font-size: 12px;
}

.cart-link:hover {
  color: #409eff;
}

.cart-badge {
  margin-bottom: 4px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  color: #333;
}

.auth-buttons {
  display: flex;
  gap: 10px;
}

/* 导航菜单样式 */
.nav-menu {
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.nav-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.main-menu {
  border-bottom: none;
}

/* 主内容区域 */
.main-content {
  flex: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  width: 100%;
}

/* 底部样式 */
.footer {
  background: #2c3e50;
  color: white;
  margin-top: auto;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.footer-section h4 {
  margin-bottom: 15px;
  color: #ecf0f1;
}

.footer-section ul {
  list-style: none;
  padding: 0;
}

.footer-section ul li {
  margin-bottom: 8px;
}

.footer-section ul li a {
  color: #bdc3c7;
  text-decoration: none;
}

.footer-section ul li a:hover {
  color: white;
}

.footer-bottom {
  border-top: 1px solid #34495e;
  padding: 20px;
  text-align: center;
  color: #bdc3c7;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    padding: 0 15px;
    height: 60px;
  }
  
  .search-box {
    margin: 0 15px;
  }
  
  .logo-text {
    display: none;
  }
  
  .main-content {
    padding: 15px;
  }
  
  .footer-content {
    grid-template-columns: 1fr;
    padding: 30px 20px;
  }
}
</style>
