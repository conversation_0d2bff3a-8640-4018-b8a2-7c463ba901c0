<template>
    <div class="main-content">

        <!-- 条件查询 -->
        <div v-if="showFlag">
            <el-form :inline="true" :model="searchForm" class="form-content search-form">
                <el-row :gutter="20" class="slt">
                     <el-form-item :label="'农产品名称'">
                         <el-input prefix-icon="el-icon-search" v-model="searchForm.nongchanpinName" placeholder="农产品名称" clearable></el-input>
                     </el-form-item>
                 
                     <el-form-item :label="'农产品类型'">
                        <el-select v-model="searchForm.nongchanpinTypes" placeholder="请选择农产品类型">
                            <el-option label="=-请选择-=" value=""></el-option>
                            <el-option
                               v-for="(item,index) in nongchanpinTypesSelectSearch"
                               v-bind:key="index"
                               :label="item.indexName"
                               :value="item.codeIndex">
                            </el-option>
                        </el-select>
                     </el-form-item>
                         
                    <el-form-item :label="'商家名称'">
                        <el-input prefix-icon="el-icon-search" v-model="searchForm.shangjiaName" placeholder="商家名称" clearable></el-input>
                    </el-form-item>

                    <el-form-item>
                        <el-button type="primary" @click="search()">查询<i class="el-icon-search el-icon--right"/></el-button>
                    </el-form-item>
                </el-row>
                <el-row class="action-buttons">
                    <el-form-item>
                        <el-button
                                v-if="isAuth('nongchanpin','新增')"
                                type="success"
                                icon="el-icon-plus"
                                @click="addOrUpdateHandler()"
                        >新增</el-button>
                        
                        <el-button
                                v-if="isAuth('nongchanpin','删除')"
                                :disabled="dataListSelections.length <= 0"
                                type="danger"
                                icon="el-icon-delete"
                                @click="deleteHandler()"
                        >删除</el-button>
                        
                        <el-button
                                v-if="isAuth('nongchanpin','报表')"
                                type="warning"
                                icon="el-icon-pie-chart"
                                @click="chartDialog()"
                        >报表</el-button>
                        
                        <el-dropdown split-button type="primary" @click="handleExportImport" style="margin-left: 10px;">
                            导入导出
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item>
                                    <a style="text-decoration:none; color: inherit;"
                                       v-if="isAuth('nongchanpin','导入导出')"
                                       href="http://localhost:8080/nongchanpinxiaoshou/upload/nongchanpinMuBan.xls"
                                    >下载导入模板</a>
                                </el-dropdown-item>
                                <el-dropdown-item>
                                    <el-upload
                                            v-if="isAuth('nongchanpin','导入导出')"
                                            style="display: inline-block"
                                            :action="getActionUrl"
                                            :on-success="nongchanpinUploadSuccess"
                                            :on-error="nongchanpinUploadError"
                                            :show-file-list="false">
                                        <span>批量导入数据</span>
                                    </el-upload>
                                </el-dropdown-item>
                                <el-dropdown-item>
                                    <download-excel v-if="isAuth('nongchanpin','导入导出')" style="display: inline-block" class="export-excel-wrapper" :data="dataList" :fields="json_fields" name="nongchanpin.xls">
                                        <span>导出数据</span>
                                    </download-excel>
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>
                    </el-form-item>
                </el-row>
            </el-form>
            <div class="table-content">
                <el-table class="data-table"
                          :border="true"
                          :stripe="true"
                          :size="'medium'"
                          v-if="isAuth('nongchanpin','查看')"
                          :data="dataList"
                          v-loading="dataListLoading"
                          @selection-change="selectionChangeHandler">
                    <el-table-column type="selection"
                                     header-align="center"
                                     align="center"
                                     width="50">
                    </el-table-column>
                    <el-table-column label="序号" type="index" width="80" align="center" />
                    
                    <el-table-column prop="shangjiaName"
                                     header-align="center"
                                     align="center"
                                     label="商家名称">
                        <template slot-scope="scope">
                            {{scope.row.shangjiaName}}
                        </template>
                    </el-table-column>

                    <el-table-column prop="nongchanpinName"
                                     header-align="center"
                                     align="center"
                                     label="农产品名称">
                        <template slot-scope="scope">
                            {{scope.row.nongchanpinName}}
                        </template>
                    </el-table-column>
                    
                    <el-table-column prop="nongchanpinPhoto"
                                     header-align="center"
                                     align="center"
                                     width="120"
                                     label="产品图片">
                        <template slot-scope="scope">
                            <div v-if="scope.row.nongchanpinPhoto" class="product-image">
                                <img :src="formatImageUrl(scope.row.nongchanpinPhoto)">
                            </div>
                            <div v-else class="no-image">无图片</div>
                        </template>
                    </el-table-column>
                    
                    <el-table-column prop="nongchanpinTypes"
                                     header-align="center"
                                     align="center"
                                     label="产品类型">
                        <template slot-scope="scope">
                            <el-tag size="medium">{{scope.row.nongchanpinValue}}</el-tag>
                        </template>
                    </el-table-column>
                    
                    <el-table-column prop="nongchanpinKucunNumber"
                                     header-align="center"
                                     align="center"
                                     label="库存">
                        <template slot-scope="scope">
                            {{scope.row.nongchanpinKucunNumber}}
                        </template>
                    </el-table-column>

                    <el-table-column prop="nongchanpinOldMoney"
                                     header-align="center"
                                     align="center"
                                     label="原价">
                        <template slot-scope="scope">
                            <span class="old-price">¥{{scope.row.nongchanpinOldMoney}}</span>
                        </template>
                    </el-table-column>

                    <el-table-column prop="nongchanpinNewMoney"
                                     header-align="center"
                                     align="center"
                                     label="现价">
                        <template slot-scope="scope">
                            <span class="new-price">¥{{scope.row.nongchanpinNewMoney}}</span>
                        </template>
                    </el-table-column>
                    
                    <el-table-column prop="nongchanpinClicknum"
                                     header-align="center"
                                     align="center"
                                     label="点击次数">
                        <template slot-scope="scope">
                            {{scope.row.nongchanpinClicknum}}
                        </template>
                    </el-table-column>
                    
                    <el-table-column prop="shangxiaTypes"
                                     header-align="center"
                                     align="center"
                                     label="状态">
                        <template slot-scope="scope">
                            <el-tag :type="scope.row.shangxiaValue === '上架' ? 'primary' : 'info'">
                                {{scope.row.shangxiaValue}}
                            </el-tag>
                        </template>
                    </el-table-column>

                    <el-table-column width="200" 
                                     align="center"
                                     header-align="center"
                                     fixed="right"
                                     label="操作">
                        <template slot-scope="scope">
                            <el-button v-if="isAuth('nongchanpin','查看')" type="success" icon="el-icon-view" size="mini" @click="addOrUpdateHandler(scope.row.id,'info')">详情</el-button>
                            <el-button v-if="isAuth('nongchanpin','修改')" type="primary" icon="el-icon-edit" size="mini" @click="addOrUpdateHandler(scope.row.id)">修改</el-button>
                            <el-button v-if="isAuth('nongchanpin','删除')" type="danger" icon="el-icon-delete" size="mini" @click="deleteHandler(scope.row.id)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination
                        clsss="pages"
                        :layout="layouts"
                        @size-change="sizeChangeHandle"
                        @current-change="currentChangeHandle"
                        :current-page="pageIndex"
                        :page-sizes="[10, 20, 50, 100]"
                        :page-size="Number(contents.pageEachNum)"
                        :total="totalPage"
                        :small="contents.pageStyle"
                        class="pagination-content"
                        :background="contents.pageBtnBG"
                        :style="{textAlign:contents.pagePosition==1?'left':contents.pagePosition==2?'center':'right'}"
                ></el-pagination>
            </div>
        </div>
        <!-- 添加/修改页面  将父组件的search方法传递给子组件-->
        <add-or-update v-if="addOrUpdateFlag" :parent="this" ref="addOrUpdate"></add-or-update>

<!-- 库存 -->
		<el-dialog
                :title="nongchanpinKucunNumberTitle"
                :visible.sync="nongchanpinKucunNumberVisible"
                width="30%">
            <span>操作数量</span><el-input-number v-model="kucunNumber" :min="1" :max="100" label="描述文字"></el-input-number>
            <span slot="footer" class="dialog-footer">
			<el-button @click="nongchanpinKucunNumberVisible = false">取 消</el-button>
			<el-button type="primary" @click="addNongchanpinKucunNumber()">确 定</el-button>
		  </span>
        </el-dialog>
        <el-dialog title="打印" :visible.sync="printVisiable" width="500px">
            <el-form ref="printContent" :model="printFrom" label-width="auto">
                <el-form-item label="发件人">
                    <div  style="display: flex">
                        <el-input v-model="printFrom.kuaidiFajianName" disabled></el-input>
                        :
                        <el-input v-model="printFrom.kuaidiFajianShoujihao" disabled></el-input>
                    </div>
                </el-form-item>
                <el-form-item label="发件人地址">
                    <el-input v-model="printFrom.kuaidiFajianAddress" disabled></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
              <el-button @click="printContent()" icon="el-icon-printer" type="success">打印</el-button>
              <el-button @click="printVisiable = false">关闭</el-button>
            </span>
        </el-dialog>

        <el-dialog title="统计报表" :visible.sync="chartVisiable" width="80%">
            <el-date-picker v-model="echartsDate" type="month" placeholder="选择年月"> </el-date-picker>
            <el-button @click="chartDialog()">查询</el-button>
                <div id="statistic" style="width:100%;height:600px;"></div>

            <span slot="footer" class="dialog-footer">
				<el-button @click="chartVisiable = false">关闭</el-button>
			</span>
        </el-dialog>

    </div>
</template>
<script>
    import AddOrUpdate from "./add-or-update";
    import styleJs from "../../../utils/style.js";
    import utilsJs, {getYearFormat,getMonthFormat,getDateFormat,getDatetimeFormat} from "../../../utils/utils.js";

    export default {
        data() {
        return {
            //打印开始
            printFrom: {
                name1:"",
            },
            printVisiable: false,
            //打印结束
            searchForm: {
                key: ""
            },
            sessionTable : "",//登录账户所在表名
            role : "",//权限
            userId:"",//当前登录人的id
    //级联表下拉框搜索条件
    //当前表下拉框搜索条件
              nongchanpinTypesSelectSearch : [],
            form:{
                id : null,
                shangjiaId : null,
                nongchanpinName : null,
                nongchanpinPhoto : null,
                nongchanpinTypes : null,
                nongchanpinKucunNumber : null,
                nongchanpinOldMoney : null,
                nongchanpinNewMoney : null,
                nongchanpinClicknum : null,
                nongchanpinContent : null,
                shangxiaTypes : null,
                nongchanpinDelete : null,
                createTime : null,
            },
            dataList: [],
            pageIndex: 1,
            pageSize: 10,
            totalPage: 0,
            dataListLoading: false,
            dataListSelections: [],
            showFlag: true,
            sfshVisiable: false,
            shForm: {},
            chartVisiable: false,
            echartsDate: new Date(),//echarts的时间查询字段
            addOrUpdateFlag:false,
            contents:null,
            layouts: '',

            //导出excel
            json_fields: {
                //级联表字段
                     '商家名称': 'shangjiaName',
                     '联系方式': 'shangjiaPhone',
                     '邮箱': 'shangjiaEmail',
                     '营业执照展示': 'shangjiaPhoto',
                     '商家信用类型': 'shangjiaXingjiTypes',
                     '现有余额': 'newMoney',
                //本表字段
                     '农产品名称': "nongchanpinName",
                     '农产品照片': "nongchanpinPhoto",
                     '农产品类型': "nongchanpinValue",
                     '农产品库存': "nongchanpinKucunNumber",
                     '农产品原价': "nongchanpinOldMoney",
                     '现价': "nongchanpinNewMoney",
                     '点击次数': "nongchanpinClicknum",
                     '是否上架': "shangxiaValue",
            },

        //库存
            //操作数量
            kucunNumber: 1,
            //操作数据id
            kucunNumberId: null,
            //操作类型
            kucunNumberTypes: null,
            //原有数量
            nongchanpinKucunNumber: 0,
            //标题
            nongchanpinKucunNumberTitle: null,
            //模态框状态
            nongchanpinKucunNumberVisible: false,
            };
        },
        created() {
            this.contents = styleJs.listStyle();
            this.init();
            this.getDataList();
            this.contentStyleChange()
        },
        mounted() {
            //获取当前登录用户的信息
            this.sessionTable = this.$storage.get("sessionTable");
            this.role = this.$storage.get("role");
            this.userId = this.$storage.get("userId");

        },
        filters: {
            htmlfilter: function (val) {
                return val.replace(/<[^>]*>/g).replace(/undefined/g,'');
            }
        },
        components: {
            AddOrUpdate,
        },
        computed: {
            getActionUrl: function() {
                return `/`+this.$base.name+`/file/upload`;
            }
        },
        methods: {
            chartDialog() {
                let _this = this;
                let params = {
                    // dateFormat :"%Y-%m", //%Y-%m
                    // riqi :getYearFormat(_this.echartsDate),//年
                    // riqi :getMonthFormat(_this.echartsDate),//年月
                    thisTable : {//当前表
                        tableName :'nongchanpin',//当前表表名,
                        // sumColum : 'nongchanpin_number', //求和字段
                        // date : 'insert_time',//分组日期字段
                        // string : 'nongchanpin_name',//分组字符串字段
                        types : 'nongchanpin_types',//分组下拉框字段
                    },
                    // joinTable : {//级联表（可以不存在）
                    //     tableName :'yonghu',//级联表表名
                    //     // date : 'insert_time',//分组日期字段
                    //     string : 'yonghu_name',//分组字符串字段
                    //     // types : 'yonghu_types',//分组下拉框字段
                    // }
                }
                _this.chartVisiable = true;
                _this.$nextTick(() => {
                    var statistic = this.$echarts.init(document.getElementById("statistic"), 'macarons');
                    this.$http({
                        url: "barCount",//barCountOne barCountTwo barSumOne barSumTwo
                        method: "get",
                        params: params
                    }).then(({data}) => {
                        if(data && data.code === 0){
                            let yAxisName = "数值";//y轴
                            let xAxisName = "月份";//x轴
                            let series = [];//具体数据值
                            data.data.yAxis.forEach(function (item,index) {//点击可关闭的按钮字符串在后台方法中
                                let tempMap = {};
                                tempMap.name=data.data.legend[index];
                                tempMap.type='bar';
                                tempMap.data=item;
                                series.push(tempMap);
                            })

                            var option = {
                                tooltip: {
                                    trigger: 'axis',
                                    axisPointer: {
                                        type: 'cross',
                                        crossStyle: {
                                            color: '#999'
                                        }
                                    }
                                },
                                toolbox: {
                                    feature: {
                                        // dataView: { show: true, readOnly: false },  // 数据查看
                                        magicType: { show: true, type: ['line', 'bar'] },//切换图形展示方式
                                        // restore: { show: true }, // 刷新
                                        saveAsImage: { show: true }//保存
                                    }
                                },
                                legend: {
                                    data: data.data.legend//标题  可以点击导致某一列数据消失
                                },
                                xAxis: [
                                    {
                                        type: 'category',
                                        axisLabel:{interval: 0},
                                        name: xAxisName,
                                        data: data.data.xAxis,
                                        axisPointer: {
                                            type: 'shadow'
                                        }
                                    }
                                ],
                                yAxis: [
                                    {
                                        type: 'value',//不能改
                                        name: yAxisName,//y轴单位
                                        axisLabel: {
                                            formatter: '{value}' // 后缀
                                        }
                                    }
                                ],
                                series:series//具体数据
                            };
                            statistic.setOption(option,true);
                            window.onresize = function () {
                                statistic.resize();
                            };
                        }else {
                            this.$message({
                                message: "报表未查询到数据",
                                type: "success",
                                duration: 1500,
                                onClose: () => {
                                    this.search();
                                }
                            });
                        }
                    });
                });
                
            },
            contentStyleChange() {
                this.contentSearchStyleChange()
                this.contentBtnAdAllStyleChange()
                this.contentSearchBtnStyleChange()
                this.contentTableBtnStyleChange()
                this.contentPageStyleChange()
            },
            contentSearchStyleChange() {
                this.$nextTick(() => {
                    document.querySelectorAll('.form-content .slt .el-input__inner').forEach(el => {
                        let textAlign = 'left'
                        if(this.contents.inputFontPosition == 2)
                            textAlign = 'center'
                            if (this.contents.inputFontPosition == 3) textAlign = 'right'
                                el.style.textAlign = textAlign
                            el.style.height = this.contents.inputHeight
                            el.style.lineHeight = this.contents.inputHeight
                            el.style.color = this.contents.inputFontColor
                            el.style.fontSize = this.contents.inputFontSize
                            el.style.borderWidth = this.contents.inputBorderWidth
                            el.style.borderStyle = this.contents.inputBorderStyle
                            el.style.borderColor = this.contents.inputBorderColor
                            el.style.borderRadius = this.contents.inputBorderRadius
                            el.style.backgroundColor = this.contents.inputBgColor
                    })
                    if (this.contents.inputTitle) {
                        document.querySelectorAll('.form-content .slt .el-form-item__label').forEach(el => {
                            el.style.color = this.contents.inputTitleColor
                            el.style.fontSize = this.contents.inputTitleSize
                            el.style.lineHeight = this.contents.inputHeight
                        })
                    }
                    setTimeout(() => {
                        document.querySelectorAll('.form-content .slt .el-input__prefix').forEach(el => {
                            el.style.color = this.contents.inputIconColor
                            el.style.lineHeight = this.contents.inputHeight
                        })
                        document.querySelectorAll('.form-content .slt .el-input__suffix').forEach(el => {
                            el.style.color = this.contents.inputIconColor
                            el.style.lineHeight = this.contents.inputHeight
                        })
                        document.querySelectorAll('.form-content .slt .el-input__icon').forEach(el => {
                            el.style.lineHeight = this.contents.inputHeight
                        })
                    }, 10 )
                })
            },
            // 搜索按钮
            contentSearchBtnStyleChange() {
                this.$nextTick(() => {
                    document.querySelectorAll('.form-content .slt .el-button--success').forEach(el => {
                        el.style.height = this.contents.searchBtnHeight
                        el.style.color = this.contents.searchBtnFontColor
                        el.style.fontSize = this.contents.searchBtnFontSize
                        el.style.borderWidth = this.contents.searchBtnBorderWidth
                        el.style.borderStyle = this.contents.searchBtnBorderStyle
                        el.style.borderColor = this.contents.searchBtnBorderColor
                        el.style.borderRadius = this.contents.searchBtnBorderRadius
                        el.style.backgroundColor = this.contents.searchBtnBgColor
                    })
                })
            },
            // 新增、批量删除
            contentBtnAdAllStyleChange() {
                this.$nextTick(() => {
                    document.querySelectorAll('.form-content .ad .el-button--success').forEach(el => {
                        el.style.height = this.contents.btnAdAllHeight
                        el.style.color = this.contents.btnAdAllAddFontColor
                        el.style.fontSize = this.contents.btnAdAllFontSize
                        el.style.borderWidth = this.contents.btnAdAllBorderWidth
                        el.style.borderStyle = this.contents.btnAdAllBorderStyle
                        el.style.borderColor = this.contents.btnAdAllBorderColor
                        el.style.borderRadius = this.contents.btnAdAllBorderRadius
                        el.style.backgroundColor = this.contents.btnAdAllAddBgColor
                    })
                    document.querySelectorAll('.form-content .ad .el-button--danger').forEach(el => {
                        el.style.height = this.contents.btnAdAllHeight
                        el.style.color = this.contents.btnAdAllDelFontColor
                        el.style.fontSize = this.contents.btnAdAllFontSize
                        el.style.borderWidth = this.contents.btnAdAllBorderWidth
                        el.style.borderStyle = this.contents.btnAdAllBorderStyle
                        el.style.borderColor = this.contents.btnAdAllBorderColor
                        el.style.borderRadius = this.contents.btnAdAllBorderRadius
                        el.style.backgroundColor = this.contents.btnAdAllDelBgColor
                    })
                    document.querySelectorAll('.form-content .ad .el-button--warning').forEach(el => {
                        el.style.height = this.contents.btnAdAllHeight
                        el.style.color = this.contents.btnAdAllWarnFontColor
                        el.style.fontSize = this.contents.btnAdAllFontSize
                        el.style.borderWidth = this.contents.btnAdAllBorderWidth
                        el.style.borderStyle = this.contents.btnAdAllBorderStyle
                        el.style.borderColor = this.contents.btnAdAllBorderColor
                        el.style.borderRadius = this.contents.btnAdAllBorderRadius
                        el.style.backgroundColor = this.contents.btnAdAllWarnBgColor
                    })
                })
            },
            // 表格
            rowStyle({row, rowIndex}) {
                if (rowIndex % 2 == 1) {
                    if (this.contents.tableStripe) {
                        return {color: this.contents.tableStripeFontColor}
                    }
                } else {
                    return ''
                }
            },
            cellStyle({row, rowIndex}) {
                if (rowIndex % 2 == 1) {
                    if (this.contents.tableStripe) {
                        return {backgroundColor: this.contents.tableStripeBgColor}
                    }
                } else {
                    return ''
                }
            },
            headerRowStyle({row, rowIndex}) {
                return {color: this.contents.tableHeaderFontColor}
            },
            headerCellStyle({row, rowIndex}) {
                return {backgroundColor: this.contents.tableHeaderBgColor}
            },
            // 表格按钮
            contentTableBtnStyleChange() {
               
            },
            // 分页
            contentPageStyleChange() {
                let arr = []
                if (this.contents.pageTotal) arr.push('total')
                if (this.contents.pageSizes) arr.push('sizes')
                if (this.contents.pagePrevNext) {
                    arr.push('prev')
                    if (this.contents.pagePager) arr.push('pager')
                    arr.push('next')
                }
                if (this.contents.pageJumper) arr.push('jumper')
                this.layouts = arr.join()
                this.contents.pageEachNum = 10
            },

            init() {
            },
            search() {
                this.pageIndex = 1;
                this.getDataList();
            },
            // 获取数据列表
            getDataList() {
                this.dataListLoading = true;
                let params = {
                    page: this.pageIndex,
                    limit: this.pageSize,
                    sort: 'id',
                }

                                         
                if (this.searchForm.shangjiaName!= '' && this.searchForm.shangjiaName!= undefined) {
                    params['shangjiaName'] = '%' + this.searchForm.shangjiaName + '%'
                }
                                                                                                                                                                                         
                if (this.searchForm.nongchanpinName!= '' && this.searchForm.nongchanpinName!= undefined) {
                    params['nongchanpinName'] = '%' + this.searchForm.nongchanpinName + '%'
                }
                 
                if (this.searchForm.nongchanpinTypes!= '' && this.searchForm.nongchanpinTypes!= undefined) {
                    params['nongchanpinTypes'] = this.searchForm.nongchanpinTypes
                }
                                                                
                params['nongchanpinDelete'] = 1// 逻辑删除字段 1 未删除 2 删除


                this.$http({
                    url: "nongchanpin/page",
                    method: "get",
                    params: params
                }).then(({data}) => {
                    if(data && data.code === 0){
                        this.dataList = data.data.list;
                        this.totalPage = data.data.total;
                    }else{
                        this.dataList = [];
                        this.totalPage = 0;
                    }
                    this.dataListLoading = false;
                });

                //查询级联表搜索条件所有列表
                //查询当前表搜索条件所有列表
                //填充下拉框选项
                this.$http({
                    url: "dictionary/page?dicCode=nongchanpin_types&page=1&limit=100",
                    method: "get",
                }).then(({data}) => {
                    if(data && data.code === 0){
                        this.nongchanpinTypesSelectSearch = data.data.list;
                    }
                });
            },
            //每页数
            sizeChangeHandle(val) {
                this.pageSize = val;
                this.pageIndex = 1;
                this.getDataList();
            },
            // 当前页
            currentChangeHandle(val) {
                this.pageIndex = val;
                this.getDataList();
            },
            // 多选
            selectionChangeHandler(val) {
                this.dataListSelections = val;
            },
            // 添加/修改
            addOrUpdateHandler(id, type) {
                this.showFlag = false;
                this.addOrUpdateFlag = true;
                this.crossAddOrUpdateFlag = false;
                if (type != 'info') {
                    type = 'else';
                }
                this.$nextTick(() => {
                    this.$refs.addOrUpdate.init(id, type);
                });
            },
            // 下载
            download(file) {
                window.open(" ${file} ")
            },
            // 弹出打印模态框
            dayinOpen(item) {
                let _this = this;
                _this.printVisiable = true;
                _this.printFrom=item;
            },
            // 打印内容
            printContent() {
                let _this = this;
                _this.$print(_this.$refs.printContent)
                _this.printVisiable = false;
            },
            // 删除
            deleteHandler(id) {
                var ids = id ? [Number(id)] : this.dataListSelections.map(item => {
                    return Number(item.id);
                });

                this.$confirm(`确定进行[${id ? "删除" : "批量删除"}]操作?`, "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                }).then(() => {
                    this.$http({
                        url: "nongchanpin/delete",
                        method: "post",
                        data: ids
                    }).then(({data}) => {
                        if(data && data.code === 0){
                            this.$message({
                                message: "操作成功",
                                type: "success",
                                duration: 1500,
                                onClose: () => {
                                    this.search();
                                }
                            });
                        }else{
                            this.$message.error(data.msg);
                        }
                    });
                });
            },
            // 导入功能上传文件成功后调用导入方法
            nongchanpinUploadSuccess(data){
                let _this = this;
                _this.$http({
                    url: "nongchanpin/batchInsert?fileName=" + data.file,
                    method: "get"
                }).then(({data}) => {
                    if(data && data.code === 0){
                        _this.$message({
                            message: "导入农产品数据成功",
                            type: "success",
                            duration: 1500,
                            onClose: () => {
                                _this.search();
                            }
                        });
                    }else{
                        _this.$message.error(data.msg);
                    }
                });

            },
            // 导入功能上传文件失败后调用导入方法
            nongchanpinUploadError(data){
                this.$message.error('上传失败');
            },
            
            // 格式化图片URL
            formatImageUrl(url) {
                if (!url) return '';
                if (url.startsWith('http')) {
                    return url;
                }
                return this.$base.url + (url.startsWith('/') ? '' : '/') + url;
            },
            //增加农产品库存
			plusNongchanpinKucunNumber(thisId,nongchanpinKucunNumber){
			//清空数据
				//农产品库存数据设置为0
				this.kucunNumber = 0
				//id清空
				this.kucunNumberId = null
				//农产品库存数据设置为0
				this.kucunNumber = 0
				//操作类型清空
				this.kucunNumberTypes = null
				//原有数量清空
				this.nongchanpinKucunNumber = 0
			//判断数据是否为空	
				if(thisId == null || thisId=="" || thisId=="null"){
					this.$message.error('未知错误,请联系管理员处理');
					return;
				}
			//数据不为空放值
				//id
				this.kucunNumberId = thisId
				//操作类型
				this.kucunNumberTypes = "+"
				//原有数量
				this.nongchanpinKucunNumber = nongchanpinKucunNumber
				//将模态框打开
				this.nongchanpinKucunNumberVisible = true;
				//标题
				this.nongchanpinKucunNumberTitle = '增加农产品库存'
			},
			//减少农产品库存
			reduceNongchanpinKucunNumber(thisId,nongchanpinKucunNumber){
			//清空数据
				//农产品库存数据设置为0
				this.kucunNumber = 0
				//id清空
				this.kucunNumberId = null
				//操作类型清空
				this.kucunNumberTypes = null
				//原有数量清空
				this.nongchanpinKucunNumber = 0
			//判断数据是否为空	
				if(thisId == null || thisId=="" || thisId=="null"){
					this.$message.error('未知错误,请联系管理员处理');
					return;
				}
			//数据不为空放值
				//id
				this.kucunNumberId = thisId
				//操作类型
				this.kucunNumberTypes = "-"
				//原有数量
				this.nongchanpinKucunNumber = nongchanpinKucunNumber
				//将模态框打开
				this.nongchanpinKucunNumberVisible = true;
				//标题
				this.nongchanpinKucunNumberTitle = '减少农产品库存'
			},
			//提交用户操作农产品库存数量
			addNongchanpinKucunNumber(){
				var type = null
				var maxNumber = 0
				if(this.kucunNumberTypes == "+"){
					type = '增加'
					maxNumber = this.nongchanpinKucunNumber + this.kucunNumber
				}else{
					type = '减少'
					if(this.nongchanpinKucunNumber < this.kucunNumber || this.kucunNumber <= 0){
						this.$message.error('农产品库存不足不能减少');
						return false;
					}else{
						maxNumber = this.nongchanpinKucunNumber - this.kucunNumber
					}
				}
				let date = {
					"id":this.kucunNumberId,
					"nongchanpinKucunNumber":maxNumber
				}
				this.$confirm(`确定要`+type+`物品数量`+this.kucunNumber+`个?`, "提示", {
				    confirmButtonText: "确定",
				    cancelButtonText: "取消",
				    type: "warning"
				}).then(() => {
				    this.$http({
				        url: "nongchanpin/update",
				        method: "post",
				        data: date
				    }).then(({ data }) => {
				        if (data && data.code === 0) {
				            this.$message({
				                message: "操作成功",
				                type: "success",
				                duration: 1500,
				                onClose: () => {
				                    this.search();
				                }
				            });
				        } else {
				            this.$message.error(data.msg);
				        }
				    });
				});
				this.nongchanpinKucunNumberVisible = false;
			},	
			//上下架
			shangxia(id,shangxiaTypes){
				var msg ="";
				if(shangxiaTypes == 1){
					shangxiaTypes=2;
					msg="下架";
				}else if(shangxiaTypes == 2){
					shangxiaTypes=1;
					msg="上架";
				}
				var shuju = {
						'id':id,
						'shangxiaTypes':shangxiaTypes,
					}
				this.$confirm(`确定要将它`+msg+`吗?`, "提示", {
				    confirmButtonText: "确定",
				    cancelButtonText: "取消",
				    type: "warning"
				}).then(() => {
				   this.$http({
				        url: "nongchanpin/update",
				        method: "post",
				        data: shuju
				    }).then(({ data }) => {
				        if (data && data.code === 0) {
				            this.$message({
				                message: msg+"成功",
				                type: "success",
				                duration: 1500,
				                onClose: () => {this.search();}
				            });
				        } else {
				            this.$message.error(data.msg);
				        }
				    });
				});
			},            //无用
            wuyong(id) {
                let _this = this;
                _this.$confirm(`确定    操作?`, "提示", {confirmButtonText: "确定",cancelButtonText: "取消",type: "warning"
                }).then(() => {
                    _this.$http({
                        url: "nongchanpin/update",
                        method: "post",
                        data: {
                            id:id,
//                            nongchanpinTypes:nongchanpinTypes,
                        }
                    }).then(({data}) => {
                        if(data && data.code === 0){
                            _this.$message({message: "操作成功",type: "success",duration: 1500,onClose: () => {
                                    _this.search();
                                }});
                        }else{
                            _this.$message.error(data.msg);
                        }
                    });
                });
            },
            // 添加导入导出处理方法
            handleExportImport() {
              // 默认是下载导入模板
              window.location.href = "http://localhost:8080/nongchanpinxiaoshou/upload/nongchanpinMuBan.xls";
            },
        }
    };
</script>
<style lang="scss" scoped>
.slt {
    margin: 0 !important;
    display: flex;
  }

  .ad {
    margin: 0 !important;
    display: flex;
  }

  .pages {
    & ::v-deep el-pagination__sizes{
      & ::v-deep el-input__inner {
        height: 22px;
        line-height: 22px;
      }
    }
  }


  .el-button+.el-button {
    margin:0;
  }

  .tables {
	& ::v-deep .el-button--success {
		height: 40px;
		color: #333;
		font-size: 14px;
		border-width: 2px;
		border-style: dashed ;
		border-color: rgba(255, 255, 255, 1);
		border-radius: 20px;
		background-color: var(--publicSubColor);
	}

	& ::v-deep .el-button--primary {
		height: 40px;
		color: #333;
		font-size: 14px;
		border-width: 2px;
		border-style: dashed ;
		border-color: rgba(255, 255, 255, 1);
		border-radius: 20px;
		background-color: var(--publicSubColor);
	}

	& ::v-deep .el-button--danger {
		height: 40px;
		color: #333;
		font-size: 14px;
		border-width: 2px;
		border-style: dashed ;
		border-color: rgba(255, 255, 255, 1);
		border-radius: 20px;
		background-color: var(--publicSubColor);
	}

    & ::v-deep .el-button {
      margin: 4px;
    }
  }
	.form-content {
		background: transparent;
	}
	.table-content {
		background: transparent;
	}

	.tables ::v-deep .el-table__body tr {
				background-color: rgba(255, 255, 255, 1) !important;
				color: rgba(92, 93, 95, 1) !important;
	 }
	.tables ::v-deep .el-table__body tr.el-table__row--striped td {
	    background: transparent;
	}
	.tables ::v-deep .el-table__body tr.el-table__row--striped {
		background-color: #F5F7FA !important;
		color: #606266 !important;
	}

	.tables ::v-deep .el-table__body tr:hover>td {
	   background-color: #f5f5f5 !important;
		   color: #333 !important;
 	}</style>

<style scoped>
    .main-content {
        padding: 10px;
    }
    
    .search-form {
        margin-bottom: 20px;
    }
    
    .slt {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 10px;
        align-items: center;
    }
    
    .action-buttons {
        display: flex;
        justify-content: flex-start;
        margin-top: 10px;
    }
    
    .data-table {
        margin-bottom: 20px;
    }
    
    .product-image {
        width: 80px;
        height: 80px;
        margin: 0 auto;
        overflow: hidden;
        border-radius: 4px;
    }
    
    .product-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    
    .no-image {
        width: 80px;
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f5f7fa;
        color: #909399;
        border-radius: 4px;
        margin: 0 auto;
        font-size: 12px;
    }
    
    .old-price {
        color: #999;
        text-decoration: line-through;
        font-size: 13px;
    }
    
    .new-price {
        color: #67a3ff;
        font-weight: bold;
        font-size: 15px;
    }
</style>


