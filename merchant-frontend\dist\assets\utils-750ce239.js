const g=(t,r="YYYY-MM-DD HH:mm:ss")=>{if(!t)return"";const e=new Date(t),n=e.getFullYear(),a=String(e.getMonth()+1).padStart(2,"0"),s=String(e.getDate()).padStart(2,"0"),o=String(e.getHours()).padStart(2,"0"),c=String(e.getMinutes()).padStart(2,"0"),u=String(e.getSeconds()).padStart(2,"0");return r.replace("YYYY",n).replace("MM",a).replace("DD",s).replace("HH",o).replace("mm",c).replace("ss",u)},d=t=>t==null?"¥0.00":`¥${Number(t).toFixed(2)}`,S=d;export{g as a,S as f};
