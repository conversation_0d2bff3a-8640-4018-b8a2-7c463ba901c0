import{c as ee,m as y}from"./api-7a2a6e2d.js";import{_ as oe}from"./_plugin-vue_export-helper-c27b6911.js";import{r as _,a as h,x as O,o as se,b as i,c as le,d as te,f as o,w as l,e as a,h as c,t as R,E as u}from"./index-2c4e09fa.js";const re={class:"profile-page"},ae={class:"avatar-section"},ne={class:"logo-section"},de={class:"security-info"},ue={class:"security-item"},ie={class:"security-item"},pe={class:"security-item"},me={class:"security-item"},fe={__name:"Profile",setup(ce){const w=ee(),b=_(null),V=_(null),P=_(null),k=_("http://localhost:8081/nongchanpinxiaoshou/api/upload"),n=h({avatar:"https://via.placeholder.com/80x80?text=头像",merchantName:"绿色农场",contactName:"张三",phone:"***********",email:"<EMAIL>",address:"北京市朝阳区农业园区123号",description:"专业提供优质有机农产品，致力于为消费者提供健康、安全的绿色食品。"}),d=h({shopName:"绿色农场直营店",logo:"https://via.placeholder.com/100x100?text=Logo",businessHours:["08:00","20:00"],deliveryRange:10,minOrderAmount:50,deliveryFee:5,status:!0}),p=h({currentPassword:"",newPassword:"",confirmPassword:""}),x=_("2025-07-07 14:30:25"),N=_("*************"),z={merchantName:[{required:!0,message:"请输入商家名称",trigger:"blur"}],contactName:[{required:!0,message:"请输入联系人",trigger:"blur"}],phone:[{required:!0,message:"请输入联系电话",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}],email:[{required:!0,message:"请输入邮箱地址",trigger:"blur"},{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}]},G={shopName:[{required:!0,message:"请输入店铺名称",trigger:"blur"}],businessHours:[{required:!0,message:"请选择营业时间",trigger:"change"}]},M={currentPassword:[{required:!0,message:"请输入当前密码",trigger:"blur"}],newPassword:[{required:!0,message:"请输入新密码",trigger:"blur"},{min:6,message:"密码长度不能少于6位",trigger:"blur"}],confirmPassword:[{required:!0,message:"请确认新密码",trigger:"blur"},{validator:(s,e,m)=>{e!==p.newPassword?m(new Error("两次输入的密码不一致")):m()},trigger:"blur"}]},q=O(()=>{const s=p.newPassword;if(!s)return"info";let e=0;return s.length>=8&&e++,/[a-z]/.test(s)&&e++,/[A-Z]/.test(s)&&e++,/\d/.test(s)&&e++,/[!@#$%^&*]/.test(s)&&e++,e<=2?"danger":e<=3?"warning":"success"}),j=O(()=>{switch(q.value){case"danger":return"弱";case"warning":return"中";case"success":return"强";default:return"未设置"}}),B=s=>{s.code===200?(n.avatar=s.data.url,u.success("头像上传成功")):u.error("头像上传失败")},I=s=>{const e=s.type==="image/jpeg"||s.type==="image/png",m=s.size/1024/1024<2;return e||u.error("头像图片只能是 JPG/PNG 格式!"),m||u.error("头像图片大小不能超过 2MB!"),e&&m},T=s=>{s.code===200?(d.logo=s.data.url,u.success("Logo上传成功")):u.error("Logo上传失败")},E=s=>{const e=s.type==="image/jpeg"||s.type==="image/png",m=s.size/1024/1024<5;return e||u.error("Logo图片只能是 JPG/PNG 格式!"),m||u.error("Logo图片大小不能超过 5MB!"),e&&m},J=async()=>{try{await b.value.validate();const s=await w.put(y.profile.update,n);s.code===200?u.success("基本信息保存成功"):u.error("保存失败："+s.message)}catch(s){console.error("保存基本信息失败:",s),u.error("保存失败")}},$=()=>{var s;(s=b.value)==null||s.resetFields()},D=async()=>{try{await V.value.validate();const s=await w.put(y.shop.update,d);s.code===200?u.success("店铺设置保存成功"):u.error("保存失败："+s.message)}catch(s){console.error("保存店铺设置失败:",s),u.error("保存失败")}},Z=()=>{var s;(s=V.value)==null||s.resetFields()},K=async()=>{try{await P.value.validate();const s=await w.put(y.profile.changePassword,{currentPassword:p.currentPassword,newPassword:p.newPassword});s.code===200?(u.success("密码修改成功"),A()):u.error("密码修改失败："+s.message)}catch(s){console.error("修改密码失败:",s),u.error("密码修改失败")}},A=()=>{var s;Object.assign(p,{currentPassword:"",newPassword:"",confirmPassword:""}),(s=P.value)==null||s.clearValidate()},Q=async()=>{try{const s=await w.get(y.profile.info);s.code===200&&(Object.assign(n,s.data.profile||n),Object.assign(d,s.data.shop||d),x.value=s.data.lastLoginTime||x.value,N.value=s.data.lastLoginIp||N.value)}catch(s){console.error("加载用户信息失败:",s)}};return se(()=>{Q()}),(s,e)=>{const m=i("el-avatar"),g=i("el-button"),C=i("el-upload"),r=i("el-form-item"),f=i("el-input"),U=i("el-form"),F=i("el-card"),v=i("el-col"),W=i("el-image"),X=i("el-time-picker"),L=i("el-input-number"),Y=i("el-switch"),H=i("el-row"),S=i("el-tag");return le(),te("div",re,[o(H,{gutter:20},{default:l(()=>[o(v,{span:12},{default:l(()=>[o(F,{class:"profile-card"},{header:l(()=>e[15]||(e[15]=[a("h3",null,"基本信息",-1)])),default:l(()=>[o(U,{ref_key:"profileFormRef",ref:b,model:n,rules:z,"label-width":"100px"},{default:l(()=>[o(r,{label:"头像"},{default:l(()=>[a("div",ae,[o(m,{size:80,src:n.avatar},null,8,["src"]),o(C,{class:"avatar-uploader",action:k.value,"show-file-list":!1,"on-success":B,"before-upload":I},{default:l(()=>[o(g,{size:"small",type:"primary"},{default:l(()=>e[16]||(e[16]=[c("更换头像")])),_:1,__:[16]})]),_:1},8,["action"])])]),_:1}),o(r,{label:"商家名称",prop:"merchantName"},{default:l(()=>[o(f,{modelValue:n.merchantName,"onUpdate:modelValue":e[0]||(e[0]=t=>n.merchantName=t)},null,8,["modelValue"])]),_:1}),o(r,{label:"联系人",prop:"contactName"},{default:l(()=>[o(f,{modelValue:n.contactName,"onUpdate:modelValue":e[1]||(e[1]=t=>n.contactName=t)},null,8,["modelValue"])]),_:1}),o(r,{label:"联系电话",prop:"phone"},{default:l(()=>[o(f,{modelValue:n.phone,"onUpdate:modelValue":e[2]||(e[2]=t=>n.phone=t)},null,8,["modelValue"])]),_:1}),o(r,{label:"邮箱地址",prop:"email"},{default:l(()=>[o(f,{modelValue:n.email,"onUpdate:modelValue":e[3]||(e[3]=t=>n.email=t)},null,8,["modelValue"])]),_:1}),o(r,{label:"经营地址",prop:"address"},{default:l(()=>[o(f,{modelValue:n.address,"onUpdate:modelValue":e[4]||(e[4]=t=>n.address=t),type:"textarea",rows:3},null,8,["modelValue"])]),_:1}),o(r,{label:"商家简介",prop:"description"},{default:l(()=>[o(f,{modelValue:n.description,"onUpdate:modelValue":e[5]||(e[5]=t=>n.description=t),type:"textarea",rows:4},null,8,["modelValue"])]),_:1}),o(r,null,{default:l(()=>[o(g,{type:"primary",onClick:J},{default:l(()=>e[17]||(e[17]=[c("保存信息")])),_:1,__:[17]}),o(g,{onClick:$},{default:l(()=>e[18]||(e[18]=[c("重置")])),_:1,__:[18]})]),_:1})]),_:1},8,["model"])]),_:1})]),_:1}),o(v,{span:12},{default:l(()=>[o(F,{class:"shop-card"},{header:l(()=>e[19]||(e[19]=[a("h3",null,"店铺设置",-1)])),default:l(()=>[o(U,{ref_key:"shopFormRef",ref:V,model:d,rules:G,"label-width":"100px"},{default:l(()=>[o(r,{label:"店铺名称",prop:"shopName"},{default:l(()=>[o(f,{modelValue:d.shopName,"onUpdate:modelValue":e[6]||(e[6]=t=>d.shopName=t)},null,8,["modelValue"])]),_:1}),o(r,{label:"店铺Logo"},{default:l(()=>[a("div",ne,[o(W,{src:d.logo,style:{width:"100px",height:"100px"},fit:"cover"},null,8,["src"]),o(C,{class:"logo-uploader",action:k.value,"show-file-list":!1,"on-success":T,"before-upload":E},{default:l(()=>[o(g,{size:"small",type:"primary"},{default:l(()=>e[20]||(e[20]=[c("更换Logo")])),_:1,__:[20]})]),_:1},8,["action"])])]),_:1}),o(r,{label:"营业时间",prop:"businessHours"},{default:l(()=>[o(X,{modelValue:d.businessHours,"onUpdate:modelValue":e[7]||(e[7]=t=>d.businessHours=t),"is-range":"","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",format:"HH:mm","value-format":"HH:mm"},null,8,["modelValue"])]),_:1}),o(r,{label:"配送范围",prop:"deliveryRange"},{default:l(()=>[o(L,{modelValue:d.deliveryRange,"onUpdate:modelValue":e[8]||(e[8]=t=>d.deliveryRange=t),min:1,max:50,"controls-position":"right"},null,8,["modelValue"]),e[21]||(e[21]=a("span",{style:{"margin-left":"8px"}},"公里",-1))]),_:1,__:[21]}),o(r,{label:"起送金额",prop:"minOrderAmount"},{default:l(()=>[o(L,{modelValue:d.minOrderAmount,"onUpdate:modelValue":e[9]||(e[9]=t=>d.minOrderAmount=t),min:0,precision:2,"controls-position":"right"},null,8,["modelValue"]),e[22]||(e[22]=a("span",{style:{"margin-left":"8px"}},"元",-1))]),_:1,__:[22]}),o(r,{label:"配送费",prop:"deliveryFee"},{default:l(()=>[o(L,{modelValue:d.deliveryFee,"onUpdate:modelValue":e[10]||(e[10]=t=>d.deliveryFee=t),min:0,precision:2,"controls-position":"right"},null,8,["modelValue"]),e[23]||(e[23]=a("span",{style:{"margin-left":"8px"}},"元",-1))]),_:1,__:[23]}),o(r,{label:"店铺状态",prop:"status"},{default:l(()=>[o(Y,{modelValue:d.status,"onUpdate:modelValue":e[11]||(e[11]=t=>d.status=t),"active-text":"营业中","inactive-text":"休息中"},null,8,["modelValue"])]),_:1}),o(r,null,{default:l(()=>[o(g,{type:"primary",onClick:D},{default:l(()=>e[24]||(e[24]=[c("保存设置")])),_:1,__:[24]}),o(g,{onClick:Z},{default:l(()=>e[25]||(e[25]=[c("重置")])),_:1,__:[25]})]),_:1})]),_:1},8,["model"])]),_:1})]),_:1})]),_:1}),o(F,{class:"security-card"},{header:l(()=>e[26]||(e[26]=[a("h3",null,"安全设置",-1)])),default:l(()=>[o(H,{gutter:20},{default:l(()=>[o(v,{span:12},{default:l(()=>[o(U,{ref_key:"passwordFormRef",ref:P,model:p,rules:M,"label-width":"100px"},{default:l(()=>[o(r,{label:"当前密码",prop:"currentPassword"},{default:l(()=>[o(f,{modelValue:p.currentPassword,"onUpdate:modelValue":e[12]||(e[12]=t=>p.currentPassword=t),type:"password","show-password":""},null,8,["modelValue"])]),_:1}),o(r,{label:"新密码",prop:"newPassword"},{default:l(()=>[o(f,{modelValue:p.newPassword,"onUpdate:modelValue":e[13]||(e[13]=t=>p.newPassword=t),type:"password","show-password":""},null,8,["modelValue"])]),_:1}),o(r,{label:"确认密码",prop:"confirmPassword"},{default:l(()=>[o(f,{modelValue:p.confirmPassword,"onUpdate:modelValue":e[14]||(e[14]=t=>p.confirmPassword=t),type:"password","show-password":""},null,8,["modelValue"])]),_:1}),o(r,null,{default:l(()=>[o(g,{type:"primary",onClick:K},{default:l(()=>e[27]||(e[27]=[c("修改密码")])),_:1,__:[27]}),o(g,{onClick:A},{default:l(()=>e[28]||(e[28]=[c("重置")])),_:1,__:[28]})]),_:1})]),_:1},8,["model"])]),_:1}),o(v,{span:12},{default:l(()=>[a("div",de,[e[34]||(e[34]=a("h4",null,"账户安全",-1)),a("div",ue,[e[30]||(e[30]=a("span",null,"登录保护：",-1)),o(S,{type:"success"},{default:l(()=>e[29]||(e[29]=[c("已开启")])),_:1,__:[29]})]),a("div",ie,[e[31]||(e[31]=a("span",null,"最后登录：",-1)),a("span",null,R(x.value),1)]),a("div",pe,[e[32]||(e[32]=a("span",null,"登录IP：",-1)),a("span",null,R(N.value),1)]),a("div",me,[e[33]||(e[33]=a("span",null,"密码强度：",-1)),o(S,{type:q.value},{default:l(()=>[c(R(j.value),1)]),_:1},8,["type"])])])]),_:1})]),_:1})]),_:1})])}}},ve=oe(fe,[["__scopeId","data-v-b73d2b25"]]);export{ve as default};
