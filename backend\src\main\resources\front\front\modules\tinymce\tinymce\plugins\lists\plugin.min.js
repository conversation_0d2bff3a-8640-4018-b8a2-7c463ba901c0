/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.2.0 (2020-02-13)
 */
!function(r){"use strict";function e(){}function l(e){return function(){return e}}function t(t){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return!t.apply(null,e)}}function n(){return s}var o,i=tinymce.util.Tools.resolve("tinymce.PluginManager"),u=l(!1),a=l(!0),s=(o={fold:function(e,n){return e()},is:u,isSome:u,isNone:a,getOr:d,getOrThunk:f,getOrDie:function(e){throw new Error(e||"error: getOrDie called on none.")},getOrNull:l(null),getOrUndefined:l(undefined),or:d,orThunk:f,map:n,each:e,bind:n,exists:u,forall:a,filter:n,equals:c,equals_:c,toArray:function(){return[]},toString:l("none()")},Object.freeze&&Object.freeze(o),o);function c(e){return e.isNone()}function f(e){return e()}function d(e){return e}function m(n){return function(e){return function(e){if(null===e)return"null";var n=typeof e;return"object"==n&&(Array.prototype.isPrototypeOf(e)||e.constructor&&"Array"===e.constructor.name)?"array":"object"==n&&(String.prototype.isPrototypeOf(e)||e.constructor&&"String"===e.constructor.name)?"string":n}(e)===n}}function g(e,n){for(var t=e.length,r=new Array(t),o=0;o<t;o++){var i=e[o];r[o]=n(i,o)}return r}function p(e,n){for(var t=0,r=e.length;t<r;t++){n(e[t],t)}}function v(e,n){for(var t=[],r=0,o=e.length;r<o;r++){var i=e[r];n(i,r)&&t.push(i)}return t}function h(e,n,t){return p(e,function(e){t=n(t,e)}),t}function N(e,n){for(var t=0,r=e.length;t<r;t++){var o=e[t];if(n(o,t))return He.some(o)}return He.none()}function y(e,n){return function(e){for(var n=[],t=0,r=e.length;t<r;++t){if(!qe(e[t]))throw new Error("Arr.flatten item "+t+" was not an array, input: "+e);ze.apply(n,e[t])}return n}(g(e,n))}function S(e){return 0===e.length?He.none():He.some(e[0])}function O(e){return 0===e.length?He.none():He.some(e[e.length-1])}function C(e,n,t){return 0!=(e.compareDocumentPosition(n)&t)}function b(e,n){var t=function(e,n){for(var t=0;t<e.length;t++){var r=e[t];if(r.test(n))return r}return undefined}(e,n);if(!t)return{major:0,minor:0};function r(e){return Number(n.replace(t,"$"+e))}return Ze(r(1),r(2))}function L(e,n){return function(){return n===e}}function T(e,n){return function(){return n===e}}function D(e,n){var t=String(n).toLowerCase();return N(e,function(e){return e.search(t)})}function E(e,n){return-1!==e.indexOf(n)}function w(n){return function(e){return E(e,n)}}function k(e,n){return e.dom()===n.dom()}function A(e,n,t){return e.isSome()&&n.isSome()?He.some(t(e.getOrDie(),n.getOrDie())):He.none()}function x(e){return He.from(e.dom().parentNode).map(Ln.fromDom)}function R(e){return g(e.dom().childNodes,Ln.fromDom)}function I(e,n){var t=e.dom().childNodes;return He.from(t[n]).map(Ln.fromDom)}function B(e){return I(e,0)}function P(e){return I(e,e.dom().childNodes.length-1)}function _(n,t){x(n).each(function(e){e.dom().insertBefore(t.dom(),n.dom())})}function M(e,n){e.dom().appendChild(n.dom())}function U(n,e){p(e,function(e){M(n,e)})}function F(e){var n=e.dom();null!==n.parentNode&&n.parentNode.removeChild(n)}function j(e,n,t){return e.fire("ListMutation",{action:n,element:t})}function H(e,n){return function(e,n){for(var t=n!==undefined&&null!==n?n:In,r=0;r<e.length&&t!==undefined&&null!==t;++r)t=t[e[r]];return t}(e.split("."),n)}function $(e){return e&&"BR"===e.nodeName}function q(e){var n=e.selection.getStart(!0);return e.dom.getParent(n,"OL,UL,DL",Gn(e,n))}function W(e){var n=e.selection.getSelectedBlocks();return Mn.grep(function(t,e){var n=Mn.map(e,function(e){var n=t.dom.getParent(e,"li,dd,dt",Gn(t,e));return n||e});return _n.unique(n)}(e,n),function(e){return $n(e)})}function V(e,n){var t=e.dom.getParents(n,"ol,ul",Gn(e,n));return O(t)}function K(e,n){var t,r,o,i=e.dom,u=e.schema.getBlockElements(),a=i.createFragment(),s=nt(e);if(s&&((r=i.create(s)).tagName===s.toUpperCase()&&i.setAttribs(r,tt(e)),Xn(n.firstChild,u)||a.appendChild(r)),n)for(;t=n.firstChild;){var c=t.nodeName;o||"SPAN"===c&&"bookmark"===t.getAttribute("data-mce-type")||(o=!0),Xn(t,u)?(a.appendChild(t),r=null):s?(r||(r=i.create(s),a.appendChild(r)),r.appendChild(t)):a.appendChild(t)}return s?o||r.appendChild(i.create("br",{"data-mce-bogus":"1"})):a.appendChild(i.create("br")),a}function X(e){return e.dom().nodeName.toLowerCase()}function z(e,n){var t=e.dom();!function(e,n){for(var t=Rn(e),r=0,o=t.length;r<o;r++){var i=t[r];n(e[i],i)}}(n,function(e,n){!function(e,n,t){if(!($e(t)||We(t)||Ke(t)))throw r.console.error("Invalid call to Attr.set. Key ",n,":: Value ",t,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(n,t+"")}(t,n,e)})}function Q(e){return h(e.dom().attributes,function(e,n){return e[n.name]=n.value,e},{})}function Y(e,n,t){if(!$e(t))throw r.console.error("Invalid call to CSS.set. Property ",n,":: Value ",t,":: Element ",e),new Error("CSS value must be a string: "+t);!function(e){return e.style!==undefined&&Ve(e.style.getPropertyValue)}(e)||e.style.setProperty(n,t)}function G(e){return function(e,n){return Ln.fromDom(e.dom().cloneNode(n))}(e,!0)}function J(e,n){var t=function(e,n){var t=Ln.fromTag(n),r=Q(e);return z(t,r),t}(e,n);_(e,t);var r=R(e);return U(t,r),F(e),t}function Z(e,n){M(e.item,n.list)}function ee(n,e,t){var r=e.slice(0,t.depth);return O(r).each(function(e){!function(e,n){M(e.list,n),e.item=n}(e,function(e,n,t){var r=Ln.fromTag("li",e);return z(r,n),U(r,t),r}(n,t.itemAttributes,t.content)),function(e,n){X(e.list)!==n.listType&&(e.list=J(e.list,n.listType)),z(e.list,n.listAttributes)}(e,t)}),r}function ne(e,n,t){var r=function(e,n,t){for(var r,o,i,u=[],a=0;a<t;a++)u.push((r=e,o=n.listType,void 0,i={list:Ln.fromTag(o,r),item:Ln.fromTag("li",r)},M(i.list,i.item),i));return u}(e,t,t.depth-n.length);return function(e){for(var n=1;n<e.length;n++)Z(e[n-1],e[n])}(r),function(e,n){for(var t=0;t<e.length-1;t++)r=e[t].item,o="list-style-type",i="none",void 0,u=r.dom(),Y(u,o,i);var r,o,i,u;O(e).each(function(e){z(e.list,n.listAttributes),z(e.item,n.itemAttributes),U(e.item,n.content)})}(r,t),function(e,n){A(O(e),S(n),Z)}(n,r),n.concat(r)}function te(e){return wn(e,"OL,UL")}function re(e){return B(e).map(te).getOr(!1)}function oe(e){return 0<e.depth}function ie(e){return e.isSelected}function ue(e){var n=R(e),t=function(e){return P(e).map(te).getOr(!1)}(e)?n.slice(0,-1):n;return g(t,G)}function ae(t){p(t,function(n,e){(function(e,n){for(var t=e[n].depth,r=n-1;0<=r;r--){if(e[r].depth===t)return He.some(e[r]);if(e[r].depth<t)break}return He.none()})(t,e).each(function(e){!function(e,n){e.listType=n.listType,e.listAttributes=Ye({},n.listAttributes)}(n,e)})})}function se(t,r,o,i){return B(i).filter(te).fold(function(){r.each(function(e){k(e.start,i)&&o.set(!0)});var e=function(n,t,r){return x(n).filter(rt).map(function(e){return{depth:t,isSelected:r,content:ue(n),itemAttributes:Q(n),listAttributes:Q(e),listType:X(e)}})}(i,t,o.get());r.each(function(e){k(e.end,i)&&o.set(!1)});var n=P(i).filter(te).map(function(e){return ot(t,r,o,e)}).getOr([]);return e.toArray().concat(n)},function(e){return ot(t,r,o,e)})}function ce(t,e){return g(e,function(e){var n=function(e,n){var t=(n||r.document).createDocumentFragment();return p(e,function(e){t.appendChild(e.dom())}),Ln.fromDom(t)}(e.content);return Ln.fromDom(K(t,n.dom()))})}function fe(e,n){return ae(n),function(t,e){var n=h(e,function(e,n){return n.depth>e.length?ne(t,e,n):ee(t,e,n)},[]);return S(n).map(function(e){return e.list})}(e.contentDocument,n).toArray()}function de(e){var n=g(Zn.getSelectedListItems(e),Ln.fromDom);return A(N(n,t(re)),N(function(e){var n=Xe.call(e,0);return n.reverse(),n}(n),t(re)),function(e,n){return{start:e,end:n}})}function le(t,e,r){var n=function(e,n){var t=Ge(!1);return g(e,function(e){return{sourceList:e,entries:ot(0,n,t,e)}})}(e,de(t));p(n,function(e){!function(e,n){p(v(e,ie),function(e){return function(e,n){switch(e){case"Indent":n.depth++;break;case"Outdent":n.depth--;break;case"Flatten":n.depth=0}}(n,e)})}(e.entries,r);var n=function(n,e){return y(function(e,n){if(0===e.length)return[];for(var t=n(e[0]),r=[],o=[],i=0,u=e.length;i<u;i++){var a=e[i],s=n(a);s!==t&&(r.push(o),o=[]),t=s,o.push(a)}return 0!==o.length&&r.push(o),r}(e,oe),function(e){return S(e).map(oe).getOr(!1)?fe(n,e):ce(n,e)})}(t,e.entries);p(n,function(e){j(t,"Indent"===r?"IndentList":"OutdentList",e.dom())}),function(n,e){p(e,function(e){_(n,e)})}(e.sourceList,n),F(e.sourceList)})}function me(e){wn(e,"dt")&&J(e,"dd")}function ge(n,e,t){p(t,"Indent"===e?me:function(e){return function(n,t){wn(t,"dd")?J(t,"dt"):wn(t,"dt")&&x(t).each(function(e){return at(n,e.dom(),t.dom())})}(n,e)})}function pe(e,n){if(Un(e))return{container:e,offset:n};var t=kn.getNode(e,n);return Un(t)?{container:t,offset:n>=e.childNodes.length?t.data.length:0}:t.previousSibling&&Un(t.previousSibling)?{container:t.previousSibling,offset:t.previousSibling.data.length}:t.nextSibling&&Un(t.nextSibling)?{container:t.nextSibling,offset:0}:{container:e,offset:n}}function ve(e,n){var t=g(Zn.getSelectedListRoots(e),Ln.fromDom),r=g(Zn.getSelectedDlItems(e),Ln.fromDom),o=!1;if(t.length||r.length){var i=e.selection.getBookmark();le(e,t,n),ge(e,n,r),e.selection.moveToBookmark(i),e.selection.setRng(st(e.selection.getRng())),e.nodeChanged(),o=!0}return o}function he(e){return ve(e,"Indent")}function Ne(e){return ve(e,"Outdent")}function ye(e){return ve(e,"Flatten")}function Se(e){return/\btox\-/.test(e.className)}function Oe(e){switch(e){case"UL":return"ToggleUlList";case"OL":return"ToggleOlList";case"DL":return"ToggleDLList"}}function Ce(t,e){Mn.each(e,function(e,n){t.setAttribute(n,e)})}function be(e,n,t){!function(e,n,t){var r=t["list-style-type"]?t["list-style-type"]:null;e.setStyle(n,"list-style-type",r)}(e,n,t),function(e,n,t){Ce(n,t["list-attributes"]),Mn.each(e.select("li",n),function(e){Ce(e,t["list-item-attributes"])})}(e,n,t)}function Le(e,n,t,r){var o,i;for(o=n[t?"startContainer":"endContainer"],i=n[t?"startOffset":"endOffset"],1===o.nodeType&&(o=o.childNodes[Math.min(i,o.childNodes.length-1)]||o),!t&&Wn(o.nextSibling)&&(o=o.nextSibling);o.parentNode!==r;){if(Kn(e,o))return o;if(/^(TD|TH)$/.test(o.parentNode.nodeName))return o;o=o.parentNode}return o}function Te(r,o,i){void 0===i&&(i={});var e,n=r.selection.getRng(!0),u="LI",t=Zn.getClosestListRootElm(r,r.selection.getStart(!0)),a=r.dom;"false"!==a.getContentEditable(r.selection.getNode())&&("DL"===(o=o.toUpperCase())&&(u="DT"),e=dt(n),Mn.each(function(t,e,r){for(var o,i=[],u=t.dom,n=Le(t,e,!0,r),a=Le(t,e,!1,r),s=[],c=n;c&&(s.push(c),c!==a);c=c.nextSibling);return Mn.each(s,function(e){if(Kn(t,e))return i.push(e),void(o=null);if(u.isBlock(e)||Wn(e))return Wn(e)&&u.remove(e),void(o=null);var n=e.nextSibling;ct.isBookmarkNode(e)&&(Kn(t,n)||!n&&e.parentNode===r)?o=null:(o||(o=u.create("p"),e.parentNode.insertBefore(o,e),i.push(o)),o.appendChild(e))}),i}(r,n,t),function(e){var n,t;(t=e.previousSibling)&&Fn(t)&&t.nodeName===o&&function(e,n,t){var r=e.getStyle(n,"list-style-type"),o=t?t["list-style-type"]:"";return r===(o=null===o?"":o)}(a,t,i)?(n=t,e=a.rename(e,u),t.appendChild(e)):(n=a.create(o),e.parentNode.insertBefore(n,e),n.appendChild(e),e=a.rename(e,u)),function(t,r,e){Mn.each(e,function(e){var n;return t.setStyle(r,((n={})[e]="",n))})}(a,e,["margin","margin-right","margin-bottom","margin-left","margin-top","padding","padding-right","padding-bottom","padding-left","padding-top"]),be(a,n,i),mt(r.dom,n)}),r.selection.setRng(lt(e)))}function De(e,n,t){return function(e,n){return e&&n&&Fn(e)&&e.nodeName===n.nodeName}(n,t)&&function(e,n,t){return e.getStyle(n,"list-style-type",!0)===e.getStyle(t,"list-style-type",!0)}(e,n,t)&&function(e,n){return e.className===n.className}(n,t)}function Ee(n,e,t,r,o){if(e.nodeName!==r||gt(o)){var i=dt(n.selection.getRng(!0));Mn.each([e].concat(t),function(e){!function(e,n,t,r){if(n.nodeName!==t){var o=e.dom.rename(n,t);be(e.dom,o,r),j(e,Oe(t),o)}else be(e.dom,n,r),j(e,Oe(t),n)}(n,e,r,o)}),n.selection.setRng(lt(i))}else ye(n)}function we(e,n){var t,r=n.parentNode;"LI"===r.nodeName&&r.firstChild===n&&((t=r.previousSibling)&&"LI"===t.nodeName?(t.appendChild(n),Qn(e,r)&&vt.remove(r)):vt.setStyle(r,"listStyleType","none")),Fn(r)&&(t=r.previousSibling)&&"LI"===t.nodeName&&t.appendChild(n)}function ke(e,n,t,r){var o=n.startContainer,i=n.startOffset;if(Un(o)&&(t?i<o.data.length:0<i))return o;var u=e.schema.getNonEmptyElements();1===o.nodeType&&(o=kn.getNode(o,i));var a=new An(o,r);for(t&&zn(e.dom,o)&&a.next();o=a[t?"next":"prev2"]();){if("LI"===o.nodeName&&!o.hasChildNodes())return o;if(u[o.nodeName])return o;if(Un(o)&&0<o.data.length)return o}}function Ae(e,n){var t=n.childNodes;return 1===t.length&&!Fn(t[0])&&e.isBlock(t[0])}function xe(e,n,t){var r,o;if(o=Ae(e,t)?t.firstChild:t,function(e,n){Ae(e,n)&&e.remove(n.firstChild,!0)}(e,n),!Qn(e,n,!0))for(;r=n.firstChild;)o.appendChild(r)}function Re(n,e,t){var r,o,i=e.parentNode;if(Yn(n,e)&&Yn(n,t)){Fn(t.lastChild)&&(o=t.lastChild),i===t.lastChild&&Wn(i.previousSibling)&&n.remove(i.previousSibling),(r=t.lastChild)&&Wn(r)&&e.hasChildNodes()&&n.remove(r),Qn(n,t,!0)&&n.$(t).empty(),xe(n,e,t),o&&t.appendChild(o);var u=En(Ln.fromDom(t),Ln.fromDom(e))?n.getParents(e,Fn,t):[];n.remove(e),p(u,function(e){Qn(n,e)&&e!==n.getRoot()&&n.remove(e)})}}function Ie(e,n,t,r){var o=e.dom;if(o.isEmpty(r))!function(e,n,t){e.dom.$(t).empty(),Re(e.dom,n,t),e.selection.setCursorLocation(t)}(e,t,r);else{var i=dt(n);Re(o,t,r),e.selection.setRng(lt(i))}}function Be(e,n){var t=e.dom,r=e.selection,o=r.getStart(),i=Zn.getClosestListRootElm(e,o),u=t.getParent(r.getStart(),"LI",i);if(u){var a=u.parentNode;if(a===e.getBody()&&Qn(t,a))return!0;var s=st(r.getRng()),c=t.getParent(ke(e,s,n,i),"LI",i);if(c&&c!==u)return e.undoManager.transact(function(){n?Ie(e,s,c,u):Vn(u)?Ne(e):function(e,n,t,r){var o=dt(n);Re(e.dom,t,r);var i=lt(o);e.selection.setRng(i)}(e,s,u,c)}),!0;if(!c&&!n&&0===s.startOffset&&0===s.endOffset)return e.undoManager.transact(function(){ye(e)}),!0}return!1}function Pe(e,n){return Be(e,n)||function(e,n){var t=e.dom,r=e.selection.getStart(),o=Zn.getClosestListRootElm(e,r),i=t.getParent(r,t.isBlock,o);if(i&&t.isEmpty(i)){var u=st(e.selection.getRng()),a=t.getParent(ke(e,u,n,o),"LI",o);if(a)return e.undoManager.transact(function(){!function(e,n,t){var r=e.getParent(n.parentNode,e.isBlock,t);e.remove(n),r&&e.isEmpty(r)&&e.remove(r)}(t,i,o),pt.mergeWithAdjacentLists(t,a.parentNode),e.selection.select(a,!0),e.selection.collapse(n)}),!0}return!1}(e,n)}function _e(e,n){return e.selection.isCollapsed()?Pe(e,n):function(e){var n=e.selection.getStart(),t=Zn.getClosestListRootElm(e,n);return!!(e.dom.getParent(n,"LI,DT,DD",t)||0<Zn.getSelectedListItems(e).length)&&(e.undoManager.transact(function(){e.execCommand("Delete"),ht(e.dom,e.getBody())}),!0)}(e)}function Me(n,t){return function(){var e=n.dom.getParent(n.selection.getStart(),"UL,OL,DL");return e&&e.nodeName===t}}function Ue(n,i){return function(o){function e(e){var n=function(e,n){for(var t=0;t<e.length;t++){if(n(e[t]))return t}return-1}(e.parents,qn),t=-1!==n?e.parents.slice(0,n):e.parents,r=Mn.grep(t,Fn);o.setActive(0<r.length&&r[0].nodeName===i&&!Se(r[0]))}return n.on("NodeChange",e),function(){return n.off("NodeChange",e)}}}var Fe,je=function(t){function e(){return o}function n(e){return e(t)}var r=l(t),o={fold:function(e,n){return n(t)},is:function(e){return t===e},isSome:a,isNone:u,getOr:r,getOrThunk:r,getOrDie:r,getOrNull:r,getOrUndefined:r,or:e,orThunk:e,map:function(e){return je(e(t))},each:function(e){e(t)},bind:n,exists:n,forall:n,filter:function(e){return e(t)?o:s},toArray:function(){return[t]},toString:function(){return"some("+t+")"},equals:function(e){return e.is(t)},equals_:function(e,n){return e.fold(u,function(e){return n(t,e)})}};return o},He={some:je,none:n,from:function(e){return null===e||e===undefined?s:je(e)}},$e=m("string"),qe=m("array"),We=m("boolean"),Ve=m("function"),Ke=m("number"),Xe=Array.prototype.slice,ze=Array.prototype.push,Qe=(Ve(Array.from)&&Array.from,function(e,n){return C(e,n,r.Node.DOCUMENT_POSITION_CONTAINED_BY)}),Ye=function(){return(Ye=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++)for(var o in n=arguments[t])Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o]);return e}).apply(this,arguments)},Ge=function(e){function n(){return t}var t=e;return{get:n,set:function(e){t=e},clone:function(){return Ge(n())}}},Je=function(){return Ze(0,0)},Ze=function(e,n){return{major:e,minor:n}},en={nu:Ze,detect:function(e,n){var t=String(n).toLowerCase();return 0===e.length?Je():b(e,t)},unknown:Je},nn="Edge",tn="Chrome",rn="Opera",on="Firefox",un="Safari",an=function(e){var n=e.current;return{current:n,version:e.version,isEdge:L(nn,n),isChrome:L(tn,n),isIE:L("IE",n),isOpera:L(rn,n),isFirefox:L(on,n),isSafari:L(un,n)}},sn={unknown:function(){return an({current:undefined,version:en.unknown()})},nu:an,edge:l(nn),chrome:l(tn),ie:l("IE"),opera:l(rn),firefox:l(on),safari:l(un)},cn="Windows",fn="Android",dn="Solaris",ln="FreeBSD",mn="ChromeOS",gn=function(e){var n=e.current;return{current:n,version:e.version,isWindows:T(cn,n),isiOS:T("iOS",n),isAndroid:T(fn,n),isOSX:T("OSX",n),isLinux:T("Linux",n),isSolaris:T(dn,n),isFreeBSD:T(ln,n),isChromeOS:T(mn,n)}},pn={unknown:function(){return gn({current:undefined,version:en.unknown()})},nu:gn,windows:l(cn),ios:l("iOS"),android:l(fn),linux:l("Linux"),osx:l("OSX"),solaris:l(dn),freebsd:l(ln),chromeos:l(mn)},vn=function(e,t){return D(e,t).map(function(e){var n=en.detect(e.versionRegexes,t);return{current:e.name,version:n}})},hn=function(e,t){return D(e,t).map(function(e){var n=en.detect(e.versionRegexes,t);return{current:e.name,version:n}})},Nn=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,yn=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(e){return E(e,"edge/")&&E(e,"chrome")&&E(e,"safari")&&E(e,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,Nn],search:function(e){return E(e,"chrome")&&!E(e,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(e){return E(e,"msie")||E(e,"trident")}},{name:"Opera",versionRegexes:[Nn,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:w("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:w("firefox")},{name:"Safari",versionRegexes:[Nn,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(e){return(E(e,"safari")||E(e,"mobile/"))&&E(e,"applewebkit")}}],Sn=[{name:"Windows",search:w("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(e){return E(e,"iphone")||E(e,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:w("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:w("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:w("linux"),versionRegexes:[]},{name:"Solaris",search:w("sunos"),versionRegexes:[]},{name:"FreeBSD",search:w("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:w("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],On={browsers:l(yn),oses:l(Sn)},Cn=Ge(function(e,n){var t=On.browsers(),r=On.oses(),o=vn(t,e).fold(sn.unknown,sn.nu),i=hn(r,e).fold(pn.unknown,pn.nu);return{browser:o,os:i,deviceType:function(e,n,t,r){var o=e.isiOS()&&!0===/ipad/i.test(t),i=e.isiOS()&&!o,u=e.isiOS()||e.isAndroid(),a=u||r("(pointer:coarse)"),s=o||!i&&u&&r("(min-device-width:768px)"),c=i||u&&!s,f=n.isSafari()&&e.isiOS()&&!1===/safari/i.test(t),d=!c&&!s&&!f;return{isiPad:l(o),isiPhone:l(i),isTablet:l(s),isPhone:l(c),isTouch:l(a),isAndroid:e.isAndroid,isiOS:e.isiOS,isWebView:l(f),isDesktop:l(d)}}(i,o,e,n)}}(r.navigator.userAgent,function(e){return r.window.matchMedia(e).matches})),bn=function(e){if(null===e||e===undefined)throw new Error("Node cannot be null or undefined");return{dom:l(e)}},Ln={fromHtml:function(e,n){var t=(n||r.document).createElement("div");if(t.innerHTML=e,!t.hasChildNodes()||1<t.childNodes.length)throw r.console.error("HTML does not have a single root node",e),new Error("HTML must have a single root node");return bn(t.childNodes[0])},fromTag:function(e,n){var t=(n||r.document).createElement(e);return bn(t)},fromText:function(e,n){var t=(n||r.document).createTextNode(e);return bn(t)},fromDom:bn,fromPoint:function(e,n,t){var r=e.dom();return He.from(r.elementFromPoint(n,t)).map(bn)}},Tn=(r.Node.ATTRIBUTE_NODE,r.Node.CDATA_SECTION_NODE,r.Node.COMMENT_NODE,r.Node.DOCUMENT_NODE,r.Node.DOCUMENT_TYPE_NODE,r.Node.DOCUMENT_FRAGMENT_NODE,r.Node.ELEMENT_NODE),Dn=(r.Node.TEXT_NODE,r.Node.PROCESSING_INSTRUCTION_NODE,r.Node.ENTITY_REFERENCE_NODE,r.Node.ENTITY_NODE,r.Node.NOTATION_NODE,Tn),En=Cn.get().browser.isIE()?function(e,n){return Qe(e.dom(),n.dom())}:function(e,n){var t=e.dom(),r=n.dom();return t!==r&&t.contains(r)},wn=function(e,n){var t=e.dom();if(t.nodeType!==Dn)return!1;var r=t;if(r.matches!==undefined)return r.matches(n);if(r.msMatchesSelector!==undefined)return r.msMatchesSelector(n);if(r.webkitMatchesSelector!==undefined)return r.webkitMatchesSelector(n);if(r.mozMatchesSelector!==undefined)return r.mozMatchesSelector(n);throw new Error("Browser lacks native selectors")},kn=tinymce.util.Tools.resolve("tinymce.dom.RangeUtils"),An=tinymce.util.Tools.resolve("tinymce.dom.TreeWalker"),xn=tinymce.util.Tools.resolve("tinymce.util.VK"),Rn=Object.keys,In=(function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n]}("element","offset"),"undefined"!=typeof r.window?r.window:Function("return this;")()),Bn=function(e,n){var t=function(e,n){return H(e,n)}(e,n);if(t===undefined||null===t)throw new Error(e+" not available on this browser");return t},Pn=function(e){return function(e){return Bn("HTMLElement",e)}(H("ownerDocument.defaultView",e)).prototype.isPrototypeOf(e)},_n=tinymce.util.Tools.resolve("tinymce.dom.DomQuery"),Mn=tinymce.util.Tools.resolve("tinymce.util.Tools"),Un=function(e){return e&&3===e.nodeType},Fn=function(e){return e&&/^(OL|UL|DL)$/.test(e.nodeName)},jn=function(e){return e&&/^(OL|UL)$/.test(e.nodeName)},Hn=function(e){return e&&/^(DT|DD)$/.test(e.nodeName)},$n=function(e){return e&&/^(LI|DT|DD)$/.test(e.nodeName)},qn=function(e){return e&&/^(TH|TD)$/.test(e.nodeName)},Wn=$,Vn=function(e){return e.parentNode.firstChild===e},Kn=function(e,n){return n&&!!e.schema.getTextBlockElements()[n.nodeName]},Xn=function(e,n){return e&&e.nodeName in n},zn=function(e,n){return!!$(n)&&!(!e.isBlock(n.nextSibling)||$(n.previousSibling))},Qn=function(e,n,t){var r=e.isEmpty(n);return!(t&&0<e.select("span[data-mce-type=bookmark]",n).length)&&r},Yn=function(e,n){return e.isChildOf(n,e.getRoot())},Gn=function(e,n){var t=e.dom.getParents(n,"TD,TH");return 0<t.length?t[0]:e.getBody()},Jn=function(n,e){var t=g(e,function(e){return V(n,e).getOr(e)});return _n.unique(t)},Zn={isList:function(e){var n=q(e);return Pn(n)},getParentList:q,getSelectedSubLists:function(e){var n=q(e),t=e.selection.getSelectedBlocks();return function(e,n){return e&&1===n.length&&n[0]===e}(n,t)?function(e){return Mn.grep(e.querySelectorAll("ol,ul,dl"),function(e){return Fn(e)})}(n):Mn.grep(t,function(e){return Fn(e)&&n!==e})},getSelectedListItems:W,getClosestListRootElm:Gn,getSelectedDlItems:function(e){return v(W(e),Hn)},getSelectedListRoots:function(e){var n=function(e){var n=V(e,e.selection.getStart()),t=v(e.selection.getSelectedBlocks(),jn);return n.toArray().concat(t)}(e);return Jn(e,n)}},et=function(e){return e.getParam("lists_indent_on_tab",!0)},nt=function(e){var n=e.getParam("forced_root_block","p");return!1===n?"":!0===n?"p":n},tt=function(e){return e.getParam("forced_root_block_attrs",{})},rt=(Fe=Tn,function(e){return function(e){return e.dom().nodeType}(e)===Fe}),ot=function(n,t,r,e){return y(R(e),function(e){return(te(e)?ot:se)(n+1,t,r,e)})},it=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),ut=it.DOM,at=function(e,n,t){var r,o,i,u,a,s;for(i=ut.select('span[data-mce-type="bookmark"]',n),a=K(e,t),(r=ut.createRng()).setStartAfter(t),r.setEndAfter(n),u=(o=r.extractContents()).firstChild;u;u=u.firstChild)if("LI"===u.nodeName&&e.dom.isEmpty(u)){ut.remove(u);break}e.dom.isEmpty(o)||ut.insertAfter(o,n),ut.insertAfter(a,n),Qn(e.dom,t.parentNode)&&(s=t.parentNode,Mn.each(i,function(e){s.parentNode.insertBefore(e,t.parentNode)}),ut.remove(s)),ut.remove(t),Qn(e.dom,n)&&ut.remove(n)},st=function(e){var n=e.cloneRange(),t=pe(e.startContainer,e.startOffset);n.setStart(t.container,t.offset);var r=pe(e.endContainer,e.endOffset);return n.setEnd(r.container,r.offset),n},ct=tinymce.util.Tools.resolve("tinymce.dom.BookmarkManager"),ft=it.DOM,dt=function(o){function e(e){var n,t,r;t=o[e?"startContainer":"endContainer"],r=o[e?"startOffset":"endOffset"],1===t.nodeType&&(n=ft.create("span",{"data-mce-type":"bookmark"}),t.hasChildNodes()?(r=Math.min(r,t.childNodes.length-1),e?t.insertBefore(n,t.childNodes[r]):ft.insertAfter(n,t.childNodes[r])):t.appendChild(n),t=n,r=0),i[e?"startContainer":"endContainer"]=t,i[e?"startOffset":"endOffset"]=r}var i={};return e(!0),o.collapsed||e(),i},lt=function(o){function e(e){var n,t,r;n=r=o[e?"startContainer":"endContainer"],t=o[e?"startOffset":"endOffset"],n&&(1===n.nodeType&&(t=function(e){for(var n=e.parentNode.firstChild,t=0;n;){if(n===e)return t;1===n.nodeType&&"bookmark"===n.getAttribute("data-mce-type")||t++,n=n.nextSibling}return-1}(n),n=n.parentNode,ft.remove(r),!n.hasChildNodes()&&ft.isBlock(n)&&n.appendChild(ft.create("br"))),o[e?"startContainer":"endContainer"]=n,o[e?"startOffset":"endOffset"]=t)}e(!0),e();var n=ft.createRng();return n.setStart(o.startContainer,o.startOffset),o.endContainer&&n.setEnd(o.endContainer,o.endOffset),st(n)},mt=function(e,n){var t,r;if(t=n.nextSibling,De(e,n,t)){for(;r=t.firstChild;)n.appendChild(r);e.remove(t)}if(t=n.previousSibling,De(e,n,t)){for(;r=t.lastChild;)n.insertBefore(r,n.firstChild);e.remove(t)}},gt=function(e){return"list-style-type"in e},pt={toggleList:function(e,n,t){var r=Zn.getParentList(e),o=Zn.getSelectedSubLists(e);t=t||{},r&&0<o.length?Ee(e,r,o,n,t):function(e,n,t,r){if(n!==e.getBody())if(n)if(n.nodeName!==t||gt(r)||Se(n)){var o=dt(e.selection.getRng(!0));be(e.dom,n,r);var i=e.dom.rename(n,t);mt(e.dom,i),e.selection.setRng(lt(o)),j(e,Oe(t),i)}else ye(e);else Te(e,t,r),j(e,Oe(t),n)}(e,r,n,t)},mergeWithAdjacentLists:mt},vt=it.DOM,ht=function(n,e){Mn.each(Mn.grep(n.select("ol,ul",e)),function(e){we(n,e)})},Nt=function(n){n.on("keydown",function(e){e.keyCode===xn.BACKSPACE?_e(n,!1)&&e.preventDefault():e.keyCode===xn.DELETE&&_e(n,!0)&&e.preventDefault()})},yt=_e,St=function(n){return{backspaceDelete:function(e){yt(n,e)}}},Ot=function(t){t.on("BeforeExecCommand",function(e){var n=e.command.toLowerCase();"indent"===n?he(t):"outdent"===n&&Ne(t)}),t.addCommand("InsertUnorderedList",function(e,n){pt.toggleList(t,"UL",n)}),t.addCommand("InsertOrderedList",function(e,n){pt.toggleList(t,"OL",n)}),t.addCommand("InsertDefinitionList",function(e,n){pt.toggleList(t,"DL",n)}),t.addCommand("RemoveList",function(){ye(t)}),t.addQueryStateHandler("InsertUnorderedList",Me(t,"UL")),t.addQueryStateHandler("InsertOrderedList",Me(t,"OL")),t.addQueryStateHandler("InsertDefinitionList",Me(t,"DL"))},Ct=function(e){et(e)&&function(n){n.on("keydown",function(e){e.keyCode!==xn.TAB||xn.metaKeyPressed(e)||n.undoManager.transact(function(){(e.shiftKey?Ne(n):he(n))&&e.preventDefault()})})}(e),Nt(e)},bt=function(n){function e(e){return function(){return n.execCommand(e)}}var t,r,o;r="advlist",o=(t=n).settings.plugins?t.settings.plugins:"",-1===Mn.inArray(o.split(/[ ,]/),r)&&(n.ui.registry.addToggleButton("numlist",{icon:"ordered-list",active:!1,tooltip:"Numbered list",onAction:e("InsertOrderedList"),onSetup:Ue(n,"OL")}),n.ui.registry.addToggleButton("bullist",{icon:"unordered-list",active:!1,tooltip:"Bullet list",onAction:e("InsertUnorderedList"),onSetup:Ue(n,"UL")}))};!function Lt(){i.add("lists",function(e){return Ct(e),bt(e),Ot(e),St(e)})}()}(window);