<template>
  <div class="orders-page">
    <el-card>
      <template #header>
        <div class="page-header">
          <h2>订单管理</h2>
          <div class="header-actions">
            <el-button @click="exportOrders">
              <el-icon><Download /></el-icon>
              导出订单
            </el-button>
          </div>
        </div>
      </template>

      <!-- 订单统计 -->
      <div class="stats-section">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-value">{{ orderStats.total }}</div>
                <div class="stat-label">总订单数</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-value pending">{{ orderStats.pending }}</div>
                <div class="stat-label">待处理</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-value processing">{{ orderStats.processing }}</div>
                <div class="stat-label">处理中</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-value completed">{{ orderStats.completed }}</div>
                <div class="stat-label">已完成</div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 搜索和筛选 -->
      <div class="search-section">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-input
              v-model="searchForm.orderNo"
              placeholder="搜索订单号"
              clearable
              @change="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="4">
            <el-select
              v-model="searchForm.status"
              placeholder="订单状态"
              clearable
              @change="handleSearch"
            >
              <el-option label="全部状态" value="" />
              <el-option label="待付款" value="pending" />
              <el-option label="待发货" value="paid" />
              <el-option label="已发货" value="shipped" />
              <el-option label="已完成" value="completed" />
              <el-option label="已取消" value="cancelled" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-date-picker
              v-model="searchForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="handleSearch"
            />
          </el-col>
          <el-col :span="4">
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 订单列表 -->
      <el-table
        v-loading="loading"
        :data="orderList"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="orderNo" label="订单号" width="180" />
        <el-table-column label="商品信息" min-width="200">
          <template #default="{ row }">
            <div class="product-info">
              <el-image
                :src="row.productImage"
                style="width: 40px; height: 40px; margin-right: 10px"
                fit="cover"
              />
              <div>
                <div class="product-name">{{ row.productName }}</div>
                <div class="product-spec">{{ row.productSpec }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="quantity" label="数量" width="80" />
        <el-table-column prop="totalAmount" label="订单金额" width="100">
          <template #default="{ row }">
            <span class="amount">¥{{ row.totalAmount }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="customerName" label="客户" width="100" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="下单时间" width="150">
          <template #default="{ row }">
            {{ formatDate(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="viewOrder(row)">
              查看
            </el-button>
            <el-button
              v-if="row.status === 'paid'"
              type="success"
              size="small"
              @click="shipOrder(row)"
            >
              发货
            </el-button>
            <el-button
              v-if="row.status === 'pending'"
              type="danger"
              size="small"
              @click="cancelOrder(row)"
            >
              取消
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Download, Search } from '@element-plus/icons-vue'
import { createApiClient, merchantApi } from '../../../shared/api.js'
import { formatDate } from '../../../shared/utils.js'

const router = useRouter()
const api = createApiClient()

// 响应式数据
const loading = ref(false)
const orderList = ref([])
const selectedOrders = ref([])

// 订单统计
const orderStats = reactive({
  total: 0,
  pending: 0,
  processing: 0,
  completed: 0
})

// 搜索表单
const searchForm = reactive({
  orderNo: '',
  status: '',
  dateRange: null
})

// 分页数据
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 状态映射
const statusMap = {
  pending: { text: '待付款', type: 'warning' },
  paid: { text: '待发货', type: 'info' },
  shipped: { text: '已发货', type: 'primary' },
  completed: { text: '已完成', type: 'success' },
  cancelled: { text: '已取消', type: 'danger' }
}

// 获取状态文本
const getStatusText = (status) => {
  return statusMap[status]?.text || status
}

// 获取状态类型
const getStatusType = (status) => {
  return statusMap[status]?.type || 'info'
}

// 模拟订单数据
const mockOrderList = [
  {
    id: 1,
    orderNo: 'ORD202507070001',
    productName: '新鲜有机西红柿',
    productSpec: '500g/袋',
    productImage: 'https://via.placeholder.com/40x40?text=西红柿',
    quantity: 2,
    totalAmount: 25.60,
    customerName: '张三',
    status: 'paid',
    createTime: '2025-07-07 10:30:00'
  },
  {
    id: 2,
    orderNo: 'ORD202507070002',
    productName: '优质苹果',
    productSpec: '1kg/袋',
    productImage: 'https://via.placeholder.com/40x40?text=苹果',
    quantity: 1,
    totalAmount: 8.50,
    customerName: '李四',
    status: 'shipped',
    createTime: '2025-07-07 09:15:00'
  },
  {
    id: 3,
    orderNo: 'ORD202507070003',
    productName: '东北大米',
    productSpec: '5kg/袋',
    productImage: 'https://via.placeholder.com/40x40?text=大米',
    quantity: 1,
    totalAmount: 25.00,
    customerName: '王五',
    status: 'completed',
    createTime: '2025-07-06 16:45:00'
  },
  {
    id: 4,
    orderNo: 'ORD202507070004',
    productName: '新鲜猪肉',
    productSpec: '1kg',
    productImage: 'https://via.placeholder.com/40x40?text=猪肉',
    quantity: 1,
    totalAmount: 35.00,
    customerName: '赵六',
    status: 'pending',
    createTime: '2025-07-07 14:20:00'
  }
]

// 获取订单列表
const getOrderList = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      ...searchForm
    }

    const response = await api.get(merchantApi.orders.list, { params })

    if (response.code === 200) {
      orderList.value = response.data.records || mockOrderList
      pagination.total = response.data.total || mockOrderList.length
    } else {
      // 使用模拟数据
      orderList.value = mockOrderList
      pagination.total = mockOrderList.length
    }

    // 更新统计数据
    updateOrderStats()
  } catch (error) {
    console.error('获取订单列表失败:', error)
    // 使用模拟数据
    orderList.value = mockOrderList
    pagination.total = mockOrderList.length
    updateOrderStats()
  } finally {
    loading.value = false
  }
}

// 更新订单统计
const updateOrderStats = () => {
  const stats = {
    total: orderList.value.length,
    pending: 0,
    processing: 0,
    completed: 0
  }

  orderList.value.forEach(order => {
    if (order.status === 'pending') stats.pending++
    else if (order.status === 'paid' || order.status === 'shipped') stats.processing++
    else if (order.status === 'completed') stats.completed++
  })

  Object.assign(orderStats, stats)
}

// 搜索处理
const handleSearch = () => {
  pagination.page = 1
  getOrderList()
}

// 重置搜索
const resetSearch = () => {
  Object.assign(searchForm, {
    orderNo: '',
    status: '',
    dateRange: null
  })
  handleSearch()
}

// 选择变化
const handleSelectionChange = (selection) => {
  selectedOrders.value = selection
}

// 分页处理
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.page = 1
  getOrderList()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  getOrderList()
}

// 操作方法
const viewOrder = (row) => {
  router.push(`/orders/detail/${row.id}`)
}

const shipOrder = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要发货订单"${row.orderNo}"吗？`,
      '发货确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await api.put(merchantApi.orders.updateStatus, {
      id: row.id,
      status: 'shipped'
    })

    if (response.code === 200) {
      ElMessage.success('订单已发货')
      getOrderList()
    } else {
      ElMessage.error('发货失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('发货失败:', error)
      ElMessage.error('发货失败')
    }
  }
}

const cancelOrder = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要取消订单"${row.orderNo}"吗？`,
      '取消确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await api.put(merchantApi.orders.updateStatus, {
      id: row.id,
      status: 'cancelled'
    })

    if (response.code === 200) {
      ElMessage.success('订单已取消')
      getOrderList()
    } else {
      ElMessage.error('取消失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消失败:', error)
      ElMessage.error('取消失败')
    }
  }
}

const exportOrders = () => {
  ElMessage.info('导出功能开发中...')
}

// 组件挂载时获取数据
onMounted(() => {
  getOrderList()
})
</script>

<style scoped>
.orders-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0;
}

.stats-section {
  margin: 20px 0;
}

.stat-card {
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  padding: 10px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
}

.stat-value.pending {
  color: #e6a23c;
}

.stat-value.processing {
  color: #409eff;
}

.stat-value.completed {
  color: #67c23a;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-top: 5px;
}

.search-section {
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 4px;
  margin-bottom: 20px;
}

.product-info {
  display: flex;
  align-items: center;
}

.product-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.product-spec {
  font-size: 12px;
  color: #999;
}

.amount {
  color: #e6a23c;
  font-weight: bold;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

:deep(.el-table .el-table__cell) {
  padding: 8px 0;
}

:deep(.el-image) {
  border-radius: 4px;
}
</style>
