<template>
    <div class="loginIn">
        <el-form class="login-form" label-position="left">
            <div class="title-container">
                <h3 class="title">农产品销售系统登录</h3>
            </div>
            <el-form-item class="input-item">
                <span class="svg-container"><svg-icon icon-class="user" /></span>
                <el-input placeholder="请输入用户名" name="username" type="text" v-model="rulesForm.username" />
            </el-form-item>
            <el-form-item class="input-item">
                <span class="svg-container"><svg-icon icon-class="password" /></span>
                <el-input placeholder="请输入密码" name="password" type="password" v-model="rulesForm.password" />
            </el-form-item>
            <div v-if="roleOptions.length>1" class="role-selector">
                <div class="role-title">角色：</div>
                <div class="role-options-wrapper">
                    <el-radio
                        @change="menuChange"
                        v-for="item in roleOptions"
                        v-bind:key="item.value"
                        v-model="rulesForm.role"
                        :label="item.value"
                    >{{item.key}}</el-radio>
                </div>
            </div>
            <el-button type="primary" @click="login()" class="login-button">登录</el-button>
            <el-form-item class="register-area">
                <div class="register-link" @click="register('shangjia')">商家注册</div>
            </el-form-item>
        </el-form>
    </div>
</template>
<script>

    import menu from "@/utils/menu";

    export default {
        data() {
            return {
                rulesForm: {
                    username: "",
                    password: "",
                    role: "",
                    code: '',
                },
                menus: [],
                roleOptions: [],
                tableName: "",
                codes: [{
                    num: 1,
                    color: '#000',
                    rotate: '10deg',
                    size: '16px'
                },{
                    num: 2,
                    color: '#000',
                    rotate: '10deg',
                    size: '16px'
                },{
                    num: 3,
                    color: '#000',
                    rotate: '10deg',
                    size: '16px'
                },{
                    num: 4,
                    color: '#000',
                    rotate: '10deg',
                    size: '16px'
                }],
            };
        },
        mounted() {
            let menus = menu.list();
            this.menus = menus;
            for (let i = 0; i < this.menus.length; i++) {
                if (this.menus[i].hasBackLogin=='是') {
                    let menuItem={};
                    menuItem["key"]=this.menus[i].roleName;
                    menuItem["value"]=this.menus[i].tableName;
                    this.roleOptions.push(menuItem);
                }
            }
        },
        created() {
            this.getRandCode()

        },
        methods: {
            menuChange(role){
            },
            register(tableName){
                this.$storage.set("loginTable", tableName);
                this.$router.push({path:'/register'})
            },
            // 登陆
            login() {
                let code = ''
                for(let i in this.codes) {
                    code += this.codes[i].num
                }
                if ('0' == '1' && !this.rulesForm.code) {
                    this.$message.error("请输入验证码");
                    return;
                }
                if ('0' == '1' && this.rulesForm.code.toLowerCase() != code.toLowerCase()) {
                    this.$message.error("验证码输入有误");
                    this.getRandCode()
                    return;
                }
                if (!this.rulesForm.username) {
                    this.$message.error("请输入用户名");
                    return;
                }
                if (!this.rulesForm.password) {
                    this.$message.error("请输入密码");
                    return;
                }
                if(this.roleOptions.length>1) {
                    console.log("1")
                    if (!this.rulesForm.role) {
                        this.$message.error("请选择角色");
                        return;
                    }
                    let menus = this.menus;
                    for (let i = 0; i < menus.length; i++) {
                        if (menus[i].tableName == this.rulesForm.role) {
                            this.tableName = menus[i].tableName;
                            this.rulesForm.role = menus[i].roleName;
                        }
                    }
                } else {
                    this.tableName = this.roleOptions[0].value;
                    this.rulesForm.role = this.roleOptions[0].key;
                }
                this.$http({
                    url: `${this.tableName}/login?username=${this.rulesForm.username}&password=${this.rulesForm.password}`,
                    method: "post"
                }).then(({ data }) => {
                    if (data && data.code === 0) {
                        this.$storage.set("Token", data.token);
                        this.$storage.set("userId", data.userId);
                        this.$storage.set("role", this.rulesForm.role);
                        this.$storage.set("sessionTable", this.tableName);
                        this.$storage.set("adminName", this.rulesForm.username);
                        this.$router.replace({ path: "/index/" });
                    } else {
                        this.$message.error(data.msg);
                    }
                });
            },
            getRandCode(len = 4){
                this.randomString(len)
            },
            randomString(len = 4) {
                let chars = [
                    "a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k",
                    "l", "m", "n", "o", "p", "q", "r", "s", "t", "u", "v",
                    "w", "x", "y", "z", "A", "B", "C", "D", "E", "F", "G",
                    "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R",
                    "S", "T", "U", "V", "W", "X", "Y", "Z", "0", "1", "2",
                    "2", "4", "5", "6", "7", "8", "9"
                ]
                let colors = ["0", "1", "2","2", "4", "5", "6", "7", "8", "9", "a", "b", "c", "d", "e", "f"]
                let sizes = ['14', '15', '16', '17', '18']

                let output = [];
                for (let i = 0; i < len; i++) {
                    // 随机验证码
                    let key = Math.floor(Math.random()*chars.length)
                    this.codes[i].num = chars[key]
                    // 随机验证码颜色
                    let code = '#'
                    for (let j = 0; j < 6; j++) {
                        let key = Math.floor(Math.random()*colors.length)
                        code += colors[key]
                    }
                    this.codes[i].color = code
                    // 随机验证码方向
                    let rotate = Math.floor(Math.random()*60)
                    let plus = Math.floor(Math.random()*2)
                    if(plus == 1) rotate = '-'+rotate
                    this.codes[i].rotate = 'rotate('+rotate+'deg)'
                    // 随机验证码字体大小
                    let size = Math.floor(Math.random()*sizes.length)
                    this.codes[i].size = sizes[size]+'px'
                }
            },
        }
    };
</script>
<style lang="scss" scoped>
   html, body {
       margin: 0;
       padding: 0;
       height: 100%;
       width: 100%;
       overflow: hidden;
   }
   
   .loginIn {
       height: 100vh;
       width: 100vw;
       background-repeat: no-repeat;
       background-position: center center;
       background-size: cover;
       background-image: url(/nongchanpinxiaoshou/upload/nongchanpin-bg.jpg);
       //src\assets\img\nongchanpin-bg.jpg
       position: relative;
       display: flex;
       justify-content: center;
       align-items: center;
   }
   
   /* 应用样式到表单，替代登录容器 */
   .login-form {
       width: 450px;
       padding: 40px;
       border-radius: 8px;
       background-color: #fff;
       box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
       z-index: 10;
       max-width: 90%;
       position: absolute;
       top: 50%;
       left: 50%;
       transform: translate(-50%, -50%);
   }
   
   .title-container {
       text-align: center;
       margin-bottom: 30px;
       
       .title {
           font-size: 26px;
           color: #67a3ff;
           margin: 0;
           font-weight: bold;
       }
   }
   
   .input-item {
       margin-bottom: 20px;
       border-radius: 4px;
       background-color: #f5f7fa;
       
       .svg-container {
           display: inline-block;
           padding: 0 15px;
           color: #889aa4;
           vertical-align: middle;
           width: 30px;
           line-height: 40px;
           text-align: center;
       }
       
       .el-input {
           display: inline-block;
           width: calc(100% - 60px);
           
           >>> input {
               background-color: transparent;
               border: none;
               border-radius: 0;
               padding: 12px 5px;
               color: #333;
               height: 40px;
               
               &:focus {
                   box-shadow: none !important;
               }
           }
       }
   }
   
   .role-selector {
       margin-bottom: 20px;
       display: flex;
       align-items: center;
       background: transparent !important;
       box-shadow: none !important;
       border: none !important;
       padding: 0;
       
       .role-title {
           margin-right: 15px;
           font-weight: bold;
       }
       
       .role-options-wrapper {
           flex: 1;
           background: transparent !important;
       }
   }
   
   .login-button {
       width: 100%;
       height: 50px;
       font-size: 18px;
       letter-spacing: 2px;
       margin-bottom: 20px;
       border: none;
       
       &:hover {
           opacity: 0.9;
       }
   }
   
   .register-area {
       margin-bottom: 0;
       text-align: center;
       
       .register-link {
           color: #67a3ff;
           font-size: 14px;
           cursor: pointer;
           
           &:hover {
               text-decoration: underline;
           }
       }
   }
</style>

<style>
/* 页面内任何登录容器都不显示背景和边框 */
div.login-container {
    background-color: transparent !important;
    box-shadow: none !important;
    border: none !important;
    padding: 0 !important;
}

/* 强制页面撑满并居中 */
#app {
    width: 100%;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>
