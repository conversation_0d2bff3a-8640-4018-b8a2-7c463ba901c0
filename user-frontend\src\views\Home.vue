<template>
  <div class="home">
    <!-- 轮播图 -->
    <section class="hero-section">
      <el-carousel height="400px" indicator-position="outside">
        <el-carousel-item v-for="(banner, index) in banners" :key="index">
          <div class="banner-item" :style="{ backgroundImage: `url(${banner.image})` }">
            <div class="banner-content">
              <h2>{{ banner.title }}</h2>
              <p>{{ banner.subtitle }}</p>
              <el-button type="primary" size="large" @click="router.push('/products')">
                立即购买
              </el-button>
            </div>
          </div>
        </el-carousel-item>
      </el-carousel>
    </section>

    <!-- 商品分类 -->
    <section class="categories-section">
      <div class="section-header">
        <h2>商品分类</h2>
        <p>精选优质农产品，新鲜直达</p>
      </div>
      <div class="categories-grid">
        <div
          v-for="category in categories"
          :key="category.name"
          class="category-item"
          @click="goToCategory(category.name)"
        >
          <div class="category-icon">
            <img :src="category.icon" :alt="category.name">
          </div>
          <h3>{{ category.name }}</h3>
          <p>{{ category.description }}</p>
        </div>
      </div>
    </section>

    <!-- 热门商品 */
    <section class="hot-products-section">
      <div class="section-header">
        <h2>热门商品</h2>
        <p>精选热销农产品</p>
        <router-link to="/products" class="more-link">查看更多 →</router-link>
      </div>
      <div class="products-grid" v-loading="loading">
        <div
          v-for="product in hotProducts"
          :key="product.id"
          class="product-card"
          @click="goToProduct(product.id)"
        >
          <div class="product-image">
            <img :src="getImageUrl(product.nongchanpinPhoto)" :alt="product.nongchanpinName">
            <div class="product-overlay">
              <el-button
                type="primary"
                icon="ShoppingCart"
                circle
                @click.stop="addToCart(product.id)"
              />
            </div>
          </div>
          <div class="product-info">
            <h3 class="product-name">{{ product.nongchanpinName }}</h3>
            <p class="product-desc">{{ product.nongchanpinContent }}</p>
            <div class="product-price">
              <span class="current-price">¥{{ product.nongchanpinNewMoney }}</span>
              <span v-if="product.nongchanpinOldMoney" class="old-price">
                ¥{{ product.nongchanpinOldMoney }}
              </span>
            </div>
            <div class="product-meta">
              <span class="product-location">{{ product.nongchanpinAddress }}</span>
              <span class="product-stock">库存: {{ product.nongchanpinKucunNumber }}</span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 优势特色 -->
    <section class="features-section">
      <div class="section-header">
        <h2>平台优势</h2>
        <p>为什么选择我们</p>
      </div>
      <div class="features-grid">
        <div v-for="feature in features" :key="feature.title" class="feature-item">
          <div class="feature-icon">
            <el-icon size="48" :color="feature.color">
              <component :is="feature.icon" />
            </el-icon>
          </div>
          <h3>{{ feature.title }}</h3>
          <p>{{ feature.description }}</p>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { createApiClient, userApi } from '../../shared/api.js'
import { getImageUrl } from '../../shared/utils.js'
import { useCartStore } from '../stores/cart.js'

const router = useRouter()
const cartStore = useCartStore()

// 响应式数据
const loading = ref(false)
const hotProducts = ref([])

// 轮播图数据
const banners = ref([
  {
    title: '新鲜农产品',
    subtitle: '直接从农场到餐桌，保证新鲜品质',
    image: '/images/banner1.jpg'
  },
  {
    title: '有机蔬菜',
    subtitle: '无农药残留，健康安全',
    image: '/images/banner2.jpg'
  },
  {
    title: '时令水果',
    subtitle: '当季水果，营养丰富',
    image: '/images/banner3.jpg'
  }
])

// 商品分类
const categories = ref([
  {
    name: '蔬菜类',
    description: '新鲜蔬菜，营养丰富',
    icon: '/images/category-vegetable.png'
  },
  {
    name: '水果类',
    description: '时令水果，香甜可口',
    icon: '/images/category-fruit.png'
  },
  {
    name: '粮食类',
    description: '优质粮食，健康主食',
    icon: '/images/category-grain.png'
  },
  {
    name: '其他类',
    description: '特色农产品',
    icon: '/images/category-other.png'
  }
])

// 平台特色
const features = ref([
  {
    title: '品质保证',
    description: '严格筛选，确保每一件商品都是优质农产品',
    icon: 'Medal',
    color: '#67c23a'
  },
  {
    title: '新鲜直达',
    description: '从农场直接配送，保证新鲜度',
    icon: 'Truck',
    color: '#409eff'
  },
  {
    title: '价格实惠',
    description: '去除中间环节，让利消费者',
    icon: 'Money',
    color: '#e6a23c'
  },
  {
    title: '安全可靠',
    description: '全程可追溯，食品安全有保障',
    icon: 'Shield',
    color: '#f56c6c'
  }
])

// 加载热门商品
const loadHotProducts = async () => {
  try {
    loading.value = true
    const apiClient = createApiClient()
    const response = await userApi.products.list(apiClient, {
      page: 1,
      limit: 8,
      sort: 'clicknum', // 按点击量排序
      order: 'desc'
    })
    
    if (response.code === 0) {
      hotProducts.value = response.data.list || []
    }
  } catch (error) {
    console.error('加载热门商品失败:', error)
  } finally {
    loading.value = false
  }
}

// 跳转到商品分类
const goToCategory = (categoryName) => {
  router.push({
    path: '/products',
    query: { category: categoryName }
  })
}

// 跳转到商品详情
const goToProduct = (productId) => {
  router.push(`/product/${productId}`)
}

// 添加到购物车
const addToCart = async (productId) => {
  const success = await cartStore.addToCart(productId, 1)
  if (success) {
    ElMessage.success('已添加到购物车')
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadHotProducts()
})
</script>

<style scoped>
.home {
  min-height: 100vh;
}

/* 轮播图样式 */
.hero-section {
  margin-bottom: 60px;
}

.banner-item {
  height: 400px;
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.banner-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
}

.banner-content {
  text-align: center;
  color: white;
  z-index: 1;
}

.banner-content h2 {
  font-size: 48px;
  margin-bottom: 16px;
  font-weight: 600;
}

.banner-content p {
  font-size: 18px;
  margin-bottom: 32px;
  opacity: 0.9;
}

/* 通用section样式 */
.section-header {
  text-align: center;
  margin-bottom: 40px;
}

.section-header h2 {
  font-size: 32px;
  color: #333;
  margin-bottom: 8px;
}

.section-header p {
  font-size: 16px;
  color: #666;
}

.more-link {
  color: #409eff;
  text-decoration: none;
  font-size: 14px;
  margin-left: 20px;
}

.more-link:hover {
  text-decoration: underline;
}

/* 商品分类样式 */
.categories-section {
  margin-bottom: 80px;
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
  max-width: 1000px;
  margin: 0 auto;
}

.category-item {
  text-align: center;
  padding: 30px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.3s ease;
}

.category-item:hover {
  transform: translateY(-5px);
}

.category-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 20px;
  background: #f8f9fa;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.category-icon img {
  width: 50px;
  height: 50px;
}

.category-item h3 {
  font-size: 20px;
  color: #333;
  margin-bottom: 8px;
}

.category-item p {
  color: #666;
  font-size: 14px;
}

/* 热门商品样式 */
.hot-products-section {
  margin-bottom: 80px;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 24px;
}

.product-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.3s ease;
}

.product-card:hover {
  transform: translateY(-5px);
}

.product-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.product-card:hover .product-overlay {
  opacity: 1;
}

.product-info {
  padding: 20px;
}

.product-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-desc {
  font-size: 14px;
  color: #666;
  margin-bottom: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-price {
  margin-bottom: 12px;
}

.current-price {
  font-size: 20px;
  font-weight: 600;
  color: #e74c3c;
}

.old-price {
  font-size: 14px;
  color: #999;
  text-decoration: line-through;
  margin-left: 8px;
}

.product-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #999;
}

/* 特色功能样式 */
.features-section {
  background: #f8f9fa;
  padding: 60px 0;
  margin: 0 -20px;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 20px;
}

.feature-item {
  text-align: center;
}

.feature-icon {
  margin-bottom: 20px;
}

.feature-item h3 {
  font-size: 20px;
  color: #333;
  margin-bottom: 12px;
}

.feature-item p {
  color: #666;
  line-height: 1.6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .banner-content h2 {
    font-size: 32px;
  }
  
  .banner-content p {
    font-size: 16px;
  }
  
  .section-header h2 {
    font-size: 24px;
  }
  
  .categories-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
  }
  
  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 16px;
  }
}
</style>
