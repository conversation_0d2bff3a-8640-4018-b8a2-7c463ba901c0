<template>
  <div class="profile-page">
    <el-row :gutter="20">
      <!-- 基本信息 -->
      <el-col :span="12">
        <el-card class="profile-card">
          <template #header>
            <h3>基本信息</h3>
          </template>
          <el-form
            ref="profileFormRef"
            :model="profileForm"
            :rules="profileRules"
            label-width="100px"
          >
            <el-form-item label="头像">
              <div class="avatar-section">
                <el-avatar :size="80" :src="profileForm.avatar" />
                <el-upload
                  class="avatar-uploader"
                  :action="uploadUrl"
                  :show-file-list="false"
                  :on-success="handleAvatarSuccess"
                  :before-upload="beforeAvatarUpload"
                >
                  <el-button size="small" type="primary">更换头像</el-button>
                </el-upload>
              </div>
            </el-form-item>
            <el-form-item label="商家名称" prop="merchantName">
              <el-input v-model="profileForm.merchantName" />
            </el-form-item>
            <el-form-item label="联系人" prop="contactName">
              <el-input v-model="profileForm.contactName" />
            </el-form-item>
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="profileForm.phone" />
            </el-form-item>
            <el-form-item label="邮箱地址" prop="email">
              <el-input v-model="profileForm.email" />
            </el-form-item>
            <el-form-item label="经营地址" prop="address">
              <el-input v-model="profileForm.address" type="textarea" :rows="3" />
            </el-form-item>
            <el-form-item label="商家简介" prop="description">
              <el-input v-model="profileForm.description" type="textarea" :rows="4" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="saveProfile">保存信息</el-button>
              <el-button @click="resetProfile">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <!-- 店铺设置 -->
      <el-col :span="12">
        <el-card class="shop-card">
          <template #header>
            <h3>店铺设置</h3>
          </template>
          <el-form
            ref="shopFormRef"
            :model="shopForm"
            :rules="shopRules"
            label-width="100px"
          >
            <el-form-item label="店铺名称" prop="shopName">
              <el-input v-model="shopForm.shopName" />
            </el-form-item>
            <el-form-item label="店铺Logo">
              <div class="logo-section">
                <el-image
                  :src="shopForm.logo"
                  style="width: 100px; height: 100px"
                  fit="cover"
                />
                <el-upload
                  class="logo-uploader"
                  :action="uploadUrl"
                  :show-file-list="false"
                  :on-success="handleLogoSuccess"
                  :before-upload="beforeLogoUpload"
                >
                  <el-button size="small" type="primary">更换Logo</el-button>
                </el-upload>
              </div>
            </el-form-item>
            <el-form-item label="营业时间" prop="businessHours">
              <el-time-picker
                v-model="shopForm.businessHours"
                is-range
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                format="HH:mm"
                value-format="HH:mm"
              />
            </el-form-item>
            <el-form-item label="配送范围" prop="deliveryRange">
              <el-input-number
                v-model="shopForm.deliveryRange"
                :min="1"
                :max="50"
                controls-position="right"
              />
              <span style="margin-left: 8px">公里</span>
            </el-form-item>
            <el-form-item label="起送金额" prop="minOrderAmount">
              <el-input-number
                v-model="shopForm.minOrderAmount"
                :min="0"
                :precision="2"
                controls-position="right"
              />
              <span style="margin-left: 8px">元</span>
            </el-form-item>
            <el-form-item label="配送费" prop="deliveryFee">
              <el-input-number
                v-model="shopForm.deliveryFee"
                :min="0"
                :precision="2"
                controls-position="right"
              />
              <span style="margin-left: 8px">元</span>
            </el-form-item>
            <el-form-item label="店铺状态" prop="status">
              <el-switch
                v-model="shopForm.status"
                active-text="营业中"
                inactive-text="休息中"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="saveShopSettings">保存设置</el-button>
              <el-button @click="resetShopSettings">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
    </el-row>

    <!-- 安全设置 -->
    <el-card class="security-card">
      <template #header>
        <h3>安全设置</h3>
      </template>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form
            ref="passwordFormRef"
            :model="passwordForm"
            :rules="passwordRules"
            label-width="100px"
          >
            <el-form-item label="当前密码" prop="currentPassword">
              <el-input
                v-model="passwordForm.currentPassword"
                type="password"
                show-password
              />
            </el-form-item>
            <el-form-item label="新密码" prop="newPassword">
              <el-input
                v-model="passwordForm.newPassword"
                type="password"
                show-password
              />
            </el-form-item>
            <el-form-item label="确认密码" prop="confirmPassword">
              <el-input
                v-model="passwordForm.confirmPassword"
                type="password"
                show-password
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="changePassword">修改密码</el-button>
              <el-button @click="resetPasswordForm">重置</el-button>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="12">
          <div class="security-info">
            <h4>账户安全</h4>
            <div class="security-item">
              <span>登录保护：</span>
              <el-tag type="success">已开启</el-tag>
            </div>
            <div class="security-item">
              <span>最后登录：</span>
              <span>{{ lastLoginTime }}</span>
            </div>
            <div class="security-item">
              <span>登录IP：</span>
              <span>{{ lastLoginIp }}</span>
            </div>
            <div class="security-item">
              <span>密码强度：</span>
              <el-tag :type="passwordStrengthType">{{ passwordStrengthText }}</el-tag>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { createApiClient, merchantApi } from '../../../shared/api.js'

const api = createApiClient()

// 表单引用
const profileFormRef = ref(null)
const shopFormRef = ref(null)
const passwordFormRef = ref(null)

// 上传地址
const uploadUrl = ref('http://localhost:8080/nongchanpinxiaoshou/api/upload')

// 基本信息表单
const profileForm = reactive({
  avatar: 'https://via.placeholder.com/80x80?text=头像',
  merchantName: '绿色农场',
  contactName: '张三',
  phone: '***********',
  email: '<EMAIL>',
  address: '北京市朝阳区农业园区123号',
  description: '专业提供优质有机农产品，致力于为消费者提供健康、安全的绿色食品。'
})

// 店铺设置表单
const shopForm = reactive({
  shopName: '绿色农场直营店',
  logo: 'https://via.placeholder.com/100x100?text=Logo',
  businessHours: ['08:00', '20:00'],
  deliveryRange: 10,
  minOrderAmount: 50.00,
  deliveryFee: 5.00,
  status: true
})

// 密码修改表单
const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 安全信息
const lastLoginTime = ref('2025-07-07 14:30:25')
const lastLoginIp = ref('*************')

// 表单验证规则
const profileRules = {
  merchantName: [
    { required: true, message: '请输入商家名称', trigger: 'blur' }
  ],
  contactName: [
    { required: true, message: '请输入联系人', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
}

const shopRules = {
  shopName: [
    { required: true, message: '请输入店铺名称', trigger: 'blur' }
  ],
  businessHours: [
    { required: true, message: '请选择营业时间', trigger: 'change' }
  ]
}

const passwordRules = {
  currentPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 密码强度计算
const passwordStrengthType = computed(() => {
  const password = passwordForm.newPassword
  if (!password) return 'info'

  let score = 0
  if (password.length >= 8) score++
  if (/[a-z]/.test(password)) score++
  if (/[A-Z]/.test(password)) score++
  if (/\d/.test(password)) score++
  if (/[!@#$%^&*]/.test(password)) score++

  if (score <= 2) return 'danger'
  if (score <= 3) return 'warning'
  return 'success'
})

const passwordStrengthText = computed(() => {
  const type = passwordStrengthType.value
  switch (type) {
    case 'danger': return '弱'
    case 'warning': return '中'
    case 'success': return '强'
    default: return '未设置'
  }
})

// 头像上传处理
const handleAvatarSuccess = (response) => {
  if (response.code === 200) {
    profileForm.avatar = response.data.url
    ElMessage.success('头像上传成功')
  } else {
    ElMessage.error('头像上传失败')
  }
}

const beforeAvatarUpload = (file) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPG) {
    ElMessage.error('头像图片只能是 JPG/PNG 格式!')
  }
  if (!isLt2M) {
    ElMessage.error('头像图片大小不能超过 2MB!')
  }
  return isJPG && isLt2M
}

// Logo上传处理
const handleLogoSuccess = (response) => {
  if (response.code === 200) {
    shopForm.logo = response.data.url
    ElMessage.success('Logo上传成功')
  } else {
    ElMessage.error('Logo上传失败')
  }
}

const beforeLogoUpload = (file) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt5M = file.size / 1024 / 1024 < 5

  if (!isJPG) {
    ElMessage.error('Logo图片只能是 JPG/PNG 格式!')
  }
  if (!isLt5M) {
    ElMessage.error('Logo图片大小不能超过 5MB!')
  }
  return isJPG && isLt5M
}

// 保存基本信息
const saveProfile = async () => {
  try {
    await profileFormRef.value.validate()

    const response = await api.put(merchantApi.profile.update, profileForm)

    if (response.code === 200) {
      ElMessage.success('基本信息保存成功')
    } else {
      ElMessage.error('保存失败：' + response.message)
    }
  } catch (error) {
    console.error('保存基本信息失败:', error)
    ElMessage.error('保存失败')
  }
}

// 重置基本信息
const resetProfile = () => {
  profileFormRef.value?.resetFields()
}

// 保存店铺设置
const saveShopSettings = async () => {
  try {
    await shopFormRef.value.validate()

    const response = await api.put(merchantApi.shop.update, shopForm)

    if (response.code === 200) {
      ElMessage.success('店铺设置保存成功')
    } else {
      ElMessage.error('保存失败：' + response.message)
    }
  } catch (error) {
    console.error('保存店铺设置失败:', error)
    ElMessage.error('保存失败')
  }
}

// 重置店铺设置
const resetShopSettings = () => {
  shopFormRef.value?.resetFields()
}

// 修改密码
const changePassword = async () => {
  try {
    await passwordFormRef.value.validate()

    const response = await api.put(merchantApi.profile.changePassword, {
      currentPassword: passwordForm.currentPassword,
      newPassword: passwordForm.newPassword
    })

    if (response.code === 200) {
      ElMessage.success('密码修改成功')
      resetPasswordForm()
    } else {
      ElMessage.error('密码修改失败：' + response.message)
    }
  } catch (error) {
    console.error('修改密码失败:', error)
    ElMessage.error('密码修改失败')
  }
}

// 重置密码表单
const resetPasswordForm = () => {
  Object.assign(passwordForm, {
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  })
  passwordFormRef.value?.clearValidate()
}

// 加载用户信息
const loadUserInfo = async () => {
  try {
    const response = await api.get(merchantApi.profile.info)

    if (response.code === 200) {
      Object.assign(profileForm, response.data.profile || profileForm)
      Object.assign(shopForm, response.data.shop || shopForm)
      lastLoginTime.value = response.data.lastLoginTime || lastLoginTime.value
      lastLoginIp.value = response.data.lastLoginIp || lastLoginIp.value
    }
  } catch (error) {
    console.error('加载用户信息失败:', error)
    // 使用默认数据
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadUserInfo()
})
</script>

<style scoped>
.profile-page {
  padding: 20px;
}

.profile-card,
.shop-card,
.security-card {
  margin-bottom: 20px;
}

.avatar-section {
  display: flex;
  align-items: center;
  gap: 20px;
}

.avatar-uploader {
  display: flex;
  align-items: center;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 20px;
}

.logo-uploader {
  display: flex;
  align-items: center;
}

.security-info {
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.security-info h4 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 16px;
}

.security-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding: 10px 0;
  border-bottom: 1px solid #eee;
}

.security-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.security-item span:first-child {
  font-weight: 500;
  color: #666;
}

:deep(.el-card__header) {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

:deep(.el-card__header h3) {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-avatar) {
  border: 2px solid #e9ecef;
}

:deep(.el-image) {
  border: 2px solid #e9ecef;
  border-radius: 8px;
}

@media (max-width: 768px) {
  .profile-page {
    padding: 10px;
  }

  .avatar-section,
  .logo-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .security-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
}
</style>
