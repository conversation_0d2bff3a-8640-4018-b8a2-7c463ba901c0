# 农产品销售系统 - 管理员后台

## 项目简介

这是农产品销售系统的管理员后台前端项目，采用 Vue 3 + Vite + Element Plus 技术栈开发，为系统管理员提供完整的后台管理功能。

## 技术栈

- **Vue 3.3.4** - 渐进式 JavaScript 框架
- **Vite 4.4.5** - 现代化的前端构建工具
- **Element Plus 2.3.8** - 基于 Vue 3 的组件库
- **Vue Router 4.2.4** - Vue.js 官方路由管理器
- **Pinia 2.1.6** - Vue 的状态管理库
- **Axios 1.4.0** - HTTP 客户端
- **ECharts 5.4.3** - 数据可视化图表库

## 功能模块

### 1. 管理员登录和权限系统
- 管理员登录认证
- JWT Token 管理
- 路由权限控制
- 用户信息管理

### 2. 仪表盘
- 系统概览统计
- 用户增长趋势图表
- 订单统计图表
- 待处理事项提醒
- 系统日志展示

### 3. 用户管理
- 用户列表查看
- 用户信息编辑
- 用户状态管理（启用/禁用）
- 用户搜索和筛选
- 批量操作（启用/禁用/删除）
- 新增用户

### 4. 商家管理
- 商家申请审核
- 商家信息查看
- 商家资质管理
- 商家状态控制
- 营业执照查看
- 审核流程管理

### 5. 内容管理
- 系统公告管理
- 商品分类管理
- 系统配置管理
- 内容发布和编辑

### 6. 系统监控
- 服务器状态监控
- 数据库状态监控
- 系统性能图表
- 操作日志查看
- 系统配置管理

## 项目结构

```
admin-frontend/
├── public/                 # 静态资源
├── src/
│   ├── assets/            # 资源文件
│   ├── components/        # 公共组件
│   ├── router/           # 路由配置
│   │   └── index.js      # 路由定义
│   ├── stores/           # 状态管理
│   │   └── auth.js       # 认证状态管理
│   ├── views/            # 页面组件
│   │   ├── Dashboard.vue # 仪表盘
│   │   ├── Login.vue     # 登录页
│   │   ├── Layout.vue    # 主布局
│   │   ├── Users.vue     # 用户管理
│   │   ├── Merchants.vue # 商家管理
│   │   ├── Content.vue   # 内容管理
│   │   └── System.vue    # 系统设置
│   ├── App.vue           # 根组件
│   └── main.js           # 入口文件
├── package.json          # 项目配置
├── vite.config.js        # Vite 配置
└── README.md            # 项目说明
```

## 安装和运行

### 环境要求
- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖
```bash
cd admin-frontend
npm install
```

### 开发环境运行
```bash
npm run dev
```
访问地址：http://localhost:3005

### 生产环境构建
```bash
npm run build
```

### 预览生产构建
```bash
npm run preview
```

## 默认登录信息

- **用户名**: admin
- **密码**: 123456

## 开发记录

### 2025-07-07
1. **项目初始化**
   - 创建 Vue 3 + Vite 项目
   - 配置 Element Plus 组件库
   - 设置项目基础结构

2. **认证系统开发**
   - 实现管理员登录页面
   - 配置 Pinia 状态管理
   - 设置路由权限控制
   - 实现 JWT Token 管理

3. **主布局开发**
   - 创建响应式侧边栏导航
   - 实现面包屑导航
   - 设计用户信息展示
   - 配置路由出口

4. **仪表盘开发**
   - 实现统计卡片展示
   - 集成 ECharts 图表
   - 添加待处理事项
   - 创建系统日志时间线

5. **用户管理模块**
   - 实现用户列表展示
   - 添加搜索和筛选功能
   - 实现用户CRUD操作
   - 支持批量操作

6. **商家管理模块**
   - 实现商家审核流程
   - 添加商家详情查看
   - 支持营业执照查看
   - 实现状态管理

7. **内容管理模块**
   - 实现系统公告管理
   - 添加商品分类管理
   - 创建系统配置界面
   - 支持内容发布

8. **系统监控模块**
   - 实现服务器状态监控
   - 添加数据库状态展示
   - 创建性能监控图表
   - 实现操作日志管理

## API 接口

项目使用模拟数据进行开发，实际部署时需要对接后端 API。主要接口包括：

- 认证接口：`/admin/auth/*`
- 用户管理：`/admin/users/*`
- 商家管理：`/admin/merchants/*`
- 系统监控：`/admin/system/*`
- 内容管理：`/admin/content/*`

## 注意事项

1. 项目目前使用模拟数据，实际使用时需要配置真实的后端 API
2. 开发服务器运行在端口 3005，如端口冲突会自动选择其他端口
3. 项目支持热重载，修改代码后会自动刷新页面
4. 生产环境部署前需要配置正确的 API 基础路径

## 后续开发计划

1. 对接真实后端 API
2. 完善错误处理机制
3. 添加更多图表和统计功能
4. 优化移动端适配
5. 添加国际化支持
6. 完善单元测试

## 联系信息

如有问题或建议，请联系开发团队。
