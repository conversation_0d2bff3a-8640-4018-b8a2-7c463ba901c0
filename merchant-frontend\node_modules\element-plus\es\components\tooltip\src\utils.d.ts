import type { Arrayable } from 'element-plus/es/utils';
import type { Ref } from 'vue';
import type { TooltipTriggerType } from './trigger';
export declare const isTriggerType: (trigger: Arrayable<TooltipTriggerType>, type: TooltipTriggerType) => boolean;
export declare const whenTrigger: (trigger: Ref<Arrayable<TooltipTriggerType>>, type: TooltipTriggerType, handler: (e: Event) => void) => (e: Event) => void;
