<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="c0dfd39e-6f4f-4a5c-8b20-a682f4cc7fd5" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="localRepository" value="H:\apache-maven-3.6.3\maven-repository" />
        <option name="mavenHome" value="H:/apache-maven-3.6.3" />
        <option name="userSettingsFile" value="H:\apache-maven-3.6.3\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="jreName" value="17" />
  </component>
  <component name="ProjectCodeStyleSettingsMigration">
    <option name="version" value="2" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 8
}</component>
  <component name="ProjectId" id="2yqigYrxLSMQii3V67rZwlODsJB" />
  <component name="ProjectViewState">
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;Spring Boot.nongchanpinxiaoshouApplication.executor&quot;: &quot;Run&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/Desktop/实训/**********张付娟实训项目/code&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;项目&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.0&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;MavenSettings&quot;,
    &quot;show.run.dashboard.notification&quot;: &quot;false&quot;,
    &quot;spring.configuration.checksum&quot;: &quot;d164d99c46d45be6b1e7585ff7ea10e7&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager" selected="Spring Boot.nongchanpinxiaoshouApplication">
    <configuration default="true" type="PythonConfigurationType" factoryName="Python">
      <module name="nongchanpinxiaoshou" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="Python.FlaskServer">
      <module name="nongchanpinxiaoshou" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="launchJavascriptDebuger" value="false" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="Tox" factoryName="Tox">
      <module name="nongchanpinxiaoshou" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="docs" factoryName="Docutils task">
      <module name="nongchanpinxiaoshou" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="docutils_input_file" value="" />
      <option name="docutils_output_file" value="" />
      <option name="docutils_params" value="" />
      <option name="docutils_task" value="" />
      <option name="docutils_open_in_browser" value="false" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="docs" factoryName="Sphinx task">
      <module name="nongchanpinxiaoshou" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="docutils_input_file" value="" />
      <option name="docutils_output_file" value="" />
      <option name="docutils_params" value="" />
      <option name="docutils_task" value="" />
      <option name="docutils_open_in_browser" value="false" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="JetRunConfigurationType">
      <module name="nongchanpinxiaoshou" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="nongchanpinxiaoshou" />
      <option name="filePath" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="lelenongchanpinxiaoshouApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="nongchanpinxiaoshou" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.lelenongchanpinxiaoshouApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="nongchanpinxiaoshouApplication (1)" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <module name="springboot" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.nongchanpinxiaoshouApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="nongchanpinxiaoshouApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="ACTIVE_PROFILES" />
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="springboot" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.nongchanpinxiaoshouApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9f38398b9061-39b83d9b5494-intellij.indexing.shared.core-IU-241.18034.62" />
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-IU-241.18034.62" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="c0dfd39e-6f4f-4a5c-8b20-a682f4cc7fd5" name="Changes" comment="" />
      <created>1750568615757</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1750568615757</updated>
      <workItem from="1750568616965" duration="1163000" />
      <workItem from="1750570480587" duration="1448000" />
      <workItem from="1750575582509" duration="3245000" />
      <workItem from="1750608448988" duration="687000" />
      <workItem from="1750638671220" duration="10635000" />
      <workItem from="1750677932450" duration="6820000" />
      <workItem from="1750730273613" duration="8794000" />
      <workItem from="1750749505587" duration="841000" />
      <workItem from="1750750458312" duration="242000" />
      <workItem from="1750750725912" duration="23000" />
      <workItem from="1750755254764" duration="3114000" />
      <workItem from="1750761718998" duration="1683000" />
      <workItem from="1750769994321" duration="4813000" />
      <workItem from="1750776429631" duration="1982000" />
      <workItem from="1750779214259" duration="10938000" />
      <workItem from="1750834239314" duration="2391000" />
      <workItem from="1751171505368" duration="3251000" />
      <workItem from="1751178306656" duration="1174000" />
      <workItem from="1751377446718" duration="390000" />
      <workItem from="1751622945295" duration="645000" />
      <workItem from="1751624469082" duration="69000" />
      <workItem from="1751860751374" duration="1032000" />
      <workItem from="1751872896186" duration="980000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>