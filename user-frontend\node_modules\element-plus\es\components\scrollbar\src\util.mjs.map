{"version": 3, "file": "util.mjs", "sources": ["../../../../../../packages/components/scrollbar/src/util.ts"], "sourcesContent": ["import type { CSSProperties } from 'vue'\nimport type { ThumbProps } from './thumb'\n\nexport const GAP = 4 // top 2 + bottom 2 of bar instance\n\nexport const BAR_MAP = {\n  vertical: {\n    offset: 'offsetHeight',\n    scroll: 'scrollTop',\n    scrollSize: 'scrollHeight',\n    size: 'height',\n    key: 'vertical',\n    axis: 'Y',\n    client: 'clientY',\n    direction: 'top',\n  },\n  horizontal: {\n    offset: 'offsetWidth',\n    scroll: 'scrollLeft',\n    scrollSize: 'scrollWidth',\n    size: 'width',\n    key: 'horizontal',\n    axis: 'X',\n    client: 'clientX',\n    direction: 'left',\n  },\n} as const\n\nexport const renderThumbStyle = ({\n  move,\n  size,\n  bar,\n}: Pick<ThumbProps, 'move' | 'size'> & {\n  bar: typeof BAR_MAP[keyof typeof BAR_MAP]\n}): CSSProperties => ({\n  [bar.size]: size,\n  transform: `translate${bar.axis}(${move}%)`,\n})\n"], "names": [], "mappings": "AAAY,MAAC,GAAG,GAAG,EAAE;AACT,MAAC,OAAO,GAAG;AACvB,EAAE,QAAQ,EAAE;AACZ,IAAI,MAAM,EAAE,cAAc;AAC1B,IAAI,MAAM,EAAE,WAAW;AACvB,IAAI,UAAU,EAAE,cAAc;AAC9B,IAAI,IAAI,EAAE,QAAQ;AAClB,IAAI,GAAG,EAAE,UAAU;AACnB,IAAI,IAAI,EAAE,GAAG;AACb,IAAI,MAAM,EAAE,SAAS;AACrB,IAAI,SAAS,EAAE,KAAK;AACpB,GAAG;AACH,EAAE,UAAU,EAAE;AACd,IAAI,MAAM,EAAE,aAAa;AACzB,IAAI,MAAM,EAAE,YAAY;AACxB,IAAI,UAAU,EAAE,aAAa;AAC7B,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,GAAG,EAAE,YAAY;AACrB,IAAI,IAAI,EAAE,GAAG;AACb,IAAI,MAAM,EAAE,SAAS;AACrB,IAAI,SAAS,EAAE,MAAM;AACrB,GAAG;AACH,EAAE;AACU,MAAC,gBAAgB,GAAG,CAAC;AACjC,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,GAAG;AACL,CAAC,MAAM;AACP,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI;AAClB,EAAE,SAAS,EAAE,CAAC,SAAS,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC;AAC7C,CAAC;;;;"}