<template>
  <div>
    <!-- 上传文件组件 -->
    <el-upload
      ref="upload"
      :action="getActionUrl"
      list-type="picture-card"
      :multiple="multiple"
      :limit="limit"
      :headers="myHeaders"
      :file-list="fileList"
      :on-exceed="handleExceed"
      :on-preview="handleUploadPreview"
      :on-remove="handleRemove"
      :on-success="handleUploadSuccess"
      :on-error="handleUploadErr"
      :before-upload="handleBeforeUpload"
    >
      <i class="el-icon-plus"></i>
      <div slot="tip" class="el-upload__tip" style="color:#838fa1;">{{tip}}</div>
    </el-upload>
    <el-dialog :visible.sync="dialogVisible" size="tiny" append-to-body>
      <img width="100%" :src="dialogImageUrl" alt>
    </el-dialog>
  </div>
</template>
<script>
import storage from "@/utils/storage";
import base from "@/utils/base";
export default {
  data() {
    return {
      // 查看大图
      dialogVisible: false,
      // 查看大图
      dialogImageUrl: "",
      // 组件渲染图片的数组字段，有特殊格式要求
      fileList: [],
      fileUrlList: [],
      myHeaders:{}
    };
  },
  

  props: ["tip", "action", "limit", "multiple", "fileUrls", "fileType"],
  mounted() {
    this.init();
    this.myHeaders= {
      'Token':storage.get("Token")
    }
  },
  watch: {
    fileUrls: function(val, oldVal) {
      this.init();
    }
  },
  computed: {
    // 计算属性的 getter
    getActionUrl: function() {
      // 添加文件类型参数
      let url = `/${this.$base.name}/` + this.action;
      if (this.fileType) {
        url += '?type=' + this.fileType;
      }
      return url;
    }
  },
  methods: {
    // 初始化
    init() {
      if (this.fileUrls && this.fileUrls.trim() !== '') {
        this.fileUrlList = this.fileUrls.split(",");
        let fileArray = [];
        let _this = this;
        this.fileUrlList.forEach(function(item, index) {
          if (!item || item.trim() === '') return;
          
          var url = item;
          
          // 修复URL格式问题
          // 修复http:/问题
          if (url.startsWith("http:/") && !url.startsWith("http://")) {
            url = url.replace("http:/", "http://");
          }
          
          // 修复双斜杠问题
          url = url.replace(/([^:])\/\//g, "$1/");
          
          // 确保URL包含完整的基础路径
          if(!url.startsWith("http") && !url.startsWith(_this.$base.url)) {
            // 如果是相对路径，确保不会产生双斜杠
            if (url.startsWith("/") && _this.$base.url.endsWith("/")) {
              url = _this.$base.url + url.substring(1);
            } else {
              url = _this.$base.url + url;
            }
          }
          
          var name = "file_" + index;
          var file = {
            name: name,
            url: url
          };
          fileArray.push(file);
        });
        this.setFileList(fileArray);
      } else {
        // 清空列表
        this.fileList = [];
        this.fileUrlList = [];
      }
    },
    handleBeforeUpload(file) {
	
    },
    // 上传文件成功后执行
    handleUploadSuccess(res, file, fileList) {
      if (res && res.code === 0) {
        // 确保使用正确的文件路径
        let fileUrl = res.file;
        
        // 确保URL格式正确
        if (fileUrl) {
          // 删除开头的多余斜杠
          if (fileUrl.startsWith('/') && this.$base.url.endsWith('/')) {
            fileUrl = fileUrl.substring(1);
          }
        }
        
        // 正确组装完整URL
        fileList[fileList.length - 1]["url"] = this.$base.url + fileUrl;
        
        // 检查并修复URL格式错误
        let finalUrl = fileList[fileList.length - 1]["url"];
        
        // 修复http:/问题
        if (finalUrl.includes('http:/') && !finalUrl.includes('http://')) {
          finalUrl = finalUrl.replace('http:/', 'http://');
        }
        
        // 修复双斜杠问题
        finalUrl = finalUrl.replace(/([^:])\/\//g, "$1/");
        
        // 更新URL
        fileList[fileList.length - 1]["url"] = finalUrl;
        
        this.setFileList(fileList);
        this.$emit("change", this.fileUrlList.join(","));
      } else {
        this.$message.error(res.msg);
      }
    },
    // 图片上传失败
    handleUploadErr(err, file, fileList) {
      this.$message.error("文件上传失败");
    },
    // 移除图片
    handleRemove(file, fileList) {
      this.setFileList(fileList);
      this.$emit("change", this.fileUrlList.join(","));
    },
    // 查看大图
    handleUploadPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    // 限制图片数量
    handleExceed(files, fileList) {
      this.$message.warning(`最多上传${this.limit}张图片`);
    },
    // 重新对fileList进行赋值
    setFileList(fileList) {
      var fileArray = [];
      var fileUrlArray = [];
      // 有些图片不是公开的，所以需要携带token信息做权限校验
      var token = storage.get("token");
      let _this = this;
      fileList.forEach(function(item, index) {
        // 确保我们处理的是有效的URL
        if (!item.url) return;
        
        var url = item.url;
        // 移除URL中可能存在的token参数
        if (url.includes("?token=")) {
          url = url.split("?token=")[0];
        } else if (url.includes("?")) {
          url = url.split("?")[0];
        }
        
        // 修复URL格式
        if (url.startsWith("http:/") && !url.startsWith("http://")) {
          url = url.replace("http:/", "http://");
        }
        
        // 修复双斜杠问题
        url = url.replace(/([^:])\/\//g, "$1/");
        
        // 确保URL包含完整的基础路径
        if(!url.startsWith("http") && !url.startsWith(_this.$base.url)) {
          // 如果是相对路径，确保不会产生双斜杠
          if (url.startsWith("/") && _this.$base.url.endsWith("/")) {
            url = _this.$base.url + url.substring(1);
          } else {
            url = _this.$base.url + url;
          }
        }
        
        var name = item.name;
        var file = {
          name: name,
          url: url + "?token=" + token
        };
        fileArray.push(file);
        fileUrlArray.push(url);
      });
      this.fileList = fileArray;
      this.fileUrlList = fileUrlArray;
    }
  }
};
</script>
<style lang="scss" scoped>
</style>
