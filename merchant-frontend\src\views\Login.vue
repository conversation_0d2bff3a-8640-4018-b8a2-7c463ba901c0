<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-header">
        <h2>商家管理后台</h2>
        <p>农产品销售系统</p>
      </div>
      
      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        @submit.prevent="handleLogin"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="请输入用户名"
            size="large"
            prefix-icon="User"
            clearable
          />
        </el-form-item>
        
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            size="large"
            prefix-icon="Lock"
            show-password
            clearable
            @keyup.enter="handleLogin"
          />
        </el-form-item>
        
        <el-form-item>
          <el-checkbox v-model="loginForm.remember">
            记住密码
          </el-checkbox>
        </el-form-item>
        
        <el-form-item>
          <el-button
            type="primary"
            size="large"
            :loading="loading"
            @click="handleLogin"
            class="login-btn"
          >
            {{ loading ? '登录中...' : '登录' }}
          </el-button>
        </el-form-item>
      </el-form>
      
      <div class="login-footer">
        <p>还没有账号？<a href="#" @click="goToRegister">立即注册</a></p>
        <p><a href="#" @click="forgotPassword">忘记密码？</a></p>
      </div>
    </div>
    
    <!-- 背景装饰 -->
    <div class="bg-decoration">
      <div class="circle circle-1"></div>
      <div class="circle circle-2"></div>
      <div class="circle circle-3"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
// 简单的API客户端
const createApiClient = () => {
  return {
    post: async (url, data) => {
      // 模拟API调用
      console.log('API调用:', url, data)
      if (data.username === 'admin' && data.password === 'admin123') {
        return {
          code: 0,
          data: {
            token: 'mock-token-' + Date.now(),
            userInfo: {
              id: 1,
              username: 'admin',
              name: '管理员',
              role: 'merchant'
            }
          }
        }
      } else {
        return {
          code: 1,
          message: '用户名或密码错误'
        }
      }
    }
  }
}

// Token和用户管理
const tokenManager = {
  getToken() {
    return localStorage.getItem('merchant_token')
  },
  setToken(token) {
    localStorage.setItem('merchant_token', token)
  },
  removeToken() {
    localStorage.removeItem('merchant_token')
  }
}

const userManager = {
  getUser() {
    const userStr = localStorage.getItem('merchant_user')
    return userStr ? JSON.parse(userStr) : null
  },
  setUser(user) {
    localStorage.setItem('merchant_user', JSON.stringify(user))
  },
  removeUser() {
    localStorage.removeItem('merchant_user')
  }
}

const router = useRouter()

// 响应式数据
const loginFormRef = ref()
const loading = ref(false)

const loginForm = reactive({
  username: '',
  password: '',
  remember: false
})

const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ]
}

// 方法
const handleLogin = async () => {
  if (!loginFormRef.value) return
  
  try {
    const valid = await loginFormRef.value.validate()
    if (!valid) return
    
    loading.value = true
    
    // 调用登录API
    const apiClient = createApiClient()
    const response = await apiClient.post('/api/auth/login', {
      username: loginForm.username,
      password: loginForm.password,
      userType: 'merchant'
    })
    
    if (response.code === 0) {
      // 保存登录信息
      tokenManager.setToken(response.data.token)
      userManager.setUser({
        ...response.data.userInfo,
        role: 'merchant'
      })
      
      // 记住密码
      if (loginForm.remember) {
        localStorage.setItem('rememberedUsername', loginForm.username)
      } else {
        localStorage.removeItem('rememberedUsername')
      }
      
      ElMessage.success('登录成功')
      router.push('/')
    } else {
      ElMessage.error(response.message || '登录失败')
    }
  } catch (error) {
    console.error('登录错误:', error)
    ElMessage.error('登录失败，请检查网络连接')
  } finally {
    loading.value = false
  }
}

const goToRegister = () => {
  router.push('/register')
}

const forgotPassword = () => {
  ElMessage.info('忘记密码功能开发中')
}

// 生命周期
onMounted(() => {
  // 检查是否已登录
  const token = tokenManager.getToken()
  const user = userManager.getUser()
  
  if (token && user?.role === 'merchant') {
    router.push('/')
    return
  }
  
  // 恢复记住的用户名
  const rememberedUsername = localStorage.getItem('rememberedUsername')
  if (rememberedUsername) {
    loginForm.username = rememberedUsername
    loginForm.remember = true
  }
})
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.login-box {
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  width: 400px;
  position: relative;
  z-index: 10;
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-header h2 {
  color: #333;
  margin-bottom: 8px;
  font-size: 24px;
  font-weight: 600;
}

.login-header p {
  color: #666;
  font-size: 14px;
}

.login-form {
  margin-bottom: 20px;
}

.login-btn {
  width: 100%;
  height: 44px;
  font-size: 16px;
  font-weight: 500;
}

.login-footer {
  text-align: center;
  font-size: 14px;
  color: #666;
}

.login-footer p {
  margin: 8px 0;
}

.login-footer a {
  color: #409EFF;
  text-decoration: none;
  transition: color 0.3s;
}

.login-footer a:hover {
  color: #66b1ff;
}

/* 背景装饰 */
.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
}

.circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 80px;
  height: 80px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 120px;
  height: 120px;
  top: 70%;
  right: 10%;
  animation-delay: 2s;
}

.circle-3 {
  width: 60px;
  height: 60px;
  top: 40%;
  left: 80%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-box {
    width: 90%;
    padding: 30px 20px;
  }
}
</style>
