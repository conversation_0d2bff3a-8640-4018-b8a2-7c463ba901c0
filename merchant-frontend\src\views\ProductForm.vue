<template>
  <div class="product-form-page">
    <el-card>
      <template #header>
        <h2>{{ isEdit ? '编辑商品' : '添加商品' }}</h2>
      </template>
      
      <div class="content">
        <p>商品表单功能开发中...</p>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()

const isEdit = computed(() => {
  return route.params.id !== undefined
})
</script>

<style scoped>
.content {
  padding: 20px;
  text-align: center;
  color: #666;
}
</style>
