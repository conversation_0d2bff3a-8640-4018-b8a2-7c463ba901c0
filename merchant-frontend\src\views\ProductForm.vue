<template>
  <div class="product-form-page">
    <el-card>
      <template #header>
        <div class="page-header">
          <h2>{{ isEdit ? '编辑商品' : '添加商品' }}</h2>
          <el-button @click="goBack">
            <el-icon><ArrowLeft /></el-icon>
            返回列表
          </el-button>
        </div>
      </template>

      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        class="product-form"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="商品名称" prop="name">
              <el-input
                v-model="form.name"
                placeholder="请输入商品名称"
                maxlength="50"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="商品分类" prop="category">
              <el-select
                v-model="form.category"
                placeholder="请选择商品分类"
                style="width: 100%"
              >
                <el-option label="蔬菜类" value="vegetables" />
                <el-option label="水果类" value="fruits" />
                <el-option label="粮食类" value="grains" />
                <el-option label="肉类" value="meat" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="商品价格" prop="price">
              <el-input-number
                v-model="form.price"
                :min="0"
                :precision="2"
                :step="0.1"
                style="width: 100%"
                placeholder="请输入价格"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="库存数量" prop="stock">
              <el-input-number
                v-model="form.stock"
                :min="0"
                :step="1"
                style="width: 100%"
                placeholder="请输入库存"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="商品状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio :label="1">上架</el-radio>
                <el-radio :label="0">下架</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="商品图片" prop="image">
          <el-upload
            class="image-uploader"
            :action="uploadUrl"
            :headers="uploadHeaders"
            :show-file-list="false"
            :on-success="handleImageSuccess"
            :before-upload="beforeImageUpload"
          >
            <img v-if="form.image" :src="form.image" class="image-preview" />
            <el-icon v-else class="image-uploader-icon"><Plus /></el-icon>
          </el-upload>
          <div class="upload-tip">
            只能上传jpg/png文件，且不超过2MB
          </div>
        </el-form-item>

        <el-form-item label="商品描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="4"
            placeholder="请输入商品描述"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="商品详情" prop="content">
          <el-input
            v-model="form.content"
            type="textarea"
            :rows="6"
            placeholder="请输入商品详细信息"
            maxlength="1000"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="商品规格">
          <div class="specs-section">
            <div
              v-for="(spec, index) in form.specs"
              :key="index"
              class="spec-item"
            >
              <el-input
                v-model="spec.name"
                placeholder="规格名称"
                style="width: 150px; margin-right: 10px"
              />
              <el-input
                v-model="spec.value"
                placeholder="规格值"
                style="width: 150px; margin-right: 10px"
              />
              <el-button
                type="danger"
                size="small"
                @click="removeSpec(index)"
              >
                删除
              </el-button>
            </div>
            <el-button type="primary" size="small" @click="addSpec">
              添加规格
            </el-button>
          </div>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            :loading="submitting"
            @click="handleSubmit"
          >
            {{ isEdit ? '更新商品' : '创建商品' }}
          </el-button>
          <el-button @click="resetForm">重置</el-button>
          <el-button @click="goBack">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft, Plus } from '@element-plus/icons-vue'
import { createApiClient, merchantApi, API_BASE_URL } from '../../../shared/api.js'

const route = useRoute()
const router = useRouter()
const api = createApiClient()

// 响应式数据
const formRef = ref()
const submitting = ref(false)

// 判断是否为编辑模式
const isEdit = computed(() => {
  return route.params.id !== undefined
})

// 表单数据
const form = reactive({
  id: null,
  name: '',
  category: '',
  price: 0,
  stock: 0,
  status: 1,
  image: '',
  description: '',
  content: '',
  specs: []
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入商品名称', trigger: 'blur' },
    { min: 2, max: 50, message: '商品名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择商品分类', trigger: 'change' }
  ],
  price: [
    { required: true, message: '请输入商品价格', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '价格必须大于0', trigger: 'blur' }
  ],
  stock: [
    { required: true, message: '请输入库存数量', trigger: 'blur' },
    { type: 'number', min: 0, message: '库存不能小于0', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入商品描述', trigger: 'blur' },
    { min: 10, max: 500, message: '商品描述长度在 10 到 500 个字符', trigger: 'blur' }
  ]
}

// 上传配置
const uploadUrl = `${API_BASE_URL}${merchantApi.products.upload}`
const uploadHeaders = {
  'Authorization': `Bearer ${localStorage.getItem('token')}`
}

// 获取商品详情
const getProductDetail = async (id) => {
  try {
    const response = await api.get(`${merchantApi.products.detail}/${id}`)

    if (response.code === 200) {
      const product = response.data
      Object.assign(form, {
        id: product.id,
        name: product.name,
        category: product.category,
        price: product.price,
        stock: product.stock,
        status: product.status,
        image: product.image,
        description: product.description,
        content: product.content,
        specs: product.specs || []
      })
    } else {
      ElMessage.error('获取商品详情失败')
    }
  } catch (error) {
    console.error('获取商品详情失败:', error)
    ElMessage.error('获取商品详情失败')
  }
}

// 图片上传成功
const handleImageSuccess = (response) => {
  if (response.code === 200) {
    form.image = response.data.url
    ElMessage.success('图片上传成功')
  } else {
    ElMessage.error('图片上传失败')
  }
}

// 图片上传前验证
const beforeImageUpload = (file) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPG) {
    ElMessage.error('上传图片只能是 JPG/PNG 格式!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('上传图片大小不能超过 2MB!')
    return false
  }
  return true
}

// 添加规格
const addSpec = () => {
  form.specs.push({
    name: '',
    value: ''
  })
}

// 删除规格
const removeSpec = (index) => {
  form.specs.splice(index, 1)
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    submitting.value = true

    const submitData = {
      ...form,
      specs: form.specs.filter(spec => spec.name && spec.value)
    }

    let response
    if (isEdit.value) {
      response = await api.put(`${merchantApi.products.update}/${form.id}`, submitData)
    } else {
      response = await api.post(merchantApi.products.create, submitData)
    }

    if (response.code === 200) {
      ElMessage.success(isEdit.value ? '商品更新成功' : '商品创建成功')
      router.push('/products')
    } else {
      ElMessage.error(response.message || '操作失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败')
  } finally {
    submitting.value = false
  }
}

// 重置表单
const resetForm = () => {
  formRef.value.resetFields()
  form.specs = []
}

// 返回列表
const goBack = () => {
  router.push('/products')
}

// 组件挂载时获取数据
onMounted(() => {
  if (isEdit.value) {
    getProductDetail(route.params.id)
  }
})
</script>

<style scoped>
.product-form-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0;
}

.product-form {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.image-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.3s;
  width: 178px;
  height: 178px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-uploader:hover {
  border-color: #409eff;
}

.image-uploader-icon {
  font-size: 28px;
  color: #8c939d;
}

.image-preview {
  width: 178px;
  height: 178px;
  object-fit: cover;
}

.upload-tip {
  margin-top: 8px;
  font-size: 12px;
  color: #999;
}

.specs-section {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
  background-color: #fafafa;
}

.spec-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.spec-item:last-child {
  margin-bottom: 0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-textarea__inner) {
  resize: vertical;
}
</style>
