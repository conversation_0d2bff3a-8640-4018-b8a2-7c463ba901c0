<template>
  <div class="product-detail" v-loading="loading">
    <div v-if="product" class="product-container">
      <!-- 面包屑导航 -->
      <el-breadcrumb separator="/" class="breadcrumb">
        <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item :to="{ path: '/products' }">商品列表</el-breadcrumb-item>
        <el-breadcrumb-item>{{ product.nongchanpinName }}</el-breadcrumb-item>
      </el-breadcrumb>

      <!-- 商品主要信息 -->
      <div class="product-main">
        <!-- 商品图片 -->
        <div class="product-images">
          <div class="main-image">
            <img :src="getImageUrl(currentImage)" :alt="product.nongchanpinName">
          </div>
          <div v-if="productImages.length > 1" class="image-thumbnails">
            <div
              v-for="(image, index) in productImages"
              :key="index"
              :class="['thumbnail', { active: currentImageIndex === index }]"
              @click="currentImageIndex = index"
            >
              <img :src="getImageUrl(image)" :alt="`${product.nongchanpinName} ${index + 1}`">
            </div>
          </div>
        </div>

        <!-- 商品信息 -->
        <div class="product-info">
          <h1 class="product-title">{{ product.nongchanpinName }}</h1>
          
          <div class="product-subtitle">{{ product.nongchanpinContent }}</div>
          
          <!-- 价格信息 -->
          <div class="price-section">
            <div class="current-price">
              <span class="price-label">现价：</span>
              <span class="price-value">¥{{ product.nongchanpinNewMoney }}</span>
            </div>
            <div v-if="product.nongchanpinOldMoney" class="old-price">
              <span class="price-label">原价：</span>
              <span class="price-value">¥{{ product.nongchanpinOldMoney }}</span>
            </div>
          </div>

          <!-- 商品属性 */
          <div class="product-attributes">
            <div class="attribute-item">
              <span class="attr-label">产地：</span>
              <span class="attr-value">{{ product.nongchanpinAddress }}</span>
            </div>
            <div class="attribute-item">
              <span class="attr-label">分类：</span>
              <span class="attr-value">{{ product.nongchanpinTypes }}</span>
            </div>
            <div class="attribute-item">
              <span class="attr-label">库存：</span>
              <span class="attr-value">{{ product.nongchanpinKucunNumber }} 件</span>
            </div>
          </div>

          <!-- 购买操作 -->
          <div class="purchase-section">
            <div class="quantity-selector">
              <span class="quantity-label">数量：</span>
              <el-input-number
                v-model="selectedQuantity"
                :min="1"
                :max="product.nongchanpinKucunNumber"
                size="large"
              />
            </div>
            
            <div class="action-buttons">
              <el-button
                type="primary"
                size="large"
                icon="ShoppingCart"
                :loading="addingToCart"
                @click="addToCart"
              >
                加入购物车
              </el-button>
              <el-button
                type="danger"
                size="large"
                icon="ShoppingBag"
                @click="buyNow"
              >
                立即购买
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 商品详情 -->
      <div class="product-details">
        <el-tabs v-model="activeTab" class="detail-tabs">
          <el-tab-pane label="商品详情" name="details">
            <div class="detail-content">
              <h3>商品介绍</h3>
              <div class="detail-text">
                {{ product.nongchanpinContent || '暂无详细介绍' }}
              </div>
              
              <h3>产品特色</h3>
              <ul class="feature-list">
                <li>新鲜采摘，品质保证</li>
                <li>绿色无污染，健康安全</li>
                <li>产地直供，价格实惠</li>
                <li>专业包装，保鲜配送</li>
              </ul>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="规格参数" name="specs">
            <div class="specs-content">
              <table class="specs-table">
                <tr>
                  <td>商品名称</td>
                  <td>{{ product.nongchanpinName }}</td>
                </tr>
                <tr>
                  <td>商品分类</td>
                  <td>{{ product.nongchanpinTypes }}</td>
                </tr>
                <tr>
                  <td>产地</td>
                  <td>{{ product.nongchanpinAddress }}</td>
                </tr>
                <tr>
                  <td>库存数量</td>
                  <td>{{ product.nongchanpinKucunNumber }} 件</td>
                </tr>
                <tr>
                  <td>上架时间</td>
                  <td>{{ formatDate(product.addtime) }}</td>
                </tr>
              </table>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>

      <!-- 推荐商品 -->
      <div class="recommended-products">
        <h3>相关推荐</h3>
        <div v-loading="recommendLoading" class="recommend-grid">
          <div
            v-for="item in recommendedProducts"
            :key="item.id"
            class="recommend-item"
            @click="goToProduct(item.id)"
          >
            <img :src="getImageUrl(item.nongchanpinPhoto)" :alt="item.nongchanpinName">
            <div class="recommend-info">
              <h4>{{ item.nongchanpinName }}</h4>
              <p class="recommend-price">¥{{ item.nongchanpinNewMoney }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 商品不存在 -->
    <div v-else-if="!loading" class="not-found">
      <el-result
        icon="warning"
        title="商品不存在"
        sub-title="抱歉，您访问的商品不存在或已下架"
      >
        <template #extra>
          <el-button type="primary" @click="router.push('/products')">
            返回商品列表
          </el-button>
        </template>
      </el-result>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { createApiClient, userApi } from '../../../shared/api.js'
import { getImageUrl, formatDate } from '../../../shared/utils.js'
import { useCartStore } from '../stores/cart.js'

const router = useRouter()
const route = useRoute()
const cartStore = useCartStore()

// 响应式数据
const loading = ref(false)
const addingToCart = ref(false)
const recommendLoading = ref(false)
const product = ref(null)
const recommendedProducts = ref([])
const selectedQuantity = ref(1)
const activeTab = ref('details')
const currentImageIndex = ref(0)

// 计算属性
const productImages = computed(() => {
  if (!product.value?.nongchanpinPhoto) return []
  return product.value.nongchanpinPhoto.split(',').filter(img => img.trim())
})

const currentImage = computed(() => {
  return productImages.value[currentImageIndex.value] || product.value?.nongchanpinPhoto
})

// 加载商品详情
const loadProductDetail = async (productId) => {
  try {
    loading.value = true
    const apiClient = createApiClient()
    const response = await userApi.products.detail(apiClient, productId)
    
    if (response.code === 0) {
      product.value = response.data
      // 重置选择的数量
      selectedQuantity.value = 1
      currentImageIndex.value = 0
      
      // 加载推荐商品
      loadRecommendedProducts()
    } else {
      ElMessage.error(response.msg || '商品不存在')
      product.value = null
    }
  } catch (error) {
    console.error('加载商品详情失败:', error)
    ElMessage.error('加载商品详情失败')
    product.value = null
  } finally {
    loading.value = false
  }
}

// 加载推荐商品
const loadRecommendedProducts = async () => {
  if (!product.value) return
  
  try {
    recommendLoading.value = true
    const apiClient = createApiClient()
    const response = await userApi.products.list(apiClient, {
      page: 1,
      limit: 4,
      nongchanpinTypes: product.value.nongchanpinTypes,
      excludeId: product.value.id
    })
    
    if (response.code === 0) {
      recommendedProducts.value = response.data.list || []
    }
  } catch (error) {
    console.error('加载推荐商品失败:', error)
  } finally {
    recommendLoading.value = false
  }
}

// 添加到购物车
const addToCart = async () => {
  if (!product.value) return
  
  try {
    addingToCart.value = true
    const success = await cartStore.addToCart(product.value.id, selectedQuantity.value)
    if (success) {
      ElMessage.success(`已添加 ${selectedQuantity.value} 件商品到购物车`)
    }
  } finally {
    addingToCart.value = false
  }
}

// 立即购买
const buyNow = async () => {
  if (!product.value) return
  
  // 先添加到购物车
  const success = await cartStore.addToCart(product.value.id, selectedQuantity.value)
  if (success) {
    // 跳转到购物车页面
    router.push('/cart')
  }
}

// 跳转到其他商品
const goToProduct = (productId) => {
  router.push(`/product/${productId}`)
}

// 监听路由参数变化
watch(() => route.params.id, (newId) => {
  if (newId) {
    loadProductDetail(newId)
  }
}, { immediate: true })

// 组件挂载时加载数据
onMounted(() => {
  const productId = route.params.id
  if (productId) {
    loadProductDetail(productId)
  }
})
</script>

<style scoped>
.product-detail {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.breadcrumb {
  margin-bottom: 20px;
}

/* 商品主要信息 */
.product-main {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  margin-bottom: 40px;
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 商品图片 */
.product-images {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.main-image {
  width: 100%;
  height: 400px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #f0f0f0;
}

.main-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-thumbnails {
  display: flex;
  gap: 10px;
  overflow-x: auto;
}

.thumbnail {
  width: 80px;
  height: 80px;
  border-radius: 6px;
  overflow: hidden;
  border: 2px solid transparent;
  cursor: pointer;
  flex-shrink: 0;
}

.thumbnail.active {
  border-color: #409eff;
}

.thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 商品信息 */
.product-info {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.product-title {
  font-size: 28px;
  font-weight: 600;
  color: #333;
  line-height: 1.3;
}

.product-subtitle {
  font-size: 16px;
  color: #666;
  line-height: 1.5;
}

/* 价格信息 */
.price-section {
  padding: 20px 0;
  border-top: 1px solid #f0f0f0;
  border-bottom: 1px solid #f0f0f0;
}

.current-price {
  display: flex;
  align-items: baseline;
  margin-bottom: 8px;
}

.current-price .price-label {
  font-size: 16px;
  color: #666;
  margin-right: 10px;
}

.current-price .price-value {
  font-size: 32px;
  font-weight: 600;
  color: #e74c3c;
}

.old-price {
  display: flex;
  align-items: baseline;
}

.old-price .price-label {
  font-size: 14px;
  color: #999;
  margin-right: 10px;
}

.old-price .price-value {
  font-size: 18px;
  color: #999;
  text-decoration: line-through;
}

/* 商品属性 */
.product-attributes {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.attribute-item {
  display: flex;
  align-items: center;
}

.attr-label {
  width: 80px;
  font-weight: 500;
  color: #666;
}

.attr-value {
  color: #333;
}

/* 购买操作 */
.purchase-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
}

.quantity-selector {
  display: flex;
  align-items: center;
  gap: 15px;
}

.quantity-label {
  font-weight: 500;
  color: #666;
}

.action-buttons {
  display: flex;
  gap: 15px;
}

.action-buttons .el-button {
  flex: 1;
  height: 50px;
  font-size: 16px;
  font-weight: 500;
}

/* 商品详情 */
.product-details {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 40px;
  overflow: hidden;
}

.detail-tabs {
  padding: 0 30px;
}

.detail-content {
  padding: 20px 0;
}

.detail-content h3 {
  font-size: 18px;
  color: #333;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 2px solid #409eff;
}

.detail-text {
  color: #666;
  line-height: 1.6;
  margin-bottom: 30px;
}

.feature-list {
  list-style: none;
  padding: 0;
}

.feature-list li {
  padding: 8px 0;
  color: #666;
  position: relative;
  padding-left: 20px;
}

.feature-list li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #67c23a;
  font-weight: bold;
}

/* 规格表格 */
.specs-content {
  padding: 20px 0;
}

.specs-table {
  width: 100%;
  border-collapse: collapse;
}

.specs-table td {
  padding: 12px 15px;
  border-bottom: 1px solid #f0f0f0;
}

.specs-table td:first-child {
  background: #f8f9fa;
  font-weight: 500;
  color: #666;
  width: 150px;
}

/* 推荐商品 */
.recommended-products {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.recommended-products h3 {
  font-size: 20px;
  color: #333;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #409eff;
}

.recommend-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
}

.recommend-item {
  cursor: pointer;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #f0f0f0;
  transition: transform 0.3s ease;
}

.recommend-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

.recommend-item img {
  width: 100%;
  height: 150px;
  object-fit: cover;
}

.recommend-info {
  padding: 15px;
}

.recommend-info h4 {
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.recommend-price {
  font-size: 16px;
  font-weight: 600;
  color: #e74c3c;
  margin: 0;
}

/* 404状态 */
.not-found {
  padding: 60px 20px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .product-main {
    grid-template-columns: 1fr;
    gap: 20px;
    padding: 20px;
  }
  
  .main-image {
    height: 300px;
  }
  
  .product-title {
    font-size: 24px;
  }
  
  .current-price .price-value {
    font-size: 28px;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .recommend-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
  }
}
</style>
