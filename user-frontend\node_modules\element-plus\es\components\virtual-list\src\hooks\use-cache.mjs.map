{"version": 3, "file": "use-cache.mjs", "sources": ["../../../../../../../packages/components/virtual-list/src/hooks/use-cache.ts"], "sourcesContent": ["import { computed, getCurrentInstance } from 'vue'\nimport { memoize } from 'lodash-unified'\nimport memoOne from 'memoize-one'\n\nimport type { VirtualizedProps } from '../props'\n\nexport const useCache = <T>() => {\n  const vm = getCurrentInstance()!\n\n  const props = vm.proxy!.$props as VirtualizedProps\n\n  return computed(() => {\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    const _getItemStyleCache = (_: any, __: any, ___: any) =>\n      ({} as Record<string, T>)\n    return props.perfMode\n      ? memoize(_getItemStyleCache)\n      : memoOne(_getItemStyleCache)\n  })\n}\n"], "names": [], "mappings": ";;;;AAGY,MAAC,QAAQ,GAAG,MAAM;AAC9B,EAAE,MAAM,EAAE,GAAG,kBAAkB,EAAE,CAAC;AAClC,EAAE,MAAM,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC;AAChC,EAAE,OAAO,QAAQ,CAAC,MAAM;AACxB,IAAI,MAAM,kBAAkB,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,GAAG,MAAM,EAAE,CAAC,CAAC;AACpD,IAAI,OAAO,KAAK,CAAC,QAAQ,GAAG,OAAO,CAAC,kBAAkB,CAAC,GAAG,OAAO,CAAC,kBAAkB,CAAC,CAAC;AACtF,GAAG,CAAC,CAAC;AACL;;;;"}