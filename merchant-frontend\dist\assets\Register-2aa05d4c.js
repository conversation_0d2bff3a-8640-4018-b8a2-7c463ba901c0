import{u as k,r as c,a as h,b as V,j as U,c as a,d as n,e as s,g,k as r,v as i,n as u,t as p,l as d,m as q,p as P,q as T,h as f,f as C,w as L}from"./index-2c4e09fa.js";import{_ as R}from"./_plugin-vue_export-helper-c27b6911.js";const S={class:"register-container"},M={class:"register-form"},B={class:"section"},D={class:"form-group"},j={key:0,class:"error-text"},$={class:"form-group"},z={key:0,class:"error-text"},E={class:"form-row"},F={class:"form-group"},I={key:0,class:"error-text"},O={class:"form-group"},A={key:0,class:"error-text"},G={class:"section"},H={class:"form-group"},J={key:0,class:"error-text"},K={class:"form-group"},Q={key:0,class:"error-text"},W={class:"form-group"},X={key:0,class:"error-text"},Y={class:"section"},Z={class:"form-group"},ee={key:0,class:"error-text"},se={class:"form-row"},oe={class:"form-group"},te={key:0,class:"error-text"},le={class:"form-group"},re={key:0,class:"error-text"},ae={class:"section"},ne={class:"form-group"},ie={class:"form-group"},de={class:"form-group"},ue={class:"agreement"},pe={class:"checkbox-label"},me=["disabled"],ve={key:0},fe={class:"login-link"},ce={__name:"Register",setup(ge){const y=k(),m=c(!1),o=c({}),t=h({merchantName:"",contactName:"",phone:"",email:"",shopName:"",category:"",address:"",username:"",password:"",confirmPassword:"",businessLicense:"",foodLicense:"",description:"",agreeTerms:!1}),w=()=>(o.value={},t.merchantName.trim()||(o.value.merchantName="请输入商家名称"),t.contactName.trim()||(o.value.contactName="请输入联系人姓名"),t.phone.trim()?/^1[3-9]\d{9}$/.test(t.phone)||(o.value.phone="请输入正确的手机号"):o.value.phone="请输入手机号",t.email.trim()?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t.email)||(o.value.email="请输入正确的邮箱格式"):o.value.email="请输入邮箱",t.shopName.trim()||(o.value.shopName="请输入店铺名称"),t.category||(o.value.category="请选择经营类别"),t.address.trim()||(o.value.address="请输入详细地址"),t.username.trim()?t.username.length<3&&(o.value.username="用户名至少3个字符"):o.value.username="请输入登录用户名",t.password?t.password.length<6&&(o.value.password="密码至少6个字符"):o.value.password="请输入密码",t.confirmPassword?t.password!==t.confirmPassword&&(o.value.confirmPassword="两次输入的密码不一致"):o.value.confirmPassword="请确认密码",Object.keys(o.value).length===0),b=async()=>{if(w()){if(!t.agreeTerms){alert("请先同意商家入驻协议");return}m.value=!0;try{console.log("商家注册申请:",t),await new Promise(v=>setTimeout(v,2e3)),alert(`🎉 入驻申请已提交成功！

我们将在1-3个工作日内完成审核，请耐心等待。
审核结果将通过邮件和短信通知您。`),y.push("/login")}catch(v){console.error("注册失败:",v),alert("提交失败，请稍后重试")}finally{m.value=!1}}},N=()=>{alert(`商家入驻协议内容...
（这里应该显示完整的协议内容）`)};return(v,e)=>{const x=V("router-link"),_=U('else"');return a(),n("div",S,[s("div",M,[e[37]||(e[37]=s("div",{class:"form-header"},[s("h2",null,"🏪 商家入驻申请"),s("p",{class:"subtitle"},"农产品销售系统")],-1)),s("form",{onSubmit:g(b,["prevent"])},[s("div",B,[e[18]||(e[18]=s("h3",null,"📋 基本信息",-1)),s("div",D,[e[14]||(e[14]=s("label",null,"商家名称 *",-1)),r(s("input",{"onUpdate:modelValue":e[0]||(e[0]=l=>t.merchantName=l),type:"text",placeholder:"请输入商家名称",required:"",class:u({error:o.value.merchantName})},null,2),[[i,t.merchantName]]),o.value.merchantName?(a(),n("span",j,p(o.value.merchantName),1)):d("",!0)]),s("div",$,[e[15]||(e[15]=s("label",null,"联系人姓名 *",-1)),r(s("input",{"onUpdate:modelValue":e[1]||(e[1]=l=>t.contactName=l),type:"text",placeholder:"请输入联系人姓名",required:"",class:u({error:o.value.contactName})},null,2),[[i,t.contactName]]),o.value.contactName?(a(),n("span",z,p(o.value.contactName),1)):d("",!0)]),s("div",E,[s("div",F,[e[16]||(e[16]=s("label",null,"手机号 *",-1)),r(s("input",{"onUpdate:modelValue":e[2]||(e[2]=l=>t.phone=l),type:"tel",placeholder:"请输入手机号",required:"",class:u({error:o.value.phone})},null,2),[[i,t.phone]]),o.value.phone?(a(),n("span",I,p(o.value.phone),1)):d("",!0)]),s("div",O,[e[17]||(e[17]=s("label",null,"邮箱 *",-1)),r(s("input",{"onUpdate:modelValue":e[3]||(e[3]=l=>t.email=l),type:"email",placeholder:"请输入邮箱地址",required:"",class:u({error:o.value.email})},null,2),[[i,t.email]]),o.value.email?(a(),n("span",A,p(o.value.email),1)):d("",!0)])])]),s("div",G,[e[23]||(e[23]=s("h3",null,"🏬 店铺信息",-1)),s("div",H,[e[19]||(e[19]=s("label",null,"店铺名称 *",-1)),r(s("input",{"onUpdate:modelValue":e[4]||(e[4]=l=>t.shopName=l),type:"text",placeholder:"请输入店铺名称",required:"",class:u({error:o.value.shopName})},null,2),[[i,t.shopName]]),o.value.shopName?(a(),n("span",J,p(o.value.shopName),1)):d("",!0)]),s("div",K,[e[21]||(e[21]=s("label",null,"经营类别 *",-1)),r(s("select",{"onUpdate:modelValue":e[5]||(e[5]=l=>t.category=l),required:"",class:u({error:o.value.category})},e[20]||(e[20]=[P('<option value="" data-v-4fe3fdd7>请选择经营类别</option><option value="fruits" data-v-4fe3fdd7>水果类</option><option value="vegetables" data-v-4fe3fdd7>蔬菜类</option><option value="grains" data-v-4fe3fdd7>粮食类</option><option value="meat" data-v-4fe3fdd7>肉类</option><option value="dairy" data-v-4fe3fdd7>乳制品</option><option value="seafood" data-v-4fe3fdd7>水产类</option><option value="other" data-v-4fe3fdd7>其他</option>',8)]),2),[[q,t.category]]),o.value.category?(a(),n("span",Q,p(o.value.category),1)):d("",!0)]),s("div",W,[e[22]||(e[22]=s("label",null,"详细地址 *",-1)),r(s("textarea",{"onUpdate:modelValue":e[6]||(e[6]=l=>t.address=l),placeholder:"请输入详细地址",required:"",class:u({error:o.value.address})},null,2),[[i,t.address]]),o.value.address?(a(),n("span",X,p(o.value.address),1)):d("",!0)])]),s("div",Y,[e[27]||(e[27]=s("h3",null,"🔐 账号信息",-1)),s("div",Z,[e[24]||(e[24]=s("label",null,"登录用户名 *",-1)),r(s("input",{"onUpdate:modelValue":e[7]||(e[7]=l=>t.username=l),type:"text",placeholder:"请输入登录用户名",required:"",class:u({error:o.value.username})},null,2),[[i,t.username]]),o.value.username?(a(),n("span",ee,p(o.value.username),1)):d("",!0)]),s("div",se,[s("div",oe,[e[25]||(e[25]=s("label",null,"密码 *",-1)),r(s("input",{"onUpdate:modelValue":e[8]||(e[8]=l=>t.password=l),type:"password",placeholder:"请输入密码",required:"",class:u({error:o.value.password})},null,2),[[i,t.password]]),o.value.password?(a(),n("span",te,p(o.value.password),1)):d("",!0)]),s("div",le,[e[26]||(e[26]=s("label",null,"确认密码 *",-1)),r(s("input",{"onUpdate:modelValue":e[9]||(e[9]=l=>t.confirmPassword=l),type:"password",placeholder:"请再次输入密码",required:"",class:u({error:o.value.confirmPassword})},null,2),[[i,t.confirmPassword]]),o.value.confirmPassword?(a(),n("span",re,p(o.value.confirmPassword),1)):d("",!0)])])]),s("div",ae,[e[31]||(e[31]=s("h3",null,"📄 资质信息",-1)),s("div",ne,[e[28]||(e[28]=s("label",null,"营业执照号",-1)),r(s("input",{"onUpdate:modelValue":e[10]||(e[10]=l=>t.businessLicense=l),type:"text",placeholder:"请输入营业执照号（可选）"},null,512),[[i,t.businessLicense]])]),s("div",ie,[e[29]||(e[29]=s("label",null,"食品经营许可证号",-1)),r(s("input",{"onUpdate:modelValue":e[11]||(e[11]=l=>t.foodLicense=l),type:"text",placeholder:"请输入食品经营许可证号（可选）"},null,512),[[i,t.foodLicense]])]),s("div",de,[e[30]||(e[30]=s("label",null,"备注说明",-1)),r(s("textarea",{"onUpdate:modelValue":e[12]||(e[12]=l=>t.description=l),placeholder:"请简要描述您的经营情况、产品特色等",rows:"3"},null,512),[[i,t.description]])])]),s("div",ue,[s("label",pe,[r(s("input",{type:"checkbox","onUpdate:modelValue":e[13]||(e[13]=l=>t.agreeTerms=l),required:""},null,512),[[T,t.agreeTerms]]),e[32]||(e[32]=s("span",{class:"checkmark"},null,-1)),e[33]||(e[33]=f(" 我已阅读并同意 ")),s("a",{href:"#",onClick:g(N,["prevent"])},"《商家入驻协议》")])]),s("button",{type:"submit",class:"register-btn",disabled:m.value},[m.value?(a(),n("span",ve,"提交中...")):d("",!0),r((a(),n("span",null,e[34]||(e[34]=[f("🚀 提交入驻申请")]))),[[_]])],8,me)],32),s("div",fe,[e[36]||(e[36]=f(" 已有账号？")),C(x,{to:"/login"},{default:L(()=>e[35]||(e[35]=[f("立即登录")])),_:1,__:[35]})])])])}}},be=R(ce,[["__scopeId","data-v-4fe3fdd7"]]);export{be as default};
