<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dao.YonghuDao">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        a.id as id
        ,a.username as username
        ,a.password as password
        ,a.yonghu_uuid_number as yonghuUuidNumber
        ,a.yonghu_name as yonghuName
        ,a.yonghu_phone as yonghuPhone
        ,a.yonghu_id_number as yonghuIdNumber
        ,a.yonghu_photo as yonghu<PERSON>hoto
        ,a.sex_types as sexTypes
        ,a.yonghu_email as yonghuEmail
        ,a.new_money as newMoney
        ,a.create_time as createTime
    </sql>
    <select id="selectListView" parameterType="map" resultType="com.entity.view.YonghuView" >
        SELECT
        <include refid="Base_Column_List" />
        <!-- 级联表的字段 -->

        FROM yonghu  a

        <where>
            <if test="params.ids != null">
                and a.id in
                <foreach item="item" index="index" collection="params.ids" open="(" separator="," close=")">
                #{item}
                </foreach>
            </if>
            <if test=" params.username != '' and params.username != null and params.username != 'null' ">
                and a.username like CONCAT('%',#{params.username},'%')
            </if>
            <if test=" params.password != '' and params.password != null and params.password != 'null' ">
                and a.password like CONCAT('%',#{params.password},'%')
            </if>
            <if test=" params.yonghuUuidNumber != '' and params.yonghuUuidNumber != null and params.yonghuUuidNumber != 'null' ">
                and a.yonghu_uuid_number like CONCAT('%',#{params.yonghuUuidNumber},'%')
            </if>
            <if test=" params.yonghuName != '' and params.yonghuName != null and params.yonghuName != 'null' ">
                and a.yonghu_name like CONCAT('%',#{params.yonghuName},'%')
            </if>
            <if test=" params.yonghuPhone != '' and params.yonghuPhone != null and params.yonghuPhone != 'null' ">
                and a.yonghu_phone like CONCAT('%',#{params.yonghuPhone},'%')
            </if>
            <if test=" params.yonghuIdNumber != '' and params.yonghuIdNumber != null and params.yonghuIdNumber != 'null' ">
                and a.yonghu_id_number like CONCAT('%',#{params.yonghuIdNumber},'%')
            </if>
            <if test="params.sexTypes != null and params.sexTypes != ''">
                and a.sex_types = #{params.sexTypes}
            </if>
            <if test=" params.yonghuEmail != '' and params.yonghuEmail != null and params.yonghuEmail != 'null' ">
                and a.yonghu_email like CONCAT('%',#{params.yonghuEmail},'%')
            </if>
            <if test="params.newMoneyStart != null ">
                <![CDATA[  and a.new_money >= #{params.newMoneyStart}   ]]>
            </if>
            <if test="params.newMoneyEnd != null ">
                <![CDATA[  and a.new_money <= #{params.newMoneyEnd}   ]]>
            </if>
            <if test="params.newMoney != null and params.newMoney != ''">
                and a.new_money = #{params.newMoney}
            </if>

        </where>
        order by a.${params.sort} ${params.order}
    </select>

</mapper>