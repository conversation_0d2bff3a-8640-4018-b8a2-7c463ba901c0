{"version": 3, "file": "utils.mjs", "sources": ["../../../../../../packages/components/time-select/src/utils.ts"], "sourcesContent": ["interface Time {\n  hours: number\n  minutes: number\n}\n\nexport const parseTime = (time: string): null | Time => {\n  const values = (time || '').split(':')\n  if (values.length >= 2) {\n    let hours = Number.parseInt(values[0], 10)\n    const minutes = Number.parseInt(values[1], 10)\n    const timeUpper = time.toUpperCase()\n    if (timeUpper.includes('AM') && hours === 12) {\n      hours = 0\n    } else if (timeUpper.includes('PM') && hours !== 12) {\n      hours += 12\n    }\n    return {\n      hours,\n      minutes,\n    }\n  }\n\n  return null\n}\n\nexport const compareTime = (time1: string, time2: string): number => {\n  const value1 = parseTime(time1)\n  if (!value1) return -1\n  const value2 = parseTime(time2)\n  if (!value2) return -1\n  const minutes1 = value1.minutes + value1.hours * 60\n  const minutes2 = value2.minutes + value2.hours * 60\n  if (minutes1 === minutes2) {\n    return 0\n  }\n  return minutes1 > minutes2 ? 1 : -1\n}\n\nexport const padTime = (time: number | string) => {\n  return `${time}`.padStart(2, '0')\n}\nexport const formatTime = (time: Time): string => {\n  return `${padTime(time.hours)}:${padTime(time.minutes)}`\n}\n\nexport const nextTime = (time: string, step: string): string => {\n  const timeValue = parseTime(time)\n  if (!timeValue) return ''\n\n  const stepValue = parseTime(step)\n  if (!stepValue) return ''\n\n  const next = {\n    hours: timeValue.hours,\n    minutes: timeValue.minutes,\n  }\n  next.minutes += stepValue.minutes\n  next.hours += stepValue.hours\n  next.hours += Math.floor(next.minutes / 60)\n  next.minutes = next.minutes % 60\n  return formatTime(next)\n}\n"], "names": [], "mappings": "AAAY,MAAC,SAAS,GAAG,CAAC,IAAI,KAAK;AACnC,EAAE,MAAM,MAAM,GAAG,CAAC,IAAI,IAAI,EAAE,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;AACzC,EAAE,IAAI,MAAM,CAAC,MAAM,IAAI,CAAC,EAAE;AAC1B,IAAI,IAAI,KAAK,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AAC/C,IAAI,MAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AACnD,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;AACzC,IAAI,IAAI,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,KAAK,KAAK,EAAE,EAAE;AAClD,MAAM,KAAK,GAAG,CAAC,CAAC;AAChB,KAAK,MAAM,IAAI,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,KAAK,KAAK,EAAE,EAAE;AACzD,MAAM,KAAK,IAAI,EAAE,CAAC;AAClB,KAAK;AACL,IAAI,OAAO;AACX,MAAM,KAAK;AACX,MAAM,OAAO;AACb,KAAK,CAAC;AACN,GAAG;AACH,EAAE,OAAO,IAAI,CAAC;AACd,EAAE;AACU,MAAC,WAAW,GAAG,CAAC,KAAK,EAAE,KAAK,KAAK;AAC7C,EAAE,MAAM,MAAM,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;AAClC,EAAE,IAAI,CAAC,MAAM;AACb,IAAI,OAAO,CAAC,CAAC,CAAC;AACd,EAAE,MAAM,MAAM,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;AAClC,EAAE,IAAI,CAAC,MAAM;AACb,IAAI,OAAO,CAAC,CAAC,CAAC;AACd,EAAE,MAAM,QAAQ,GAAG,MAAM,CAAC,OAAO,GAAG,MAAM,CAAC,KAAK,GAAG,EAAE,CAAC;AACtD,EAAE,MAAM,QAAQ,GAAG,MAAM,CAAC,OAAO,GAAG,MAAM,CAAC,KAAK,GAAG,EAAE,CAAC;AACtD,EAAE,IAAI,QAAQ,KAAK,QAAQ,EAAE;AAC7B,IAAI,OAAO,CAAC,CAAC;AACb,GAAG;AACH,EAAE,OAAO,QAAQ,GAAG,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AACtC,EAAE;AACU,MAAC,OAAO,GAAG,CAAC,IAAI,KAAK;AACjC,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AACpC,EAAE;AACU,MAAC,UAAU,GAAG,CAAC,IAAI,KAAK;AACpC,EAAE,OAAO,CAAC,EAAE,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AAC3D,EAAE;AACU,MAAC,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,KAAK;AACxC,EAAE,MAAM,SAAS,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;AACpC,EAAE,IAAI,CAAC,SAAS;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,EAAE,MAAM,SAAS,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;AACpC,EAAE,IAAI,CAAC,SAAS;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,EAAE,MAAM,IAAI,GAAG;AACf,IAAI,KAAK,EAAE,SAAS,CAAC,KAAK;AAC1B,IAAI,OAAO,EAAE,SAAS,CAAC,OAAO;AAC9B,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,OAAO,IAAI,SAAS,CAAC,OAAO,CAAC;AACpC,EAAE,IAAI,CAAC,KAAK,IAAI,SAAS,CAAC,KAAK,CAAC;AAChC,EAAE,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC,CAAC;AAC9C,EAAE,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;AACnC,EAAE,OAAO,UAAU,CAAC,IAAI,CAAC,CAAC;AAC1B;;;;"}