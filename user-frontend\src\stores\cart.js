import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { createApiClient, userApi } from '../../../shared/api.js'
import { tokenManager } from '../../../shared/utils.js'

export const useCartStore = defineStore('cart', () => {
  // 购物车商品列表
  const cartItems = ref([])
  
  // 加载状态
  const loading = ref(false)
  
  // 计算属性
  const totalCount = computed(() => {
    return cartItems.value.reduce((total, item) => total + item.buynumber, 0)
  })
  
  const totalPrice = computed(() => {
    return cartItems.value.reduce((total, item) => {
      return total + (item.nongchanpinNewMoney * item.buynumber)
    }, 0)
  })
  
  const selectedItems = computed(() => {
    return cartItems.value.filter(item => item.selected)
  })
  
  const selectedTotalPrice = computed(() => {
    return selectedItems.value.reduce((total, item) => {
      return total + (item.nongchanpinNewMoney * item.buynumber)
    }, 0)
  })
  
  // API客户端
  const getApiClient = () => createApiClient()
  
  // 加载购物车
  const loadCart = async () => {
    if (!tokenManager.isValid()) return
    
    try {
      loading.value = true
      const apiClient = getApiClient()
      const response = await userApi.cart.list(apiClient)
      
      if (response.code === 0) {
        cartItems.value = response.data.list.map(item => ({
          ...item,
          selected: false // 添加选中状态
        }))
      }
    } catch (error) {
      console.error('加载购物车失败:', error)
    } finally {
      loading.value = false
    }
  }
  
  // 添加到购物车
  const addToCart = async (productId, quantity = 1) => {
    if (!tokenManager.isValid()) {
      ElMessage.warning('请先登录')
      return false
    }
    
    try {
      const apiClient = getApiClient()
      
      // 检查商品是否已在购物车中
      const existingItem = cartItems.value.find(item => item.nongchanpinId === productId)
      
      if (existingItem) {
        // 更新数量
        const newQuantity = existingItem.buynumber + quantity
        await updateCartItem(existingItem.id, newQuantity)
      } else {
        // 添加新商品
        const cartData = {
          nongchanpinId: productId,
          buynumber: quantity
        }
        
        const response = await userApi.cart.add(apiClient, cartData)
        
        if (response.code === 0) {
          await loadCart() // 重新加载购物车
          ElMessage.success('已添加到购物车')
          return true
        } else {
          ElMessage.error(response.msg || '添加失败')
          return false
        }
      }
    } catch (error) {
      console.error('添加到购物车失败:', error)
      ElMessage.error('添加失败，请重试')
      return false
    }
  }
  
  // 更新购物车商品数量
  const updateCartItem = async (cartId, quantity) => {
    if (quantity <= 0) {
      return removeFromCart(cartId)
    }
    
    try {
      const apiClient = getApiClient()
      const cartData = {
        id: cartId,
        buynumber: quantity
      }
      
      const response = await userApi.cart.update(apiClient, cartData)
      
      if (response.code === 0) {
        // 更新本地状态
        const item = cartItems.value.find(item => item.id === cartId)
        if (item) {
          item.buynumber = quantity
        }
        return true
      } else {
        ElMessage.error(response.msg || '更新失败')
        return false
      }
    } catch (error) {
      console.error('更新购物车失败:', error)
      ElMessage.error('更新失败，请重试')
      return false
    }
  }
  
  // 从购物车移除商品
  const removeFromCart = async (cartId) => {
    try {
      const apiClient = getApiClient()
      const response = await userApi.cart.delete(apiClient, [cartId])
      
      if (response.code === 0) {
        // 更新本地状态
        const index = cartItems.value.findIndex(item => item.id === cartId)
        if (index > -1) {
          cartItems.value.splice(index, 1)
        }
        ElMessage.success('已移除商品')
        return true
      } else {
        ElMessage.error(response.msg || '移除失败')
        return false
      }
    } catch (error) {
      console.error('移除商品失败:', error)
      ElMessage.error('移除失败，请重试')
      return false
    }
  }
  
  // 批量移除选中的商品
  const removeSelectedItems = async () => {
    const selectedIds = selectedItems.value.map(item => item.id)
    if (selectedIds.length === 0) {
      ElMessage.warning('请选择要删除的商品')
      return false
    }
    
    try {
      const apiClient = getApiClient()
      const response = await userApi.cart.delete(apiClient, selectedIds)
      
      if (response.code === 0) {
        // 更新本地状态
        cartItems.value = cartItems.value.filter(item => !selectedIds.includes(item.id))
        ElMessage.success('已删除选中商品')
        return true
      } else {
        ElMessage.error(response.msg || '删除失败')
        return false
      }
    } catch (error) {
      console.error('批量删除失败:', error)
      ElMessage.error('删除失败，请重试')
      return false
    }
  }
  
  // 切换商品选中状态
  const toggleItemSelection = (cartId) => {
    const item = cartItems.value.find(item => item.id === cartId)
    if (item) {
      item.selected = !item.selected
    }
  }
  
  // 全选/取消全选
  const toggleAllSelection = (selected) => {
    cartItems.value.forEach(item => {
      item.selected = selected
    })
  }
  
  // 清空购物车
  const clearCart = () => {
    cartItems.value = []
  }
  
  // 获取商品在购物车中的数量
  const getItemQuantity = (productId) => {
    const item = cartItems.value.find(item => item.nongchanpinId === productId)
    return item ? item.buynumber : 0
  }
  
  // 检查商品是否在购物车中
  const isInCart = (productId) => {
    return cartItems.value.some(item => item.nongchanpinId === productId)
  }
  
  return {
    // 状态
    cartItems,
    loading,
    
    // 计算属性
    totalCount,
    totalPrice,
    selectedItems,
    selectedTotalPrice,
    
    // 方法
    loadCart,
    addToCart,
    updateCartItem,
    removeFromCart,
    removeSelectedItems,
    toggleItemSelection,
    toggleAllSelection,
    clearCart,
    getItemQuantity,
    isInCart
  }
})
