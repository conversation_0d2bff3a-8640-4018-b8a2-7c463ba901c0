<template>
  <div class="products-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>商品列表</h1>
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item>商品列表</el-breadcrumb-item>
        <el-breadcrumb-item v-if="currentCategory">{{ currentCategory }}</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filters-section">
      <div class="filters-row">
        <!-- 分类筛选 -->
        <div class="filter-group">
          <label>商品分类：</label>
          <el-radio-group v-model="filters.category" @change="handleFilterChange">
            <el-radio-button label="">全部</el-radio-button>
            <el-radio-button label="蔬菜">蔬菜类</el-radio-button>
            <el-radio-button label="水果">水果类</el-radio-button>
            <el-radio-button label="粮食">粮食类</el-radio-button>
            <el-radio-button label="其他">其他类</el-radio-button>
          </el-radio-group>
        </div>

        <!-- 价格筛选 -->
        <div class="filter-group">
          <label>价格范围：</label>
          <el-select v-model="filters.priceRange" placeholder="选择价格范围" @change="handleFilterChange">
            <el-option label="全部价格" value="" />
            <el-option label="0-50元" value="0-50" />
            <el-option label="50-100元" value="50-100" />
            <el-option label="100-200元" value="100-200" />
            <el-option label="200元以上" value="200+" />
          </el-select>
        </div>

        <!-- 排序 -->
        <div class="filter-group">
          <label>排序方式：</label>
          <el-select v-model="filters.sortBy" @change="handleFilterChange">
            <el-option label="综合排序" value="id" />
            <el-option label="价格从低到高" value="price_asc" />
            <el-option label="价格从高到低" value="price_desc" />
            <el-option label="销量优先" value="sales" />
            <el-option label="最新上架" value="newest" />
          </el-select>
        </div>
      </div>

      <!-- 搜索框 -->
      <div class="search-row">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索商品名称..."
          class="search-input"
          @keyup.enter="handleSearch"
        >
          <template #append>
            <el-button icon="Search" @click="handleSearch" />
          </template>
        </el-input>
      </div>
    </div>

    <!-- 商品列表 -->
    <div class="products-content">
      <!-- 结果统计 -->
      <div class="results-info">
        <span>共找到 {{ pagination.total }} 件商品</span>
        <div class="view-mode">
          <el-radio-group v-model="viewMode" size="small">
            <el-radio-button label="grid">
              <el-icon><Grid /></el-icon>
            </el-radio-button>
            <el-radio-button label="list">
              <el-icon><List /></el-icon>
            </el-radio-button>
          </el-radio-group>
        </div>
      </div>

      <!-- 商品网格/列表 -->
      <div v-loading="loading" class="products-container">
        <div v-if="products.length === 0 && !loading" class="empty-state">
          <el-empty description="暂无商品" />
        </div>

        <div v-else :class="['products-grid', viewMode]">
          <div
            v-for="product in products"
            :key="product.id"
            :class="['product-card', viewMode]"
            @click="goToProduct(product.id)"
          >
            <div class="product-image">
              <img :src="getImageUrl(product.nongchanpinPhoto)" :alt="product.nongchanpinName">
              <div class="product-overlay">
                <el-button
                  type="primary"
                  icon="ShoppingCart"
                  circle
                  @click.stop="addToCart(product.id)"
                />
              </div>
            </div>
            
            <div class="product-info">
              <h3 class="product-name">{{ product.nongchanpinName }}</h3>
              <p class="product-desc">{{ product.nongchanpinContent }}</p>
              
              <div class="product-price">
                <span class="current-price">¥{{ product.nongchanpinNewMoney }}</span>
                <span v-if="product.nongchanpinOldMoney" class="old-price">
                  ¥{{ product.nongchanpinOldMoney }}
                </span>
              </div>
              
              <div class="product-meta">
                <span class="product-location">{{ product.nongchanpinAddress }}</span>
                <span class="product-stock">库存: {{ product.nongchanpinKucunNumber }}</span>
              </div>
              
              <div v-if="viewMode === 'list'" class="product-actions">
                <el-button
                  type="primary"
                  size="small"
                  @click.stop="addToCart(product.id)"
                >
                  加入购物车
                </el-button>
                <el-button
                  size="small"
                  @click.stop="goToProduct(product.id)"
                >
                  查看详情
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="pagination.total > 0" class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.limit"
          :page-sizes="[12, 24, 36, 48]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { createApiClient, userApi } from '../../../shared/api.js'
import { getImageUrl } from '../../../shared/utils.js'
import { useCartStore } from '../stores/cart.js'

const router = useRouter()
const route = useRoute()
const cartStore = useCartStore()

// 响应式数据
const loading = ref(false)
const products = ref([])
const searchKeyword = ref('')
const viewMode = ref('grid')

// 筛选条件
const filters = reactive({
  category: '',
  priceRange: '',
  sortBy: 'id'
})

// 分页信息
const pagination = reactive({
  page: 1,
  limit: 12,
  total: 0
})

// 当前分类
const currentCategory = computed(() => {
  return route.query.category || filters.category
})

// 加载商品列表
const loadProducts = async () => {
  try {
    loading.value = true
    const apiClient = createApiClient()
    
    // 构建查询参数
    const params = {
      page: pagination.page,
      limit: pagination.limit
    }
    
    // 添加搜索关键词
    if (searchKeyword.value) {
      params.nongchanpinName = searchKeyword.value
    }
    
    // 添加分类筛选
    if (filters.category) {
      params.nongchanpinTypes = filters.category
    }
    
    // 添加价格筛选
    if (filters.priceRange) {
      const [min, max] = filters.priceRange.split('-')
      if (min) params.minPrice = min
      if (max && max !== '+') params.maxPrice = max
    }
    
    // 添加排序
    switch (filters.sortBy) {
      case 'price_asc':
        params.sort = 'nongchanpinNewMoney'
        params.order = 'asc'
        break
      case 'price_desc':
        params.sort = 'nongchanpinNewMoney'
        params.order = 'desc'
        break
      case 'sales':
        params.sort = 'clicknum'
        params.order = 'desc'
        break
      case 'newest':
        params.sort = 'addtime'
        params.order = 'desc'
        break
      default:
        params.sort = 'id'
        params.order = 'desc'
    }
    
    const response = await userApi.products.list(apiClient, params)
    
    if (response.code === 0) {
      products.value = response.data.list || []
      pagination.total = response.data.total || 0
    } else {
      ElMessage.error(response.msg || '加载商品失败')
    }
  } catch (error) {
    console.error('加载商品失败:', error)
    ElMessage.error('加载商品失败，请重试')
  } finally {
    loading.value = false
  }
}

// 处理筛选变化
const handleFilterChange = () => {
  pagination.page = 1
  loadProducts()
}

// 处理搜索
const handleSearch = () => {
  pagination.page = 1
  loadProducts()
}

// 处理分页变化
const handlePageChange = (page) => {
  pagination.page = page
  loadProducts()
}

// 处理每页数量变化
const handleSizeChange = (size) => {
  pagination.limit = size
  pagination.page = 1
  loadProducts()
}

// 跳转到商品详情
const goToProduct = (productId) => {
  router.push(`/product/${productId}`)
}

// 添加到购物车
const addToCart = async (productId) => {
  const success = await cartStore.addToCart(productId, 1)
  if (success) {
    ElMessage.success('已添加到购物车')
  }
}

// 监听路由查询参数变化
watch(() => route.query, (newQuery) => {
  // 更新搜索关键词
  if (newQuery.search) {
    searchKeyword.value = newQuery.search
  }
  
  // 更新分类筛选
  if (newQuery.category) {
    filters.category = newQuery.category
  }
  
  // 重新加载数据
  pagination.page = 1
  loadProducts()
}, { immediate: true })

// 组件挂载时初始化
onMounted(() => {
  // 从URL参数初始化搜索条件
  if (route.query.search) {
    searchKeyword.value = route.query.search
  }
  if (route.query.category) {
    filters.category = route.query.category
  }
  
  loadProducts()
})
</script>

<style scoped>
.products-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* 页面头部 */
.page-header {
  margin-bottom: 30px;
}

.page-header h1 {
  font-size: 28px;
  color: #333;
  margin-bottom: 10px;
}

/* 筛选区域 */
.filters-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.filters-row {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 20px;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.filter-group label {
  font-weight: 500;
  color: #333;
  white-space: nowrap;
}

.search-row {
  display: flex;
  justify-content: center;
}

.search-input {
  max-width: 400px;
}

/* 商品内容区域 */
.products-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.results-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.view-mode {
  display: flex;
  align-items: center;
}

/* 商品网格 */
.products-grid.grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  padding: 20px;
}

.products-grid.list {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px;
}

/* 网格模式商品卡片 */
.product-card.grid {
  background: white;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.product-card.grid:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.product-card.grid .product-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

/* 列表模式商品卡片 */
.product-card.list {
  display: flex;
  background: white;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: box-shadow 0.3s ease;
}

.product-card.list:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.product-card.list .product-image {
  width: 200px;
  height: 150px;
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
}

.product-card.list .product-info {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* 商品图片 */
.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.product-card:hover .product-overlay {
  opacity: 1;
}

/* 商品信息 */
.product-info {
  padding: 15px;
}

.product-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-desc {
  font-size: 14px;
  color: #666;
  margin-bottom: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-price {
  margin-bottom: 12px;
}

.current-price {
  font-size: 18px;
  font-weight: 600;
  color: #e74c3c;
}

.old-price {
  font-size: 14px;
  color: #999;
  text-decoration: line-through;
  margin-left: 8px;
}

.product-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #999;
  margin-bottom: 12px;
}

.product-actions {
  display: flex;
  gap: 10px;
}

/* 分页 */
.pagination-container {
  padding: 20px;
  display: flex;
  justify-content: center;
  border-top: 1px solid #f0f0f0;
}

/* 空状态 */
.empty-state {
  padding: 60px 20px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .filters-row {
    flex-direction: column;
    gap: 15px;
  }
  
  .filter-group {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .results-info {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }
  
  .products-grid.grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
    padding: 15px;
  }
  
  .product-card.list {
    flex-direction: column;
  }
  
  .product-card.list .product-image {
    width: 100%;
    height: 200px;
  }
}
</style>
