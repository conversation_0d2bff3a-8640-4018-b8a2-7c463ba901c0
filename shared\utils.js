// 共享工具函数

// 日期格式化
export const formatDate = (date, format = 'YYYY-MM-DD HH:mm:ss') => {
  if (!date) return ''
  const d = new Date(date)
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  const seconds = String(d.getSeconds()).padStart(2, '0')

  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}

// 价格格式化
export const formatPrice = (price) => {
  if (price === null || price === undefined) return '¥0.00'
  return `¥${Number(price).toFixed(2)}`
}

// 货币格式化（别名）
export const formatCurrency = formatPrice

// 文件上传处理
export const handleFileUpload = (file, callback) => {
  const formData = new FormData()
  formData.append('file', file)
  
  // 这里可以调用上传API
  // 暂时返回本地URL用于预览
  const reader = new FileReader()
  reader.onload = (e) => {
    callback(e.target.result)
  }
  reader.readAsDataURL(file)
}

// 图片URL处理
export const getImageUrl = (path) => {
  if (!path) return '/default-image.jpg'
  if (path.startsWith('http')) return path
  return `http://localhost:8080/upload/${path}`
}

// 用户角色判断
export const getUserRole = () => {
  return localStorage.getItem('userRole') || 'guest'
}

export const isUser = () => getUserRole() === 'user'
export const isMerchant = () => getUserRole() === 'merchant'
export const isAdmin = () => getUserRole() === 'admin'

// Token管理
export const tokenManager = {
  get: () => localStorage.getItem('token'),
  set: (token) => localStorage.setItem('token', token),
  remove: () => localStorage.removeItem('token'),
  isValid: () => {
    const token = localStorage.getItem('token')
    if (!token) return false
    
    try {
      const payload = JSON.parse(atob(token.split('.')[1]))
      return payload.exp > Date.now() / 1000
    } catch {
      return false
    }
  }
}

// 用户信息管理
export const userManager = {
  get: () => {
    const userInfo = localStorage.getItem('userInfo')
    return userInfo ? JSON.parse(userInfo) : null
  },
  set: (userInfo) => {
    localStorage.setItem('userInfo', JSON.stringify(userInfo))
    localStorage.setItem('userRole', userInfo.userType || 'user')
  },
  remove: () => {
    localStorage.removeItem('userInfo')
    localStorage.removeItem('userRole')
  }
}

// 表单验证规则
export const validationRules = {
  required: (message = '此字段为必填项') => ({
    required: true,
    message,
    trigger: 'blur'
  }),
  
  email: (message = '请输入正确的邮箱地址') => ({
    type: 'email',
    message,
    trigger: 'blur'
  }),
  
  phone: (message = '请输入正确的手机号') => ({
    pattern: /^1[3-9]\d{9}$/,
    message,
    trigger: 'blur'
  }),
  
  password: (message = '密码长度至少6位') => ({
    min: 6,
    message,
    trigger: 'blur'
  }),
  
  price: (message = '请输入正确的价格') => ({
    pattern: /^\d+(\.\d{1,2})?$/,
    message,
    trigger: 'blur'
  })
}

// 订单状态映射
export const orderStatusMap = {
  0: { text: '待付款', color: '#f56c6c' },
  1: { text: '待发货', color: '#e6a23c' },
  2: { text: '待收货', color: '#409eff' },
  3: { text: '已完成', color: '#67c23a' },
  4: { text: '已取消', color: '#909399' }
}

// 获取订单状态
export const getOrderStatus = (status) => {
  return orderStatusMap[status] || { text: '未知状态', color: '#909399' }
}

// 防抖函数
export const debounce = (func, wait) => {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// 节流函数
export const throttle = (func, limit) => {
  let inThrottle
  return function(...args) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

// 深拷贝
export const deepClone = (obj) => {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj.getTime())
  if (obj instanceof Array) return obj.map(item => deepClone(item))
  if (typeof obj === 'object') {
    const clonedObj = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }
}

// 生成唯一ID
export const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}
