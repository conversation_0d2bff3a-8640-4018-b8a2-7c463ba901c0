<template>
  <div class="order-detail-page">
    <el-card>
      <template #header>
        <div class="page-header">
          <h2>订单详情</h2>
          <el-button @click="goBack">
            <el-icon><ArrowLeft /></el-icon>
            返回列表
          </el-button>
        </div>
      </template>

      <div v-loading="loading" class="order-content">
        <div v-if="orderDetail" class="order-info">
          <!-- 订单基本信息 -->
          <el-row :gutter="20">
            <el-col :span="12">
              <el-card class="info-card">
                <template #header>
                  <h3>订单信息</h3>
                </template>
                <div class="info-item">
                  <label>订单号：</label>
                  <span>{{ orderDetail.orderNo }}</span>
                </div>
                <div class="info-item">
                  <label>订单状态：</label>
                  <el-tag :type="getStatusType(orderDetail.status)">
                    {{ getStatusText(orderDetail.status) }}
                  </el-tag>
                </div>
                <div class="info-item">
                  <label>下单时间：</label>
                  <span>{{ formatDate(orderDetail.createTime) }}</span>
                </div>
                <div class="info-item">
                  <label>订单金额：</label>
                  <span class="amount">¥{{ orderDetail.totalAmount }}</span>
                </div>
                <div class="info-item">
                  <label>支付方式：</label>
                  <span>{{ orderDetail.paymentMethod || '在线支付' }}</span>
                </div>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card class="info-card">
                <template #header>
                  <h3>客户信息</h3>
                </template>
                <div class="info-item">
                  <label>客户姓名：</label>
                  <span>{{ orderDetail.customerName }}</span>
                </div>
                <div class="info-item">
                  <label>联系电话：</label>
                  <span>{{ orderDetail.customerPhone }}</span>
                </div>
                <div class="info-item">
                  <label>收货地址：</label>
                  <span>{{ orderDetail.deliveryAddress }}</span>
                </div>
                <div class="info-item">
                  <label>备注信息：</label>
                  <span>{{ orderDetail.remark || '无' }}</span>
                </div>
              </el-card>
            </el-col>
          </el-row>

          <!-- 商品信息 -->
          <el-card class="product-card">
            <template #header>
              <h3>商品信息</h3>
            </template>
            <el-table :data="orderDetail.products" style="width: 100%">
              <el-table-column label="商品图片" width="100">
                <template #default="{ row }">
                  <el-image
                    :src="row.image"
                    style="width: 60px; height: 60px"
                    fit="cover"
                  />
                </template>
              </el-table-column>
              <el-table-column prop="name" label="商品名称" min-width="150" />
              <el-table-column prop="spec" label="规格" width="120" />
              <el-table-column prop="price" label="单价" width="100">
                <template #default="{ row }">
                  <span>¥{{ row.price }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="quantity" label="数量" width="80" />
              <el-table-column label="小计" width="100">
                <template #default="{ row }">
                  <span class="amount">¥{{ (row.price * row.quantity).toFixed(2) }}</span>
                </template>
              </el-table-column>
            </el-table>
          </el-card>

          <!-- 订单操作 -->
          <el-card class="action-card">
            <template #header>
              <h3>订单操作</h3>
            </template>
            <div class="action-buttons">
              <el-button
                v-if="orderDetail.status === 'paid'"
                type="success"
                @click="shipOrder"
              >
                <el-icon><Van /></el-icon>
                确认发货
              </el-button>
              <el-button
                v-if="orderDetail.status === 'pending'"
                type="danger"
                @click="cancelOrder"
              >
                <el-icon><Close /></el-icon>
                取消订单
              </el-button>
              <el-button @click="printOrder">
                <el-icon><Printer /></el-icon>
                打印订单
              </el-button>
              <el-button @click="exportOrder">
                <el-icon><Download /></el-icon>
                导出订单
              </el-button>
            </div>
          </el-card>

          <!-- 订单日志 -->
          <el-card class="log-card">
            <template #header>
              <h3>订单日志</h3>
            </template>
            <el-timeline>
              <el-timeline-item
                v-for="log in orderDetail.logs"
                :key="log.id"
                :timestamp="log.createTime"
                :type="log.type"
              >
                {{ log.content }}
              </el-timeline-item>
            </el-timeline>
          </el-card>
        </div>

        <div v-else class="no-data">
          <el-empty description="订单不存在或已删除" />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, Van, Close, Printer, Download } from '@element-plus/icons-vue'
import { createApiClient, merchantApi } from '../../../shared/api.js'
import { formatDate } from '../../../shared/utils.js'

const route = useRoute()
const router = useRouter()
const api = createApiClient()

// 响应式数据
const loading = ref(false)
const orderDetail = ref(null)

// 状态映射
const statusMap = {
  pending: { text: '待付款', type: 'warning' },
  paid: { text: '待发货', type: 'info' },
  shipped: { text: '已发货', type: 'primary' },
  completed: { text: '已完成', type: 'success' },
  cancelled: { text: '已取消', type: 'danger' }
}

// 获取状态文本
const getStatusText = (status) => {
  return statusMap[status]?.text || status
}

// 获取状态类型
const getStatusType = (status) => {
  return statusMap[status]?.type || 'info'
}

// 模拟订单详情数据
const mockOrderDetail = {
  id: 1,
  orderNo: 'ORD202507070001',
  status: 'paid',
  totalAmount: 25.60,
  paymentMethod: '微信支付',
  createTime: '2025-07-07 10:30:00',
  customerName: '张三',
  customerPhone: '13800138000',
  deliveryAddress: '北京市朝阳区某某街道某某小区1号楼101室',
  remark: '请尽快发货，谢谢！',
  products: [
    {
      id: 1,
      name: '新鲜有机西红柿',
      spec: '500g/袋',
      price: 12.80,
      quantity: 2,
      image: 'https://via.placeholder.com/60x60?text=西红柿'
    }
  ],
  logs: [
    {
      id: 1,
      content: '订单已创建',
      createTime: '2025-07-07 10:30:00',
      type: 'primary'
    },
    {
      id: 2,
      content: '订单已支付',
      createTime: '2025-07-07 10:35:00',
      type: 'success'
    }
  ]
}

// 获取订单详情
const getOrderDetail = async () => {
  loading.value = true
  try {
    const orderId = route.params.id
    const response = await api.get(merchantApi.orders.detail.replace(':id', orderId))

    if (response.code === 200) {
      orderDetail.value = response.data || mockOrderDetail
    } else {
      // 使用模拟数据
      orderDetail.value = mockOrderDetail
    }
  } catch (error) {
    console.error('获取订单详情失败:', error)
    // 使用模拟数据
    orderDetail.value = mockOrderDetail
  } finally {
    loading.value = false
  }
}

// 返回列表
const goBack = () => {
  router.push('/orders')
}

// 确认发货
const shipOrder = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要发货此订单吗？',
      '发货确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await api.put(merchantApi.orders.updateStatus, {
      id: orderDetail.value.id,
      status: 'shipped'
    })

    if (response.code === 200) {
      ElMessage.success('订单已发货')
      orderDetail.value.status = 'shipped'
      // 添加日志
      orderDetail.value.logs.push({
        id: Date.now(),
        content: '订单已发货',
        createTime: new Date().toLocaleString(),
        type: 'success'
      })
    } else {
      ElMessage.error('发货失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('发货失败:', error)
      ElMessage.error('发货失败')
    }
  }
}

// 取消订单
const cancelOrder = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要取消此订单吗？',
      '取消确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await api.put(merchantApi.orders.updateStatus, {
      id: orderDetail.value.id,
      status: 'cancelled'
    })

    if (response.code === 200) {
      ElMessage.success('订单已取消')
      orderDetail.value.status = 'cancelled'
      // 添加日志
      orderDetail.value.logs.push({
        id: Date.now(),
        content: '订单已取消',
        createTime: new Date().toLocaleString(),
        type: 'danger'
      })
    } else {
      ElMessage.error('取消失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消失败:', error)
      ElMessage.error('取消失败')
    }
  }
}

// 打印订单
const printOrder = () => {
  ElMessage.info('打印功能开发中...')
}

// 导出订单
const exportOrder = () => {
  ElMessage.info('导出功能开发中...')
}

// 组件挂载时获取数据
onMounted(() => {
  getOrderDetail()
})
</script>

<style scoped>
.order-detail-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0;
}

.order-content {
  padding: 20px 0;
}

.order-info {
  max-width: 1200px;
  margin: 0 auto;
}

.info-card,
.product-card,
.action-card,
.log-card {
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px 0;
}

.info-item label {
  width: 100px;
  font-weight: 500;
  color: #666;
  flex-shrink: 0;
}

.info-item span {
  flex: 1;
  color: #333;
}

.amount {
  color: #e6a23c;
  font-weight: bold;
  font-size: 16px;
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.no-data {
  text-align: center;
  padding: 60px 0;
}

:deep(.el-card__header) {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

:deep(.el-card__header h3) {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

:deep(.el-table .el-table__cell) {
  padding: 12px 0;
}

:deep(.el-timeline-item__content) {
  font-size: 14px;
}

:deep(.el-image) {
  border-radius: 4px;
}

@media (max-width: 768px) {
  .order-detail-page {
    padding: 10px;
  }

  .action-buttons {
    justify-content: center;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .info-item label {
    width: auto;
    margin-bottom: 4px;
  }
}
</style>
