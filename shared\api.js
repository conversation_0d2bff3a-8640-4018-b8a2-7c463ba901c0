// 共享的API配置和工具函数
import axios from 'axios'

// API基础配置
export const API_BASE_URL = 'http://localhost:8080/nongchanpinxiaoshou'

// 创建axios实例
export const createApiClient = (baseURL = API_BASE_URL) => {
  const client = axios.create({
    baseURL,
    timeout: 10000,
    headers: {
      'Content-Type': 'application/json'
    }
  })

  // 请求拦截器
  client.interceptors.request.use(
    (config) => {
      const token = localStorage.getItem('token')
      if (token) {
        config.headers.Authorization = `Bearer ${token}`
      }
      return config
    },
    (error) => {
      return Promise.reject(error)
    }
  )

  // 响应拦截器
  client.interceptors.response.use(
    (response) => {
      return response.data
    },
    (error) => {
      // 如果是网络错误或连接被拒绝，返回模拟数据
      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
        console.warn('后端服务未启动，返回模拟数据')
        return Promise.resolve({ code: 200, data: [], message: '后端服务未启动' })
      }

      if (error.response?.status === 401) {
        // 清除token并跳转到登录页
        localStorage.removeItem('token')
        window.location.href = '/login'
      }
      return Promise.reject(error)
    }
  )

  return client
}

// 通用API方法
export const apiMethods = {
  // 用户认证
  auth: {
    login: (client, userType, credentials) => 
      client.post(`/${userType}/login`, credentials),
    logout: (client, userType) => 
      client.get(`/${userType}/logout`),
    register: (client, userType, userData) => 
      client.post(`/${userType}/register`, userData)
  },

  // 通用CRUD操作
  crud: {
    list: (client, resource, params = {}) => 
      client.get(`/${resource}/page`, { params }),
    detail: (client, resource, id) => 
      client.get(`/${resource}/detail/${id}`),
    create: (client, resource, data) => 
      client.post(`/${resource}/save`, data),
    update: (client, resource, data) => 
      client.post(`/${resource}/update`, data),
    delete: (client, resource, ids) => 
      client.post(`/${resource}/delete`, ids)
  }
}

// 用户端API
export const userApi = {
  // 认证相关
  auth: {
    login: (client, data) => client.post('/api/auth/login', data),
    logout: (client) => client.post('/api/auth/logout'),
    register: (client, data) => client.post('/yonghu/register', data)
  },

  // 商品相关
  products: {
    list: (client, params) => client.get('/nongchanpin/page', { params }),
    detail: (client, id) => client.get(`/nongchanpin/info/${id}`),
    search: (client, keyword) => client.get(`/nongchanpin/list`, { params: { nongchanpinName: keyword } })
  },

  // 购物车
  cart: {
    list: (client) => client.get('/cart/page'),
    add: (client, data) => client.post('/cart/save', data),
    update: (client, data) => client.post('/cart/update', data),
    remove: (client, id) => client.post('/cart/delete', { ids: [id] }),
    removeSelected: (client, ids) => client.post('/cart/delete', { ids }),
    clear: (client) => client.post('/cart/clear')
  },

  // 订单
  orders: {
    list: (client, params) => client.get('/nongchanpinOrder/page', { params }),
    create: (client, data) => client.post('/nongchanpinOrder/save', data),
    detail: (client, id) => client.get(`/nongchanpinOrder/info/${id}`),
    pay: (client, id) => client.post(`/nongchanpinOrder/update`, { id, nongchanpinOrderTypes: 2 }),
    cancel: (client, id) => client.post(`/nongchanpinOrder/update`, { id, nongchanpinOrderTypes: 5 }),
    receive: (client, id) => client.post(`/nongchanpinOrder/update`, { id, nongchanpinOrderTypes: 3 }),
    stats: (client) => client.get('/nongchanpinOrder/stats')
  },

  // 用户资料
  profile: {
    info: (client) => client.get('/yonghu/session'),
    update: (client, data) => client.post('/yonghu/update', data),
    changePassword: (client, data) => client.post('/yonghu/updatePassword', data)
  },

  // 收藏
  favorites: {
    list: (client, params) => client.get('/nongchanpinCollection/page', { params }),
    add: (client, data) => client.post('/nongchanpinCollection/save', data),
    remove: (client, ids) => client.post('/nongchanpinCollection/delete', { ids })
  },

  // 地址
  address: {
    list: (client) => client.get('/address/page'),
    add: (client, data) => client.post('/address/save', data),
    update: (client, data) => client.post('/address/update', data),
    remove: (client, ids) => client.post('/address/delete', { ids }),
    setDefault: (client, id) => client.post('/address/update', { id, isdefault: 1 })
  }
}

// 商家端API
export const merchantApi = {
  // 认证相关
  auth: {
    login: (client, data) => client.post('/api/auth/login', data),
    logout: (client) => client.post('/api/auth/logout'),
    profile: (client) => client.get('/shangjia/session')
  },

  // 数据概览
  dashboard: {
    stats: (client) => client.get('/nongchanpinOrder/page'),
    salesChart: (client) => client.get('/nongchanpinOrder/page'),
    categoryChart: (client) => client.get('/nongchanpin/page'),
    recentOrders: (client) => client.get('/nongchanpinOrder/page')
  },

  // 商品管理
  products: {
    list: (client, params) => client.get('/nongchanpin/page', { params }),
    detail: (client, id) => client.get(`/nongchanpin/info/${id}`),
    create: (client, data) => client.post('/nongchanpin/save', data),
    update: (client, data) => client.post('/nongchanpin/update', data),
    delete: (client, ids) => client.post('/nongchanpin/delete', { ids }),
    updateStatus: (client, data) => client.post('/nongchanpin/update', data),
    batchUpdateStatus: (client, data) => client.post('/nongchanpin/update', data),
    batchDelete: (client, ids) => client.post('/nongchanpin/delete', { ids }),
    categories: (client) => client.get('/dictionary/page'),
    upload: (client, data) => client.post('/file/upload', data)
  },

  // 订单管理
  orders: {
    list: (client, params) => client.get('/nongchanpinOrder/page', { params }),
    detail: (client, id) => client.get(`/nongchanpinOrder/info/${id}`),
    updateStatus: (client, data) => client.post('/nongchanpinOrder/update', data),
    statistics: (client) => client.get('/nongchanpinOrder/page')
  },

  // 库存管理
  inventory: {
    list: (client, params) => client.get('/nongchanpin/page', { params }),
    update: (client, data) => client.post('/nongchanpin/update', data),
    alerts: (client) => client.get('/nongchanpin/page'),
    statistics: (client) => client.get('/nongchanpin/page')
  },

  // 数据分析
  analytics: {
    overview: (client) => client.get('/nongchanpinOrder/page'),
    sales: (client) => client.get('/nongchanpinOrder/page'),
    products: (client) => client.get('/nongchanpin/page'),
    customers: (client) => client.get('/yonghu/page')
  },

  // 个人资料
  profile: {
    info: (client) => client.get('/shangjia/session'),
    update: (client, data) => client.post('/shangjia/update', data),
    changePassword: (client, data) => client.post('/shangjia/updatePassword', data)
  },

  // 店铺设置
  shop: {
    info: (client) => client.get('/shangjia/session'),
    update: (client, data) => client.post('/shangjia/update', data)
  },

  // 统计数据
  statistics: {
    dashboard: (client) => client.get('/nongchanpinOrder/page'),
    sales: (client, params) => client.get('/nongchanpinOrder/page', { params })
  }
}

// 管理员端API
export const adminApi = {
  // 认证相关
  auth: {
    login: (client, data) => client.post('/api/auth/login', data),
    logout: (client) => client.post('/api/auth/logout'),
    profile: (client) => client.get('/users/session')
  },

  // 用户管理
  users: {
    list: (client, params) => client.get('/yonghu/page', { params }),
    detail: (client, id) => client.get(`/yonghu/info/${id}`),
    create: (client, data) => client.post('/yonghu/save', data),
    update: (client, data) => client.post('/yonghu/update', data),
    delete: (client, ids) => client.post('/yonghu/delete', { ids }),
    updateStatus: (client, data) => client.post('/yonghu/update', data),
    batchUpdateStatus: (client, data) => client.post('/yonghu/update', data),
    statistics: (client) => client.get('/yonghu/page')
  },

  // 商家管理
  merchants: {
    list: (client, params) => client.get('/shangjia/page', { params }),
    detail: (client, id) => client.get(`/shangjia/info/${id}`),
    approve: (client, data) => client.post('/shangjia/update', { ...data, shangjiaYesnoTypes: 2 }),
    reject: (client, data) => client.post('/shangjia/update', { ...data, shangjiaYesnoTypes: 3 }),
    updateStatus: (client, data) => client.post('/shangjia/update', data),
    statistics: (client) => client.get('/shangjia/page')
  },

  // 系统监控
  system: {
    dashboard: (client) => client.get('/users/session'),
    logs: (client, params) => client.get('/config/page', { params }),
    performance: (client) => client.get('/config/page'),
    config: (client, params) => client.get('/config/page', { params })
  },

  // 内容管理
  content: {
    announcements: (client, params) => client.get('/gonggao/page', { params }),
    categories: (client, params) => client.get('/dictionary/page', { params }),
    settings: (client, params) => client.get('/config/page', { params })
  },

  // 公告管理
  announcements: {
    list: (client, params) => client.get('/gonggao/page', { params }),
    detail: (client, id) => client.get(`/gonggao/info/${id}`),
    create: (client, data) => client.post('/gonggao/save', data),
    update: (client, data) => client.post('/gonggao/update', data),
    delete: (client, ids) => client.post('/gonggao/delete', { ids })
  },

  // 字典管理
  dictionary: {
    list: (client, params) => client.get('/dictionary/page', { params }),
    detail: (client, id) => client.get(`/dictionary/info/${id}`),
    create: (client, data) => client.post('/dictionary/save', data),
    update: (client, data) => client.post('/dictionary/update', data),
    delete: (client, ids) => client.post('/dictionary/delete', { ids })
  }
}

// API方法集合，方便前端调用
export const apiMethods = {
  // 用户端方法
  user: userApi,

  // 商家端方法
  merchant: merchantApi,

  // 管理员端方法
  admin: adminApi,

  // 通用认证方法
  auth: {
    login: (client, userType, data) => {
      const loginData = { ...data, userType }
      return client.post('/api/auth/login', loginData)
    },
    logout: (client) => client.post('/api/auth/logout')
  }
}

// 导出默认API客户端
export default {
  createApiClient,
  userApi,
  merchantApi,
  adminApi,
  apiMethods
}