// 共享的API配置和工具函数
import axios from 'axios'

// API基础配置
export const API_BASE_URL = 'http://localhost:8080/nongchanpinxiaoshou'

// 创建axios实例
export const createApiClient = (baseURL = API_BASE_URL) => {
  const client = axios.create({
    baseURL,
    timeout: 10000,
    headers: {
      'Content-Type': 'application/json'
    }
  })

  // 请求拦截器
  client.interceptors.request.use(
    (config) => {
      const token = localStorage.getItem('token')
      if (token) {
        config.headers.Authorization = `Bearer ${token}`
      }
      return config
    },
    (error) => {
      return Promise.reject(error)
    }
  )

  // 响应拦截器
  client.interceptors.response.use(
    (response) => {
      return response.data
    },
    (error) => {
      // 如果是网络错误或连接被拒绝，返回模拟数据
      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
        console.warn('后端服务未启动，返回模拟数据')
        return Promise.resolve({ code: 200, data: [], message: '后端服务未启动' })
      }

      if (error.response?.status === 401) {
        // 清除token并跳转到登录页
        localStorage.removeItem('token')
        window.location.href = '/login'
      }
      return Promise.reject(error)
    }
  )

  return client
}

// 通用API方法
export const apiMethods = {
  // 用户认证
  auth: {
    login: (client, userType, credentials) => 
      client.post(`/${userType}/login`, credentials),
    logout: (client, userType) => 
      client.get(`/${userType}/logout`),
    register: (client, userType, userData) => 
      client.post(`/${userType}/register`, userData)
  },

  // 通用CRUD操作
  crud: {
    list: (client, resource, params = {}) => 
      client.get(`/${resource}/page`, { params }),
    detail: (client, resource, id) => 
      client.get(`/${resource}/detail/${id}`),
    create: (client, resource, data) => 
      client.post(`/${resource}/save`, data),
    update: (client, resource, data) => 
      client.post(`/${resource}/update`, data),
    delete: (client, resource, ids) => 
      client.post(`/${resource}/delete`, ids)
  }
}

// 用户端API
export const userApi = {
  // 认证相关
  auth: {
    login: (client, data) => client.post('/api/auth/login', data),
    logout: (client) => client.post('/api/auth/logout'),
    register: (client, data) => client.post('/api/yonghu/register', data)
  },

  // 商品相关
  products: {
    list: (client, params) => client.get('/api/nongchanpin/page', { params }),
    detail: (client, id) => client.get(`/api/nongchanpin/info/${id}`),
    search: (client, keyword) => client.get(`/api/nongchanpin/list`, { params: { nongchanpinName: keyword } })
  },

  // 购物车
  cart: {
    list: (client) => client.get('/api/cart/page'),
    add: (client, data) => client.post('/api/cart/save', data),
    update: (client, data) => client.post('/api/cart/update', data),
    remove: (client, id) => client.post('/api/cart/delete', { ids: [id] }),
    removeSelected: (client, ids) => client.post('/api/cart/delete', { ids }),
    clear: (client) => client.post('/api/cart/clear')
  },

  // 订单
  orders: {
    list: (client, params) => client.get('/api/nongchanpinOrder/page', { params }),
    create: (client, data) => client.post('/api/nongchanpinOrder/save', data),
    detail: (client, id) => client.get(`/api/nongchanpinOrder/info/${id}`),
    pay: (client, id) => client.post(`/api/nongchanpinOrder/pay/${id}`),
    cancel: (client, id) => client.post(`/api/nongchanpinOrder/cancel/${id}`),
    receive: (client, id) => client.post(`/api/nongchanpinOrder/receive/${id}`),
    stats: (client) => client.get('/api/nongchanpinOrder/stats')
  },

  // 用户资料
  profile: {
    info: (client) => client.get('/api/yonghu/session'),
    update: (client, data) => client.post('/api/yonghu/update', data),
    changePassword: (client, data) => client.post('/api/yonghu/updatePassword', data)
  }
}

// 商家端API
export const merchantApi = {
  // 商品管理
  products: {
    list: (client, params) => apiMethods.crud.list(client, 'nongchanpin', params),
    create: (client, data) => apiMethods.crud.create(client, 'nongchanpin', data),
    update: (client, data) => apiMethods.crud.update(client, 'nongchanpin', data),
    delete: (client, ids) => apiMethods.crud.delete(client, 'nongchanpin', ids)
  },

  // 订单管理
  orders: {
    list: (client, params) => apiMethods.crud.list(client, 'nongchanpinOrder', params),
    update: (client, data) => apiMethods.crud.update(client, 'nongchanpinOrder', data)
  },

  // 统计数据
  statistics: {
    dashboard: (client) => client.get('/statistics/merchant/dashboard'),
    sales: (client, params) => client.get('/statistics/merchant/sales', { params })
  }
}

// 管理端API
export const adminApi = {
  // 用户管理
  users: {
    list: (client, params) => apiMethods.crud.list(client, 'yonghu', params),
    create: (client, data) => apiMethods.crud.create(client, 'yonghu', data),
    update: (client, data) => apiMethods.crud.update(client, 'yonghu', data),
    delete: (client, ids) => apiMethods.crud.delete(client, 'yonghu', ids)
  },

  // 商家管理
  merchants: {
    list: (client, params) => apiMethods.crud.list(client, 'shangjia', params),
    approve: (client, id) => client.post(`/shangjia/approve/${id}`),
    reject: (client, id) => client.post(`/shangjia/reject/${id}`)
  },

  // 系统统计
  statistics: {
    dashboard: (client) => client.get('/statistics/admin/dashboard'),
    users: (client, params) => client.get('/statistics/admin/users', { params }),
    sales: (client, params) => client.get('/statistics/admin/sales', { params })
  }
}
