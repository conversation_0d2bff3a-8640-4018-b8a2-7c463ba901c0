<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="0b2005e7-9244-49a9-bc0c-fa054e86722b" name="变更" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="localRepository" value="H:\apache-maven-3.6.3\maven-repository" />
        <option name="mavenHome" value="H:/apache-maven-3.6.3" />
        <option name="userSettingsFile" value="H:\apache-maven-3.6.3\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectId" id="2zPEscrLdHhBH6P5vYfQLAogHqd" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "WebServerToolWindowFactoryState": "false",
    "last_opened_file_path": "C:/Users/<USER>/Desktop/实训/2332121290张付娟实训项目/code",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "settings.editor.selected.configurable": "MavenSettings"
  }
}]]></component>
  <component name="RunManager">
    <configuration name="未命名" type="MavenRunConfiguration" factoryName="Maven">
      <MavenSettings>
        <option name="myGeneralSettings" />
        <option name="myRunnerSettings" />
        <option name="myRunnerParameters">
          <MavenRunnerParameters>
            <option name="profiles">
              <set />
            </option>
            <option name="goals">
              <list />
            </option>
            <option name="pomFileName" />
            <option name="profilesMap">
              <map />
            </option>
            <option name="resolveToWorkspace" value="false" />
            <option name="workingDirPath" value="" />
          </MavenRunnerParameters>
        </option>
      </MavenSettings>
      <method v="2" />
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="0b2005e7-9244-49a9-bc0c-fa054e86722b" name="变更" comment="" />
      <created>1751624515629</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751624515629</updated>
      <workItem from="1751624518757" duration="527000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>