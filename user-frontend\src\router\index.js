import { createRouter, createWebHistory } from 'vue-router'
import { userManager, tokenManager } from '../../../shared/utils.js'

// 路由配置
const routes = [
  {
    path: '/',
    name: 'Layout',
    component: () => import('../views/Layout.vue'),
    children: [
      {
        path: '',
        name: 'Home',
        component: () => import('../views/Home.vue'),
        meta: { title: '首页' }
      },
      {
        path: '/products',
        name: 'Products',
        component: () => import('../views/Products.vue'),
        meta: { title: '商品列表' }
      },
      {
        path: '/product/:id',
        name: 'ProductDetail',
        component: () => import('../views/ProductDetail.vue'),
        meta: { title: '商品详情' }
      },
      {
        path: '/cart',
        name: 'Cart',
        component: () => import('../views/Cart.vue'),
        meta: { title: '购物车', requiresAuth: true }
      },
      {
        path: '/orders',
        name: 'Orders',
        component: () => import('../views/Orders.vue'),
        meta: { title: '我的订单', requiresAuth: true }
      },
      {
        path: '/profile',
        name: 'Profile',
        component: () => import('../views/Profile.vue'),
        meta: { title: '个人中心', requiresAuth: true }
      }
    ]
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/Login.vue'),
    meta: { title: '用户登录' }
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('../views/Register.vue'),
    meta: { title: '用户注册' }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('../views/NotFound.vue'),
    meta: { title: '页面不存在' }
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  document.title = to.meta.title ? `${to.meta.title} - 农产品销售平台` : '农产品销售平台'
  
  // 检查是否需要登录
  if (to.meta.requiresAuth) {
    if (!tokenManager.isValid()) {
      // 未登录，跳转到登录页
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
      return
    }
    
    // 检查用户角色
    const userInfo = userManager.get()
    if (!userInfo || userInfo.userType !== 'user') {
      // 角色不匹配，跳转到登录页
      next('/login')
      return
    }
  }
  
  // 如果已登录用户访问登录页，跳转到首页
  if (to.path === '/login' && tokenManager.isValid()) {
    const userInfo = userManager.get()
    if (userInfo && userInfo.userType === 'user') {
      next('/')
      return
    }
  }
  
  next()
})

export default router
