# 农产品销售系统

## 项目概述
这是一个基于Spring Boot + Vue.js的农产品销售系统，支持用户购买、商家管理、系统管理等功能。

## 技术栈
- 后端：Spring Boot 2.6.13 + MyBatis Plus + MySQL
- 前端：Vue.js 2.6 + Element UI + LayUI
- 数据库：MySQL 8.0

## 功能模块
- 用户管理
- 商家管理  
- 农产品管理
- 订单管理
- 购物车功能
- 交流论坛
- 公告信息

## 运行说明
1. 导入数据库文件 `nongchanpinxiaoshou.sql`
2. 修改 `application.yml` 中的数据库配置
3. 启动后端服务
4. 启动前端服务

## 登录架构分析与改进建议

### 当前登录架构问题
1. **架构混乱**：商家既能在前台登录，又能在后台登录
2. **用户体验差**：不同角色跳转到不同系统
3. **维护困难**：两套前端系统需要同时维护
4. **权限复杂**：角色权限分散在不同系统中

### 推荐改进方案：完全分离架构

#### 新架构设计
```
🌐 用户端 (C端)
├── 普通用户登录
├── 购物、浏览、下单功能
└── 移动端友好界面

🏢 商家端 (B端)  
├── 商家登录
├── 店铺管理、商品管理
├── 订单处理、数据分析
└── 专业的商家工具

⚙️ 管理端 (Admin)
├── 超级管理员登录
├── 系统管理、用户管理
├── 平台数据监控
└── 系统配置管理
```

#### URL结构建议
```
用户端：https://yourdomain.com/
商家端：https://yourdomain.com/merchant/
管理端：https://yourdomain.com/admin/
```

#### 前端架构调整
```
📁 项目结构建议
├── user-frontend/     # 用户端 (Vue3 + Vant)
├── merchant-frontend/ # 商家端 (Vue3 + Element Plus)
├── admin-frontend/    # 管理端 (Vue3 + Element Plus)
└── shared/           # 共享组件和工具
```

### 技术实现方案

#### 1. 统一认证服务
```javascript
// 统一的认证接口
POST /api/auth/login
{
  "username": "xxx",
  "password": "xxx", 
  "userType": "user|merchant|admin"
}

// 返回不同的token和权限
{
  "token": "xxx",
  "userType": "merchant",
  "permissions": [...],
  "redirectUrl": "/merchant/dashboard"
}
```

#### 2. JWT Token设计
```javascript
// Token payload
{
  "userId": 123,
  "username": "merchant001",
  "userType": "merchant",
  "permissions": ["product:create", "order:view"],
  "exp": 1640995200
}
```

#### 3. 权限控制
```javascript
// 基于角色的权限控制 (RBAC)
用户端权限：
- 浏览商品
- 购买下单
- 个人中心管理

商家端权限：
- 商品管理
- 订单处理
- 店铺装修
- 数据分析

管理端权限：
- 用户管理
- 商家审核
- 系统配置
- 数据监控
```

### 实施优先级

#### 高优先级 (必须完成)
1. **重构登录系统**：实现三端分离
2. **统一认证服务**：JWT token管理
3. **权限系统重构**：基于RBAC的权限控制
4. **商家端独立**：专业的商家管理界面

#### 中优先级 (建议完成)
1. **用户端现代化**：响应式设计，移动端适配
2. **数据可视化**：商家和管理端的数据大屏
3. **API网关**：统一的API管理和限流

#### 低优先级 (加分项)
1. **单点登录 (SSO)**：跨系统免登录
2. **微服务架构**：服务拆分和治理
3. **容器化部署**：Docker + K8s

### 毕设亮点

1. **清晰的系统架构**：三端分离，职责明确
2. **现代化的技术栈**：Vue3 + TypeScript + Vite
3. **完善的权限系统**：细粒度权限控制
4. **优秀的用户体验**：不同角色专属界面
5. **可扩展的架构**：支持后续功能扩展

### 开发建议

1. **渐进式改造**：先完成登录系统分离，再逐步优化各端功能
2. **保持兼容性**：改造过程中保证现有功能正常运行
3. **完善文档**：详细记录架构设计和实现过程
4. **性能优化**：关注系统性能和用户体验

## 项目结构

### 当前结构
```
├── code/
│   ├── springboot/          # 后端代码
│   └── vue/                 # 管理端前端代码
├── nongchanpinxiaoshou.sql  # 数据库文件
└── README.md               # 项目说明
```

### 目标结构 (完全分离架构)
```
├── backend/                 # 后端服务 (Spring Boot)
├── user-frontend/           # 用户端 (C端购物网站)
├── merchant-frontend/       # 商家端 (B端管理后台)
├── admin-frontend/          # 管理端 (Admin管理系统)
├── shared/                  # 共享资源和工具
├── docs/                    # 项目文档
├── nongchanpinxiaoshou.sql  # 数据库文件
└── README.md               # 项目说明
```

## 实施计划

### 第一阶段：项目结构规划和准备 [已完成]
- [x] 分析现有代码结构
- [x] 创建新的项目目录
- [x] 准备开发环境
- [x] 制定开发规范

### 第二阶段：后端API统一和认证重构 [进行中]
- [x] 统一认证接口设计
- [x] JWT Token重构
- [x] 权限控制优化
- [ ] API接口规范化

### 第三阶段：用户端(C端)开发
- [ ] 用户界面设计
- [ ] 商品浏览功能
- [ ] 购物车和订单
- [ ] 移动端适配

### 第四阶段：商家端(B端)开发
- [ ] 商家后台设计
- [ ] 商品管理功能
- [ ] 订单处理系统
- [ ] 数据分析面板

### 第五阶段：管理端(Admin)优化
- [ ] 管理界面优化
- [ ] 用户管理功能
- [ ] 商家审核系统
- [ ] 系统监控面板

### 第六阶段：集成测试和部署
- [ ] 三端集成测试
- [ ] 部署环境配置
- [ ] 性能优化
- [ ] 文档完善

## 更新日志
- 2025-07-04: 完成第一阶段项目结构重构，创建三端分离目录
- 2025-07-04: 完成后端统一认证控制器和权限拦截器
- 2025-07-04: 创建用户端基础框架和登录页面
- 2025-07-04: 添加完全分离架构实施计划
- 2025-07-04: 添加登录架构分析和改进建议
- 2025-06-22: 初始项目创建

## 已完成的工作

### 项目结构重构
1. **新目录结构**: 创建了backend、user-frontend、merchant-frontend、admin-frontend、shared、docs目录
2. **代码迁移**: 将现有Spring Boot代码迁移到backend目录，Vue管理端代码迁移到admin-frontend目录
3. **共享资源**: 创建shared目录存放API接口、工具函数等共享代码

### 后端认证系统重构
1. **统一认证控制器**: 创建AuthController支持用户、商家、管理员三种角色的统一登录
2. **权限拦截器优化**: 更新AuthorizationInterceptor支持Bearer Token认证
3. **角色权限设计**: 为不同角色定义了详细的权限列表

### 用户端基础框架
1. **Vue3项目初始化**: 使用Vite + Vue3 + Element Plus技术栈
2. **路由配置**: 设置用户端路由和权限守卫
3. **登录页面**: 创建现代化的用户登录界面
4. **API集成**: 集成统一认证API接口

### 开发文档
1. **开发指南**: 详细的项目架构和开发规范文档
2. **技术栈说明**: 各端技术选型和配置说明
3. **部署指南**: 开发和生产环境部署说明
