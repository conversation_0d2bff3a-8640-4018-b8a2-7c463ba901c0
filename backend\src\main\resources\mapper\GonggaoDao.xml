<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dao.GonggaoDao">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        a.id as id
        ,a.gonggao_name as gonggaoName
        ,a.gonggao_photo as gonggaoPhoto
        ,a.gonggao_types as gonggaoTypes
        ,a.insert_time as insertTime
        ,a.gonggao_content as gonggaoContent
        ,a.create_time as createTime
    </sql>
    <select id="selectListView" parameterType="map" resultType="com.entity.view.GonggaoView" >
        SELECT
        <include refid="Base_Column_List" />
        <!-- 级联表的字段 -->

        FROM gonggao  a

        <where>
            <if test="params.ids != null">
                and a.id in
                <foreach item="item" index="index" collection="params.ids" open="(" separator="," close=")">
                #{item}
                </foreach>
            </if>
            <if test=" params.gonggaoName != '' and params.gonggaoName != null and params.gonggaoName != 'null' ">
                and a.gonggao_name like CONCAT('%',#{params.gonggaoName},'%')
            </if>
            <if test="params.gonggaoTypes != null and params.gonggaoTypes != ''">
                and a.gonggao_types = #{params.gonggaoTypes}
            </if>
            <if test=" params.insertTimeStart != '' and params.insertTimeStart != null ">
                <![CDATA[  and UNIX_TIMESTAMP(a.insert_time) >= UNIX_TIMESTAMP(#{params.insertTimeStart}) ]]>
            </if>
            <if test=" params.insertTimeEnd != '' and params.insertTimeEnd != null ">
                <![CDATA[  and UNIX_TIMESTAMP(a.insert_time) <= UNIX_TIMESTAMP(#{params.insertTimeEnd}) ]]>
            </if>
            <if test="params.insertTime != null and params.insertTime != ''">
                and a.insert_time = #{params.insertTime}
            </if>
            <if test=" params.gonggaoContent != '' and params.gonggaoContent != null and params.gonggaoContent != 'null' ">
                and a.gonggao_content like CONCAT('%',#{params.gonggaoContent},'%')
            </if>

        </where>
        order by a.${params.sort} ${params.order}
    </select>

</mapper>