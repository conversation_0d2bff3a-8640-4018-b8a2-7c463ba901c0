{"version": 3, "file": "usePagination.js", "sources": ["../../../../../../packages/components/pagination/src/usePagination.ts"], "sourcesContent": ["import { inject } from 'vue'\nimport { elPaginationKey } from './constants'\n\nexport const usePagination = () => inject(elPaginationKey, {})\n"], "names": ["inject", "elPaginationKey"], "mappings": ";;;;;;;AAEY,MAAC,aAAa,GAAG,MAAMA,UAAM,CAACC,yBAAe,EAAE,EAAE;;;;"}