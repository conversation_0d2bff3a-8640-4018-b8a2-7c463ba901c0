<template>
  <div class="analytics-page">
    <!-- 时间范围选择 -->
    <el-card class="time-range-card">
      <div class="time-range-selector">
        <el-radio-group v-model="timeRange" @change="handleTimeRangeChange">
          <el-radio-button label="today">今日</el-radio-button>
          <el-radio-button label="week">本周</el-radio-button>
          <el-radio-button label="month">本月</el-radio-button>
          <el-radio-button label="quarter">本季度</el-radio-button>
          <el-radio-button label="year">本年</el-radio-button>
          <el-radio-button label="custom">自定义</el-radio-button>
        </el-radio-group>
        <el-date-picker
          v-if="timeRange === 'custom'"
          v-model="customDateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="margin-left: 20px"
          @change="handleCustomDateChange"
        />
      </div>
    </el-card>

    <!-- 核心指标 -->
    <el-card class="metrics-card">
      <template #header>
        <h3>核心指标</h3>
      </template>
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="metric-item">
            <div class="metric-value">¥{{ metrics.totalRevenue }}</div>
            <div class="metric-label">总收入</div>
            <div class="metric-change" :class="metrics.revenueChange >= 0 ? 'positive' : 'negative'">
              {{ metrics.revenueChange >= 0 ? '+' : '' }}{{ metrics.revenueChange }}%
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="metric-item">
            <div class="metric-value">{{ metrics.totalOrders }}</div>
            <div class="metric-label">订单数量</div>
            <div class="metric-change" :class="metrics.ordersChange >= 0 ? 'positive' : 'negative'">
              {{ metrics.ordersChange >= 0 ? '+' : '' }}{{ metrics.ordersChange }}%
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="metric-item">
            <div class="metric-value">{{ metrics.avgOrderValue }}</div>
            <div class="metric-label">客单价</div>
            <div class="metric-change" :class="metrics.avgOrderChange >= 0 ? 'positive' : 'negative'">
              {{ metrics.avgOrderChange >= 0 ? '+' : '' }}{{ metrics.avgOrderChange }}%
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="metric-item">
            <div class="metric-value">{{ metrics.conversionRate }}%</div>
            <div class="metric-label">转化率</div>
            <div class="metric-change" :class="metrics.conversionChange >= 0 ? 'positive' : 'negative'">
              {{ metrics.conversionChange >= 0 ? '+' : '' }}{{ metrics.conversionChange }}%
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 图表区域 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <h3>销售趋势</h3>
          </template>
          <div ref="salesTrendChart" class="chart-container"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <h3>商品分类销售占比</h3>
          </template>
          <div ref="categoryChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <h3>订单状态分布</h3>
          </template>
          <div ref="orderStatusChart" class="chart-container"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <h3>热销商品TOP10</h3>
          </template>
          <div ref="topProductsChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细数据表格 -->
    <el-card class="table-card">
      <template #header>
        <div class="table-header">
          <h3>商品销售明细</h3>
          <el-button @click="exportData">
            <el-icon><Download /></el-icon>
            导出数据
          </el-button>
        </div>
      </template>
      <el-table :data="productSalesData" style="width: 100%">
        <el-table-column prop="productName" label="商品名称" min-width="150" />
        <el-table-column prop="category" label="分类" width="100" />
        <el-table-column prop="salesCount" label="销量" width="80" />
        <el-table-column prop="revenue" label="销售额" width="100">
          <template #default="{ row }">
            <span>¥{{ row.revenue }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="profit" label="利润" width="100">
          <template #default="{ row }">
            <span class="profit">¥{{ row.profit }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="profitMargin" label="利润率" width="80">
          <template #default="{ row }">
            <span>{{ row.profitMargin }}%</span>
          </template>
        </el-table-column>
        <el-table-column prop="stockLevel" label="库存水平" width="100">
          <template #default="{ row }">
            <el-tag :type="getStockLevelType(row.stockLevel)">
              {{ row.stockLevel }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Download } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { createApiClient, merchantApi } from '../../../shared/api.js'

// 响应式数据
const timeRange = ref('month')
const customDateRange = ref(null)

// 核心指标数据
const metrics = reactive({
  totalRevenue: '25,680.50',
  revenueChange: 12.5,
  totalOrders: 156,
  ordersChange: 8.3,
  avgOrderValue: '164.62',
  avgOrderChange: 3.8,
  conversionRate: 3.2,
  conversionChange: -0.5
})

// 商品销售数据
const productSalesData = ref([
  {
    productName: '新鲜有机西红柿',
    category: '蔬菜类',
    salesCount: 45,
    revenue: 576.00,
    profit: 172.80,
    profitMargin: 30.0,
    stockLevel: '充足'
  },
  {
    productName: '优质苹果',
    category: '水果类',
    salesCount: 32,
    revenue: 272.00,
    profit: 81.60,
    profitMargin: 30.0,
    stockLevel: '不足'
  },
  {
    productName: '东北大米',
    category: '粮食类',
    salesCount: 28,
    revenue: 140.00,
    profit: 35.00,
    profitMargin: 25.0,
    stockLevel: '缺货'
  },
  {
    productName: '新鲜猪肉',
    category: '肉类',
    salesCount: 18,
    revenue: 630.00,
    profit: 189.00,
    profitMargin: 30.0,
    stockLevel: '充足'
  }
])

// 图表引用
const salesTrendChart = ref(null)
const categoryChart = ref(null)
const orderStatusChart = ref(null)
const topProductsChart = ref(null)

// 图表实例
let salesTrendChartInstance = null
let categoryChartInstance = null
let orderStatusChartInstance = null
let topProductsChartInstance = null

// 获取库存水平类型
const getStockLevelType = (level) => {
  switch (level) {
    case '充足': return 'success'
    case '不足': return 'warning'
    case '缺货': return 'danger'
    default: return 'info'
  }
}

// 时间范围变化处理
const handleTimeRangeChange = (value) => {
  if (value !== 'custom') {
    customDateRange.value = null
  }
  loadAnalyticsData()
}

// 自定义日期变化处理
const handleCustomDateChange = () => {
  loadAnalyticsData()
}

// 加载分析数据
const loadAnalyticsData = async () => {
  try {
    const params = {
      timeRange: timeRange.value,
      startDate: customDateRange.value?.[0],
      endDate: customDateRange.value?.[1]
    }

    const apiClient = createApiClient()
    const response = await merchantApi.analytics.overview(apiClient, params)

    if (response.code === 0) {
      // 更新指标数据
      Object.assign(metrics, response.data.metrics || metrics)
      productSalesData.value = response.data.productSales || productSalesData.value
    }

    // 更新图表
    updateCharts()
  } catch (error) {
    console.error('加载分析数据失败:', error)
    // 使用模拟数据更新图表
    updateCharts()
  }
}

// 初始化销售趋势图表
const initSalesTrendChart = () => {
  if (!salesTrendChart.value) return

  salesTrendChartInstance = echarts.init(salesTrendChart.value)

  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['销售额', '订单数']
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月']
    },
    yAxis: [
      {
        type: 'value',
        name: '销售额(元)',
        position: 'left'
      },
      {
        type: 'value',
        name: '订单数',
        position: 'right'
      }
    ],
    series: [
      {
        name: '销售额',
        type: 'line',
        data: [12000, 15000, 18000, 22000, 25000, 28000, 25680],
        smooth: true,
        itemStyle: { color: '#409eff' }
      },
      {
        name: '订单数',
        type: 'bar',
        yAxisIndex: 1,
        data: [120, 145, 168, 185, 202, 218, 156],
        itemStyle: { color: '#67c23a' }
      }
    ]
  }

  salesTrendChartInstance.setOption(option)
}

// 初始化分类销售图表
const initCategoryChart = () => {
  if (!categoryChart.value) return

  categoryChartInstance = echarts.init(categoryChart.value)

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '销售占比',
        type: 'pie',
        radius: '50%',
        data: [
          { value: 35, name: '蔬菜类' },
          { value: 25, name: '水果类' },
          { value: 20, name: '肉类' },
          { value: 15, name: '粮食类' },
          { value: 5, name: '其他' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }

  categoryChartInstance.setOption(option)
}

// 初始化订单状态图表
const initOrderStatusChart = () => {
  if (!orderStatusChart.value) return

  orderStatusChartInstance = echarts.init(orderStatusChart.value)

  const option = {
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        name: '订单状态',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 45, name: '已完成' },
          { value: 35, name: '待发货' },
          { value: 15, name: '已发货' },
          { value: 5, name: '已取消' }
        ]
      }
    ]
  }

  orderStatusChartInstance.setOption(option)
}

// 初始化热销商品图表
const initTopProductsChart = () => {
  if (!topProductsChart.value) return

  topProductsChartInstance = echarts.init(topProductsChart.value)

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value'
    },
    yAxis: {
      type: 'category',
      data: ['新鲜猪肉', '东北大米', '优质苹果', '有机西红柿']
    },
    series: [
      {
        name: '销量',
        type: 'bar',
        data: [18, 28, 32, 45],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: '#83bff6' },
            { offset: 0.5, color: '#188df0' },
            { offset: 1, color: '#188df0' }
          ])
        }
      }
    ]
  }

  topProductsChartInstance.setOption(option)
}

// 更新所有图表
const updateCharts = () => {
  nextTick(() => {
    initSalesTrendChart()
    initCategoryChart()
    initOrderStatusChart()
    initTopProductsChart()
  })
}

// 导出数据
const exportData = () => {
  ElMessage.info('导出功能开发中...')
}

// 窗口大小变化时重新调整图表
const handleResize = () => {
  salesTrendChartInstance?.resize()
  categoryChartInstance?.resize()
  orderStatusChartInstance?.resize()
  topProductsChartInstance?.resize()
}

// 组件挂载
onMounted(() => {
  loadAnalyticsData()
  window.addEventListener('resize', handleResize)
})

// 组件卸载时清理
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  salesTrendChartInstance?.dispose()
  categoryChartInstance?.dispose()
  orderStatusChartInstance?.dispose()
  topProductsChartInstance?.dispose()
})
</script>

<style scoped>
.analytics-page {
  padding: 20px;
}

.time-range-card {
  margin-bottom: 20px;
}

.time-range-selector {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 0;
}

.metrics-card {
  margin-bottom: 20px;
}

.metric-item {
  text-align: center;
  padding: 20px;
  border-radius: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.metric-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(-100%);
  transition: transform 0.3s;
}

.metric-item:hover::before {
  transform: translateX(0);
}

.metric-value {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 8px;
}

.metric-label {
  font-size: 14px;
  opacity: 0.9;
  margin-bottom: 8px;
}

.metric-change {
  font-size: 12px;
  font-weight: 500;
}

.metric-change.positive {
  color: #67c23a;
}

.metric-change.negative {
  color: #f56c6c;
}

.chart-card {
  margin-bottom: 20px;
}

.chart-container {
  height: 300px;
  width: 100%;
}

.table-card {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-header h3 {
  margin: 0;
}

.profit {
  color: #67c23a;
  font-weight: bold;
}

:deep(.el-card__header) {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

:deep(.el-card__header h3) {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

@media (max-width: 768px) {
  .analytics-page {
    padding: 10px;
  }

  .time-range-selector {
    flex-direction: column;
    gap: 10px;
  }

  .metric-item {
    margin-bottom: 10px;
  }

  .chart-container {
    height: 250px;
  }
}
</style>
