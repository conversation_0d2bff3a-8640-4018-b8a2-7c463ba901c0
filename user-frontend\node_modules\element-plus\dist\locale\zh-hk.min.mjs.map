{"version": 3, "file": "zh-hk.min.mjs", "sources": ["../../../../packages/locale/lang/zh-hk.ts"], "sourcesContent": ["export default {\n  name: 'zh-hk',\n  el: {\n    breadcrumb: {\n      label: '麵包屑',\n    },\n    colorpicker: {\n      confirm: '確認',\n      clear: '清空',\n      defaultLabel: '顏色選擇器',\n      description: '當前顏色為 {color}。按 Enter 鍵選擇新顏色。',\n      alphaLabel: '選擇透明度的值',\n    },\n    datepicker: {\n      now: '現在',\n      today: '今天',\n      cancel: '取消',\n      clear: '清空',\n      confirm: '確認',\n      dateTablePrompt: '使用方向鍵與 Enter 鍵以選擇日期',\n      monthTablePrompt: '使用方向鍵與 Enter 鍵以選擇月份',\n      yearTablePrompt: '使用方向鍵與 Enter 鍵以選擇年份',\n      selectedDate: '已選日期',\n      selectDate: '選擇日期',\n      selectTime: '選擇時間',\n      startDate: '開始日期',\n      startTime: '開始時間',\n      endDate: '結束日期',\n      endTime: '結束時間',\n      prevYear: '前一年',\n      nextYear: '後一年',\n      prevMonth: '上個月',\n      nextMonth: '下個月',\n      year: '年',\n      month1: '1 月',\n      month2: '2 月',\n      month3: '3 月',\n      month4: '4 月',\n      month5: '5 月',\n      month6: '6 月',\n      month7: '7 月',\n      month8: '8 月',\n      month9: '9 月',\n      month10: '10 月',\n      month11: '11 月',\n      month12: '12 月',\n      // week: '周次',\n      weeks: {\n        sun: '日',\n        mon: '一',\n        tue: '二',\n        wed: '三',\n        thu: '四',\n        fri: '五',\n        sat: '六',\n      },\n      weeksFull: {\n        sun: '星期日',\n        mon: '星期一',\n        tue: '星期二',\n        wed: '星期三',\n        thu: '星期四',\n        fri: '星期五',\n        sat: '星期六',\n      },\n      months: {\n        jan: '一月',\n        feb: '二月',\n        mar: '三月',\n        apr: '四月',\n        may: '五月',\n        jun: '六月',\n        jul: '七月',\n        aug: '八月',\n        sep: '九月',\n        oct: '十月',\n        nov: '十一月',\n        dec: '十二月',\n      },\n    },\n    inputNumber: {\n      decrease: '減少數值',\n      increase: '增加數值',\n    },\n    select: {\n      loading: '載入中',\n      noMatch: '無匹配資料',\n      noData: '無資料',\n      placeholder: '請選擇',\n    },\n    mention: {\n      loading: '載入中',\n    },\n    dropdown: {\n      toggleDropdown: '切換下拉選單',\n    },\n    cascader: {\n      noMatch: '無匹配資料',\n      loading: '載入中',\n      placeholder: '請選擇',\n      noData: '無資料',\n    },\n    pagination: {\n      goto: '前往',\n      pagesize: '項/頁',\n      total: '共 {total} 項',\n      pageClassifier: '頁',\n      page: '頁',\n      prev: '上一頁',\n      next: '下一頁',\n      currentPage: '第 {pager} 頁',\n      prevPages: '向前 {pager} 頁',\n      nextPages: '向後 {pager} 頁',\n      deprecationWarning:\n        '檢測到已過時的使用方式，請參閱 el-pagination 說明文件以了解更多資訊',\n    },\n    dialog: {\n      close: '關閉此對話框',\n    },\n    drawer: {\n      close: '關閉此對話框',\n    },\n    messagebox: {\n      title: '提示',\n      confirm: '確定',\n      cancel: '取消',\n      error: '輸入的資料不符合規定!',\n      close: '關閉此對話框',\n    },\n    upload: {\n      deleteTip: '按 Delete 鍵以刪除',\n      delete: '刪除',\n      preview: '查看圖片',\n      continue: '繼續上傳',\n    },\n    slider: {\n      defaultLabel: '滑桿介於 {min} 至 {max}',\n      defaultRangeStartLabel: '選擇起始值',\n      defaultRangeEndLabel: '選擇結束值',\n    },\n    table: {\n      emptyText: '暫無資料',\n      confirmFilter: '篩選',\n      resetFilter: '重置',\n      clearFilter: '全部',\n      sumText: '合計',\n    },\n    tour: {\n      next: '下一步',\n      previous: '上一步',\n      finish: '結束導覽',\n    },\n    tree: {\n      emptyText: '暫無資料',\n    },\n    transfer: {\n      noMatch: '無匹配資料',\n      noData: '無資料',\n      titles: ['列表 1', '列表 2'],\n      filterPlaceholder: '請輸入搜尋內容',\n      noCheckedFormat: '共 {total} 項',\n      hasCheckedFormat: '已選 {checked}/{total} 項',\n    },\n    image: {\n      error: '載入失敗',\n    },\n    pageHeader: {\n      title: '返回',\n    },\n    popconfirm: {\n      confirmButtonText: '確認',\n      cancelButtonText: '取消',\n    },\n    carousel: {\n      leftArrow: '上一張幻燈片',\n      rightArrow: '下一張幻燈片',\n      indicator: '幻燈片切換至索引 {index}',\n    },\n  },\n}\n"], "names": [], "mappings": ";;AAAA,WAAc,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,cAAc,CAAC,YAAY,CAAC,gCAAgC,CAAC,WAAW,CAAC,qGAAqG,CAAC,UAAU,CAAC,4CAA4C,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,cAAc,CAAC,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,cAAc,CAAC,eAAe,CAAC,iFAAiF,CAAC,gBAAgB,CAAC,iFAAiF,CAAC,eAAe,CAAC,iFAAiF,CAAC,YAAY,CAAC,0BAA0B,CAAC,UAAU,CAAC,0BAA0B,CAAC,UAAU,CAAC,0BAA0B,CAAC,SAAS,CAAC,0BAA0B,CAAC,SAAS,CAAC,0BAA0B,CAAC,OAAO,CAAC,0BAA0B,CAAC,OAAO,CAAC,0BAA0B,CAAC,QAAQ,CAAC,oBAAoB,CAAC,QAAQ,CAAC,oBAAoB,CAAC,SAAS,CAAC,oBAAoB,CAAC,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,0BAA0B,CAAC,QAAQ,CAAC,0BAA0B,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,oBAAoB,CAAC,OAAO,CAAC,gCAAgC,CAAC,MAAM,CAAC,oBAAoB,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,sCAAsC,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,gCAAgC,CAAC,OAAO,CAAC,oBAAoB,CAAC,WAAW,CAAC,oBAAoB,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,eAAe,CAAC,KAAK,CAAC,uBAAuB,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,uBAAuB,CAAC,SAAS,CAAC,6BAA6B,CAAC,SAAS,CAAC,6BAA6B,CAAC,kBAAkB,CAAC,6KAA6K,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,cAAc,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,+DAA+D,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,wCAAwC,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,0BAA0B,CAAC,QAAQ,CAAC,0BAA0B,CAAC,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,6CAA6C,CAAC,sBAAsB,CAAC,gCAAgC,CAAC,oBAAoB,CAAC,gCAAgC,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,0BAA0B,CAAC,aAAa,CAAC,cAAc,CAAC,WAAW,CAAC,cAAc,CAAC,WAAW,CAAC,cAAc,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,oBAAoB,CAAC,MAAM,CAAC,0BAA0B,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,gCAAgC,CAAC,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC,iBAAiB,CAAC,4CAA4C,CAAC,eAAe,CAAC,uBAAuB,CAAC,gBAAgB,CAAC,uCAAuC,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,cAAc,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,sCAAsC,CAAC,UAAU,CAAC,sCAAsC,CAAC,SAAS,CAAC,0DAA0D,CAAC,CAAC,CAAC;;;;"}