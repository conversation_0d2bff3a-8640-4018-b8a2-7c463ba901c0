import{u as r,b as a,c as d,d as _,e,f as u,w as p,h as c}from"./index-2c4e09fa.js";import{_ as i}from"./_plugin-vue_export-helper-c27b6911.js";const l={class:"not-found"},f={class:"not-found-content"},m={__name:"NotFound",setup(v){const t=r(),s=()=>{t.push("/")};return(x,o)=>{const n=a("el-button");return d(),_("div",l,[e("div",f,[o[1]||(o[1]=e("div",{class:"error-code"},"404",-1)),o[2]||(o[2]=e("div",{class:"error-message"},"页面未找到",-1)),o[3]||(o[3]=e("div",{class:"error-description"}," 抱歉，您访问的页面不存在或已被移除 ",-1)),u(n,{type:"primary",onClick:s},{default:p(()=>o[0]||(o[0]=[c(" 返回首页 ")])),_:1,__:[0]})])])}}},b=i(m,[["__scopeId","data-v-66aef4f1"]]);export{b as default};
