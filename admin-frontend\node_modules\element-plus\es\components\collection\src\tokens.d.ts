import type { Ref } from 'vue';
export type CollectionItem<T = Record<string, any>> = {
    ref: HTMLElement | null;
} & T;
export type ElCollectionInjectionContext = {
    itemMap: Map<HTMLElement, CollectionItem>;
    getItems: <T>() => CollectionItem<T>[];
    collectionRef: Ref<HTMLElement | undefined>;
};
export type ElCollectionItemInjectionContext = {
    collectionItemRef: Ref<HTMLElement | undefined>;
};
