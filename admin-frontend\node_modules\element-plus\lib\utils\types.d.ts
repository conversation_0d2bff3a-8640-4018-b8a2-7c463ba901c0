export { isArray, isFunction, isObject, isString, isDate, isPromise, isSymbol, isPlainObject, } from '@vue/shared';
export declare const isUndefined: (val: any) => val is undefined;
export declare const isBoolean: (val: any) => val is boolean;
export declare const isNumber: (val: any) => val is number;
export declare const isEmpty: (val: unknown) => boolean;
export declare const isElement: (e: unknown) => e is Element;
export declare const isPropAbsent: (prop: unknown) => prop is null | undefined;
export declare const isStringNumber: (val: string) => boolean;
export declare const isWindow: (val: unknown) => val is Window;
