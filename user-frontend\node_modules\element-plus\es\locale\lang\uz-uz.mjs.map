{"version": 3, "file": "uz-uz.mjs", "sources": ["../../../../../packages/locale/lang/uz-uz.ts"], "sourcesContent": ["export default {\n  name: 'uz-uz',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'Qabul qilish',\n      clear: '<PERSON><PERSON><PERSON>',\n    },\n    datepicker: {\n      now: '<PERSON><PERSON><PERSON>',\n      today: 'Bugun',\n      cancel: '<PERSON><PERSON> qilish',\n      clear: 'Tozalash',\n      confirm: 'Qabul qilish',\n      selectDate: '<PERSON>nni tanlash',\n      selectTime: 'Soatni tanlash',\n      startDate: 'Boshlanish sanasi',\n      startTime: '<PERSON><PERSON><PERSON><PERSON> vaqti',\n      endDate: 'Tugash sanasi',\n      endTime: '<PERSON>gash vaqti',\n      prevYear: 'Oʻtgan yil',\n      nextYear: 'Kelgusi yil',\n      prevMonth: 'Oʻtgan oy',\n      nextMonth: 'Kelgusi oy',\n      year: 'Yil',\n      month1: 'Yanvar',\n      month2: 'Fevral',\n      month3: 'Mart',\n      month4: 'Aprel',\n      month5: 'May',\n      month6: 'Iyun',\n      month7: 'Iyul',\n      month8: 'Avgust',\n      month9: '<PERSON><PERSON><PERSON><PERSON>',\n      month10: 'Oktabr',\n      month11: 'Noyabr',\n      month12: 'Dekabr',\n      week: 'Hafta',\n      weeks: {\n        sun: 'Yak',\n        mon: 'Dush',\n        tue: 'Sesh',\n        wed: 'Chor',\n        thu: 'Pay',\n        fri: 'Jum',\n        sat: 'Shan',\n      },\n      months: {\n        jan: 'Yan',\n        feb: 'Fev',\n        mar: 'Mar',\n        apr: 'Apr',\n        may: 'May',\n        jun: 'Iyun',\n        jul: 'Iyul',\n        aug: 'Avg',\n        sep: 'Sen',\n        oct: 'Okt',\n        nov: 'Noy',\n        dec: 'Dek',\n      },\n    },\n    select: {\n      loading: 'Yuklanmoqda',\n      noMatch: 'Mos maʼlumot yoʻq',\n      noData: 'Maʼlumot yoʻq',\n      placeholder: 'Tanladizngiz',\n    },\n    mention: {\n      loading: 'Yuklanmoqda',\n    },\n    cascader: {\n      noMatch: 'Mos maʼlumot topilmadi',\n      loading: 'Yuklanmoqda',\n      placeholder: 'Tanlash',\n      noData: 'Maʼlumot yoʻq',\n    },\n    pagination: {\n      goto: 'Oʻtish',\n      pagesize: '/sahifa',\n      total: 'Barchasi {total} ta',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Xabar',\n      confirm: 'Qabul qilish',\n      cancel: 'Bekor qilish',\n      error: 'Xatolik',\n    },\n    upload: {\n      deleteTip: 'Oʻchirish tugmasini bosib oʻchiring',\n      delete: 'Oʻchirish',\n      preview: 'Oldin koʻrish',\n      continue: 'Davom qilish',\n    },\n    table: {\n      emptyText: 'Boʻsh',\n      confirmFilter: 'Qabul qilish',\n      resetFilter: 'Oldingi holatga qaytarish',\n      clearFilter: 'Jami',\n      sumText: 'Summasi',\n    },\n    tree: {\n      emptyText: 'Maʼlumot yoʻq',\n    },\n    transfer: {\n      noMatch: 'Mos maʼlumot topilmadi',\n      noData: 'Maʼlumot yoʻq',\n      titles: ['1-jadval', '2-jadval'],\n      filterPlaceholder: 'Kalit soʻzni kiriting',\n      noCheckedFormat: '{total} ta element',\n      hasCheckedFormat: '{checked}/{total} ta belgilandi',\n    },\n    image: {\n      error: 'Xatolik',\n    },\n    pageHeader: {\n      title: 'Orqaga',\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": "AAAA,WAAe;AACf,EAAE,IAAI,EAAE,OAAO;AACf,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,cAAc;AAC7B,MAAM,KAAK,EAAE,UAAU;AACvB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,OAAO;AAClB,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,MAAM,EAAE,cAAc;AAC5B,MAAM,KAAK,EAAE,UAAU;AACvB,MAAM,OAAO,EAAE,cAAc;AAC7B,MAAM,UAAU,EAAE,eAAe;AACjC,MAAM,UAAU,EAAE,gBAAgB;AAClC,MAAM,SAAS,EAAE,mBAAmB;AACpC,MAAM,SAAS,EAAE,kBAAkB;AACnC,MAAM,OAAO,EAAE,eAAe;AAC9B,MAAM,OAAO,EAAE,cAAc;AAC7B,MAAM,QAAQ,EAAE,iBAAiB;AACjC,MAAM,QAAQ,EAAE,aAAa;AAC7B,MAAM,SAAS,EAAE,gBAAgB;AACjC,MAAM,SAAS,EAAE,YAAY;AAC7B,MAAM,IAAI,EAAE,KAAK;AACjB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,OAAO,EAAE,QAAQ;AACvB,MAAM,OAAO,EAAE,QAAQ;AACvB,MAAM,OAAO,EAAE,QAAQ;AACvB,MAAM,IAAI,EAAE,OAAO;AACnB,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,MAAM;AACnB,QAAQ,GAAG,EAAE,MAAM;AACnB,QAAQ,GAAG,EAAE,MAAM;AACnB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,MAAM;AACnB,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,MAAM;AACnB,QAAQ,GAAG,EAAE,MAAM;AACnB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,OAAO;AACP,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,aAAa;AAC5B,MAAM,OAAO,EAAE,6BAA6B;AAC5C,MAAM,MAAM,EAAE,yBAAyB;AACvC,MAAM,WAAW,EAAE,cAAc;AACjC,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,aAAa;AAC5B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,6BAA6B;AAC5C,MAAM,OAAO,EAAE,aAAa;AAC5B,MAAM,WAAW,EAAE,SAAS;AAC5B,MAAM,MAAM,EAAE,yBAAyB;AACvC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,aAAa;AACzB,MAAM,QAAQ,EAAE,SAAS;AACzB,MAAM,KAAK,EAAE,qBAAqB;AAClC,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,qBAAqB;AACjC,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,SAAS,EAAE,oBAAoB;AACrC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,OAAO,EAAE,cAAc;AAC7B,MAAM,MAAM,EAAE,cAAc;AAC5B,MAAM,KAAK,EAAE,SAAS;AACtB,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,+CAA+C;AAChE,MAAM,MAAM,EAAE,gBAAgB;AAC9B,MAAM,OAAO,EAAE,oBAAoB;AACnC,MAAM,QAAQ,EAAE,cAAc;AAC9B,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,YAAY;AAC7B,MAAM,aAAa,EAAE,cAAc;AACnC,MAAM,WAAW,EAAE,2BAA2B;AAC9C,MAAM,WAAW,EAAE,MAAM;AACzB,MAAM,OAAO,EAAE,SAAS;AACxB,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,yBAAyB;AAC1C,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,6BAA6B;AAC5C,MAAM,MAAM,EAAE,yBAAyB;AACvC,MAAM,MAAM,EAAE,CAAC,UAAU,EAAE,UAAU,CAAC;AACtC,MAAM,iBAAiB,EAAE,4BAA4B;AACrD,MAAM,eAAe,EAAE,oBAAoB;AAC3C,MAAM,gBAAgB,EAAE,iCAAiC;AACzD,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,SAAS;AACtB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,QAAQ;AACrB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,KAAK;AAC9B,MAAM,gBAAgB,EAAE,IAAI;AAC5B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,SAAS,EAAE,kCAAkC;AACnD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}