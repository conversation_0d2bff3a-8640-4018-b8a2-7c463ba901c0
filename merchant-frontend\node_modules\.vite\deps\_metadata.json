{"hash": "dd0790c2", "browserHash": "1c71e85d", "optimized": {"@element-plus/icons-vue": {"src": "../../@element-plus/icons-vue/dist/index.js", "file": "@element-plus_icons-vue.js", "fileHash": "4679f3b0", "needsInterop": false}, "element-plus": {"src": "../../element-plus/es/index.mjs", "file": "element-plus.js", "fileHash": "b127a495", "needsInterop": false}, "element-plus/dist/locale/zh-cn.mjs": {"src": "../../element-plus/dist/locale/zh-cn.mjs", "file": "element-plus_dist_locale_zh-cn__mjs.js", "fileHash": "73191e97", "needsInterop": false}, "pinia": {"src": "../../pinia/dist/pinia.mjs", "file": "pinia.js", "fileHash": "0ade7250", "needsInterop": false}, "vue": {"src": "../../vue/dist/vue.runtime.esm-bundler.js", "file": "vue.js", "fileHash": "2fdb487c", "needsInterop": false}, "vue-router": {"src": "../../vue-router/dist/vue-router.mjs", "file": "vue-router.js", "fileHash": "bd3d02fb", "needsInterop": false}, "axios": {"src": "../../../../shared/node_modules/axios/index.js", "file": "axios.js", "fileHash": "15ea8ee9", "needsInterop": false}}, "chunks": {"chunk-HYZ2CRGS": {"file": "chunk-HYZ2CRGS.js"}, "chunk-THX7JSAO": {"file": "chunk-THX7JSAO.js"}, "chunk-YFT6OQ5R": {"file": "chunk-YFT6OQ5R.js"}, "chunk-UGLA4GWR": {"file": "chunk-UGLA4GWR.js"}, "chunk-5WWUZCGV": {"file": "chunk-5WWUZCGV.js"}}}