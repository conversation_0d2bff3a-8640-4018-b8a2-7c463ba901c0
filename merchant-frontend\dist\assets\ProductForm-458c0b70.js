import{s as Q,u as W,r as F,x as X,a as Y,o as Z,b as n,c as g,d as y,f as e,w as l,E as d,e as v,t as S,A as q,h as i,y as ee,F as te,D as le,G as oe,B as ae}from"./index-2c4e09fa.js";import{c as se,m as V,A as re}from"./api-7a2a6e2d.js";import{_ as ne}from"./_plugin-vue_export-helper-c27b6911.js";const ue={class:"product-form-page"},de={class:"page-header"},ie=["src"],pe={class:"specs-section"},me={__name:"ProductForm",setup(ce){const h=Q(),U=W(),w=se(),k=F(),x=F(!1),m=X(()=>h.params.id!==void 0),o=Y({id:null,name:"",category:"",price:0,stock:0,status:1,image:"",description:"",content:"",specs:[]}),E={name:[{required:!0,message:"请输入商品名称",trigger:"blur"},{min:2,max:50,message:"商品名称长度在 2 到 50 个字符",trigger:"blur"}],category:[{required:!0,message:"请选择商品分类",trigger:"change"}],price:[{required:!0,message:"请输入商品价格",trigger:"blur"},{type:"number",min:.01,message:"价格必须大于0",trigger:"blur"}],stock:[{required:!0,message:"请输入库存数量",trigger:"blur"},{type:"number",min:0,message:"库存不能小于0",trigger:"blur"}],description:[{required:!0,message:"请输入商品描述",trigger:"blur"},{min:10,max:500,message:"商品描述长度在 10 到 500 个字符",trigger:"blur"}]},I=`${re}${V.products.upload}`,M={Authorization:`Bearer ${localStorage.getItem("token")}`},R=async r=>{try{const t=await w.get(`${V.products.detail}/${r}`);if(t.code===200){const a=t.data;Object.assign(o,{id:a.id,name:a.name,category:a.category,price:a.price,stock:a.stock,status:a.status,image:a.image,description:a.description,content:a.content,specs:a.specs||[]})}else d.error("获取商品详情失败")}catch(t){console.error("获取商品详情失败:",t),d.error("获取商品详情失败")}},z=r=>{r.code===200?(o.image=r.data.url,d.success("图片上传成功")):d.error("图片上传失败")},D=r=>{const t=r.type==="image/jpeg"||r.type==="image/png",a=r.size/1024/1024<2;return t?a?!0:(d.error("上传图片大小不能超过 2MB!"),!1):(d.error("上传图片只能是 JPG/PNG 格式!"),!1)},G=()=>{o.specs.push({name:"",value:""})},N=r=>{o.specs.splice(r,1)},j=async()=>{try{await k.value.validate(),x.value=!0;const r={...o,specs:o.specs.filter(a=>a.name&&a.value)};let t;m.value?t=await w.put(`${V.products.update}/${o.id}`,r):t=await w.post(V.products.create,r),t.code===200?(d.success(m.value?"商品更新成功":"商品创建成功"),U.push("/products")):d.error(t.message||"操作失败")}catch(r){console.error("提交失败:",r),d.error("操作失败")}finally{x.value=!1}},L=()=>{k.value.resetFields(),o.specs=[]},B=()=>{U.push("/products")};return Z(()=>{m.value&&R(h.params.id)}),(r,t)=>{const a=n("el-icon"),p=n("el-button"),c=n("el-input"),u=n("el-form-item"),_=n("el-col"),f=n("el-option"),J=n("el-select"),C=n("el-row"),P=n("el-input-number"),$=n("el-radio"),H=n("el-radio-group"),O=n("el-upload"),T=n("el-form"),K=n("el-card");return g(),y("div",ue,[e(K,null,{header:l(()=>[v("div",de,[v("h2",null,S(m.value?"编辑商品":"添加商品"),1),e(p,{onClick:B},{default:l(()=>[e(a,null,{default:l(()=>[e(q(oe))]),_:1}),t[7]||(t[7]=i(" 返回列表 "))]),_:1,__:[7]})])]),default:l(()=>[e(T,{ref_key:"formRef",ref:k,model:o,rules:E,"label-width":"120px",class:"product-form"},{default:l(()=>[e(C,{gutter:20},{default:l(()=>[e(_,{span:12},{default:l(()=>[e(u,{label:"商品名称",prop:"name"},{default:l(()=>[e(c,{modelValue:o.name,"onUpdate:modelValue":t[0]||(t[0]=s=>o.name=s),placeholder:"请输入商品名称",maxlength:"50","show-word-limit":""},null,8,["modelValue"])]),_:1})]),_:1}),e(_,{span:12},{default:l(()=>[e(u,{label:"商品分类",prop:"category"},{default:l(()=>[e(J,{modelValue:o.category,"onUpdate:modelValue":t[1]||(t[1]=s=>o.category=s),placeholder:"请选择商品分类",style:{width:"100%"}},{default:l(()=>[e(f,{label:"蔬菜类",value:"vegetables"}),e(f,{label:"水果类",value:"fruits"}),e(f,{label:"粮食类",value:"grains"}),e(f,{label:"肉类",value:"meat"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(C,{gutter:20},{default:l(()=>[e(_,{span:8},{default:l(()=>[e(u,{label:"商品价格",prop:"price"},{default:l(()=>[e(P,{modelValue:o.price,"onUpdate:modelValue":t[2]||(t[2]=s=>o.price=s),min:0,precision:2,step:.1,style:{width:"100%"},placeholder:"请输入价格"},null,8,["modelValue"])]),_:1})]),_:1}),e(_,{span:8},{default:l(()=>[e(u,{label:"库存数量",prop:"stock"},{default:l(()=>[e(P,{modelValue:o.stock,"onUpdate:modelValue":t[3]||(t[3]=s=>o.stock=s),min:0,step:1,style:{width:"100%"},placeholder:"请输入库存"},null,8,["modelValue"])]),_:1})]),_:1}),e(_,{span:8},{default:l(()=>[e(u,{label:"商品状态",prop:"status"},{default:l(()=>[e(H,{modelValue:o.status,"onUpdate:modelValue":t[4]||(t[4]=s=>o.status=s)},{default:l(()=>[e($,{label:1},{default:l(()=>t[8]||(t[8]=[i("上架")])),_:1,__:[8]}),e($,{label:0},{default:l(()=>t[9]||(t[9]=[i("下架")])),_:1,__:[9]})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(u,{label:"商品图片",prop:"image"},{default:l(()=>[e(O,{class:"image-uploader",action:I,headers:M,"show-file-list":!1,"on-success":z,"before-upload":D},{default:l(()=>[o.image?(g(),y("img",{key:0,src:o.image,class:"image-preview"},null,8,ie)):(g(),ee(a,{key:1,class:"image-uploader-icon"},{default:l(()=>[e(q(ae))]),_:1}))]),_:1}),t[10]||(t[10]=v("div",{class:"upload-tip"}," 只能上传jpg/png文件，且不超过2MB ",-1))]),_:1,__:[10]}),e(u,{label:"商品描述",prop:"description"},{default:l(()=>[e(c,{modelValue:o.description,"onUpdate:modelValue":t[5]||(t[5]=s=>o.description=s),type:"textarea",rows:4,placeholder:"请输入商品描述",maxlength:"500","show-word-limit":""},null,8,["modelValue"])]),_:1}),e(u,{label:"商品详情",prop:"content"},{default:l(()=>[e(c,{modelValue:o.content,"onUpdate:modelValue":t[6]||(t[6]=s=>o.content=s),type:"textarea",rows:6,placeholder:"请输入商品详细信息",maxlength:"1000","show-word-limit":""},null,8,["modelValue"])]),_:1}),e(u,{label:"商品规格"},{default:l(()=>[v("div",pe,[(g(!0),y(te,null,le(o.specs,(s,A)=>(g(),y("div",{key:A,class:"spec-item"},[e(c,{modelValue:s.name,"onUpdate:modelValue":b=>s.name=b,placeholder:"规格名称",style:{width:"150px","margin-right":"10px"}},null,8,["modelValue","onUpdate:modelValue"]),e(c,{modelValue:s.value,"onUpdate:modelValue":b=>s.value=b,placeholder:"规格值",style:{width:"150px","margin-right":"10px"}},null,8,["modelValue","onUpdate:modelValue"]),e(p,{type:"danger",size:"small",onClick:b=>N(A)},{default:l(()=>t[11]||(t[11]=[i(" 删除 ")])),_:2,__:[11]},1032,["onClick"])]))),128)),e(p,{type:"primary",size:"small",onClick:G},{default:l(()=>t[12]||(t[12]=[i(" 添加规格 ")])),_:1,__:[12]})])]),_:1}),e(u,null,{default:l(()=>[e(p,{type:"primary",loading:x.value,onClick:j},{default:l(()=>[i(S(m.value?"更新商品":"创建商品"),1)]),_:1},8,["loading"]),e(p,{onClick:L},{default:l(()=>t[13]||(t[13]=[i("重置")])),_:1,__:[13]}),e(p,{onClick:B},{default:l(()=>t[14]||(t[14]=[i("取消")])),_:1,__:[14]})]),_:1})]),_:1},8,["model"])]),_:1})])}}},be=ne(me,[["__scopeId","data-v-2018434c"]]);export{be as default};
