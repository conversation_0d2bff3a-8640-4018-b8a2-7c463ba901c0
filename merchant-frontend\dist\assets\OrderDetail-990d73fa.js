import{s as K,u as Q,r as T,o as U,b as o,j as W,c as d,d as y,f as l,w as s,e as t,A as m,h as c,k as X,t as r,y as x,l as B,F as Y,D as Z,z as O,E as u,G as $,I as ee,J as te,K as le,H as se}from"./index-2c4e09fa.js";import{c as ae,m as h}from"./api-7a2a6e2d.js";import{a as ne}from"./utils-750ce239.js";import{_ as oe}from"./_plugin-vue_export-helper-c27b6911.js";const re={class:"order-detail-page"},ie={class:"page-header"},de={class:"order-content"},ue={key:0,class:"order-info"},ce={class:"info-item"},pe={class:"info-item"},_e={class:"info-item"},me={class:"info-item"},fe={class:"amount"},ve={class:"info-item"},ye={class:"info-item"},ge={class:"info-item"},we={class:"info-item"},be={class:"info-item"},xe={class:"amount"},he={class:"action-buttons"},ke={key:1,class:"no-data"},De={__name:"OrderDetail",setup(Te){const C=K(),N=Q(),g=ae(),w=T(!1),a=T(null),k={pending:{text:"待付款",type:"warning"},paid:{text:"待发货",type:"info"},shipped:{text:"已发货",type:"primary"},completed:{text:"已完成",type:"success"},cancelled:{text:"已取消",type:"danger"}},A=n=>{var e;return((e=k[n])==null?void 0:e.text)||n},S=n=>{var e;return((e=k[n])==null?void 0:e.type)||"info"},b={id:1,orderNo:"ORD202507070001",status:"paid",totalAmount:25.6,paymentMethod:"微信支付",createTime:"2025-07-07 10:30:00",customerName:"张三",customerPhone:"13800138000",deliveryAddress:"北京市朝阳区某某街道某某小区1号楼101室",remark:"请尽快发货，谢谢！",products:[{id:1,name:"新鲜有机西红柿",spec:"500g/袋",price:12.8,quantity:2,image:"https://via.placeholder.com/60x60?text=西红柿"}],logs:[{id:1,content:"订单已创建",createTime:"2025-07-07 10:30:00",type:"primary"},{id:2,content:"订单已支付",createTime:"2025-07-07 10:35:00",type:"success"}]},M=async()=>{w.value=!0;try{const n=C.params.id,e=await g.get(h.orders.detail.replace(":id",n));e.code===200?a.value=e.data||b:a.value=b}catch(n){console.error("获取订单详情失败:",n),a.value=b}finally{w.value=!1}},E=()=>{N.push("/orders")},V=async()=>{try{await O.confirm("确定要发货此订单吗？","发货确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),(await g.put(h.orders.updateStatus,{id:a.value.id,status:"shipped"})).code===200?(u.success("订单已发货"),a.value.status="shipped",a.value.logs.push({id:Date.now(),content:"订单已发货",createTime:new Date().toLocaleString(),type:"success"})):u.error("发货失败")}catch(n){n!=="cancel"&&(console.error("发货失败:",n),u.error("发货失败"))}},q=async()=>{try{await O.confirm("确定要取消此订单吗？","取消确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),(await g.put(h.orders.updateStatus,{id:a.value.id,status:"cancelled"})).code===200?(u.success("订单已取消"),a.value.status="cancelled",a.value.logs.push({id:Date.now(),content:"订单已取消",createTime:new Date().toLocaleString(),type:"danger"})):u.error("取消失败")}catch(n){n!=="cancel"&&(console.error("取消失败:",n),u.error("取消失败"))}},F=()=>{u.info("打印功能开发中...")},I=()=>{u.info("导出功能开发中...")};return U(()=>{M()}),(n,e)=>{const f=o("el-icon"),v=o("el-button"),L=o("el-tag"),p=o("el-card"),D=o("el-col"),R=o("el-row"),P=o("el-image"),_=o("el-table-column"),j=o("el-table"),z=o("el-timeline-item"),G=o("el-timeline"),H=o("el-empty"),J=W("loading");return d(),y("div",re,[l(p,null,{header:s(()=>[t("div",ie,[e[1]||(e[1]=t("h2",null,"订单详情",-1)),l(v,{onClick:E},{default:s(()=>[l(f,null,{default:s(()=>[l(m($))]),_:1}),e[0]||(e[0]=c(" 返回列表 "))]),_:1,__:[0]})])]),default:s(()=>[X((d(),y("div",de,[a.value?(d(),y("div",ue,[l(R,{gutter:20},{default:s(()=>[l(D,{span:12},{default:s(()=>[l(p,{class:"info-card"},{header:s(()=>e[2]||(e[2]=[t("h3",null,"订单信息",-1)])),default:s(()=>[t("div",ce,[e[3]||(e[3]=t("label",null,"订单号：",-1)),t("span",null,r(a.value.orderNo),1)]),t("div",pe,[e[4]||(e[4]=t("label",null,"订单状态：",-1)),l(L,{type:S(a.value.status)},{default:s(()=>[c(r(A(a.value.status)),1)]),_:1},8,["type"])]),t("div",_e,[e[5]||(e[5]=t("label",null,"下单时间：",-1)),t("span",null,r(m(ne)(a.value.createTime)),1)]),t("div",me,[e[6]||(e[6]=t("label",null,"订单金额：",-1)),t("span",fe,"¥"+r(a.value.totalAmount),1)]),t("div",ve,[e[7]||(e[7]=t("label",null,"支付方式：",-1)),t("span",null,r(a.value.paymentMethod||"在线支付"),1)])]),_:1})]),_:1}),l(D,{span:12},{default:s(()=>[l(p,{class:"info-card"},{header:s(()=>e[8]||(e[8]=[t("h3",null,"客户信息",-1)])),default:s(()=>[t("div",ye,[e[9]||(e[9]=t("label",null,"客户姓名：",-1)),t("span",null,r(a.value.customerName),1)]),t("div",ge,[e[10]||(e[10]=t("label",null,"联系电话：",-1)),t("span",null,r(a.value.customerPhone),1)]),t("div",we,[e[11]||(e[11]=t("label",null,"收货地址：",-1)),t("span",null,r(a.value.deliveryAddress),1)]),t("div",be,[e[12]||(e[12]=t("label",null,"备注信息：",-1)),t("span",null,r(a.value.remark||"无"),1)])]),_:1})]),_:1})]),_:1}),l(p,{class:"product-card"},{header:s(()=>e[13]||(e[13]=[t("h3",null,"商品信息",-1)])),default:s(()=>[l(j,{data:a.value.products,style:{width:"100%"}},{default:s(()=>[l(_,{label:"商品图片",width:"100"},{default:s(({row:i})=>[l(P,{src:i.image,style:{width:"60px",height:"60px"},fit:"cover"},null,8,["src"])]),_:1}),l(_,{prop:"name",label:"商品名称","min-width":"150"}),l(_,{prop:"spec",label:"规格",width:"120"}),l(_,{prop:"price",label:"单价",width:"100"},{default:s(({row:i})=>[t("span",null,"¥"+r(i.price),1)]),_:1}),l(_,{prop:"quantity",label:"数量",width:"80"}),l(_,{label:"小计",width:"100"},{default:s(({row:i})=>[t("span",xe,"¥"+r((i.price*i.quantity).toFixed(2)),1)]),_:1})]),_:1},8,["data"])]),_:1}),l(p,{class:"action-card"},{header:s(()=>e[14]||(e[14]=[t("h3",null,"订单操作",-1)])),default:s(()=>[t("div",he,[a.value.status==="paid"?(d(),x(v,{key:0,type:"success",onClick:V},{default:s(()=>[l(f,null,{default:s(()=>[l(m(ee))]),_:1}),e[15]||(e[15]=c(" 确认发货 "))]),_:1,__:[15]})):B("",!0),a.value.status==="pending"?(d(),x(v,{key:1,type:"danger",onClick:q},{default:s(()=>[l(f,null,{default:s(()=>[l(m(te))]),_:1}),e[16]||(e[16]=c(" 取消订单 "))]),_:1,__:[16]})):B("",!0),l(v,{onClick:F},{default:s(()=>[l(f,null,{default:s(()=>[l(m(le))]),_:1}),e[17]||(e[17]=c(" 打印订单 "))]),_:1,__:[17]}),l(v,{onClick:I},{default:s(()=>[l(f,null,{default:s(()=>[l(m(se))]),_:1}),e[18]||(e[18]=c(" 导出订单 "))]),_:1,__:[18]})])]),_:1}),l(p,{class:"log-card"},{header:s(()=>e[19]||(e[19]=[t("h3",null,"订单日志",-1)])),default:s(()=>[l(G,null,{default:s(()=>[(d(!0),y(Y,null,Z(a.value.logs,i=>(d(),x(z,{key:i.id,timestamp:i.createTime,type:i.type},{default:s(()=>[c(r(i.content),1)]),_:2},1032,["timestamp","type"]))),128))]),_:1})]),_:1})])):(d(),y("div",ke,[l(H,{description:"订单不存在或已删除"})]))])),[[J,w.value]])]),_:1})])}}},Ae=oe(De,[["__scopeId","data-v-e467503b"]]);export{Ae as default};
