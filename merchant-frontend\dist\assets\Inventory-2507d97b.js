import{r as I,a as C,o as dt,b as i,j as ut,c as A,d as ct,f as e,w as o,e as s,A as V,h as d,t as u,k as pt,y as _t,n as mt,E as y,z as ft,L as gt,H as vt,C as B}from"./index-2c4e09fa.js";import{c as kt,m as D}from"./api-7a2a6e2d.js";import{a as yt}from"./utils-750ce239.js";import{_ as bt}from"./_plugin-vue_export-helper-c27b6911.js";const St={class:"inventory-page"},ht={class:"page-header"},xt={class:"header-actions"},wt={class:"stats-section"},Vt={class:"stat-content"},Ct={class:"stat-value"},Ut={class:"stat-content"},zt={class:"stat-value warning"},It={class:"stat-content"},Tt={class:"stat-value danger"},qt={class:"stat-content"},Et={class:"stat-value success"},Ot={class:"search-section"},jt={class:"product-info"},At={class:"product-name"},Bt={class:"product-category"},Dt={class:"stock-value"},Lt={class:"pagination"},$t={__name:"Inventory",setup(Ft){const T=kt(),U=I(!1),v=I([]),L=I([]),b=C({totalProducts:0,lowStock:0,outOfStock:0,totalValue:0}),m=C({name:"",category:"",stockStatus:""}),r=C({page:1,size:10,total:0}),n=C({visible:!1,product:null,form:{type:"in",quantity:1,reason:""}}),$={type:[{required:!0,message:"请选择调整类型",trigger:"change"}],quantity:[{required:!0,message:"请输入调整数量",trigger:"blur"}],reason:[{required:!0,message:"请输入调整原因",trigger:"blur"}]},k=[{id:1,name:"新鲜有机西红柿",category:"蔬菜类",sku:"VEG001",image:"https://via.placeholder.com/50x50?text=西红柿",stock:150,minStock:50,maxStock:500,price:12.8,lastUpdateTime:"2025-07-07 10:30:00"},{id:2,name:"优质苹果",category:"水果类",sku:"FRU001",image:"https://via.placeholder.com/50x50?text=苹果",stock:25,minStock:30,maxStock:200,price:8.5,lastUpdateTime:"2025-07-07 09:15:00"},{id:3,name:"东北大米",category:"粮食类",sku:"GRA001",image:"https://via.placeholder.com/50x50?text=大米",stock:0,minStock:20,maxStock:100,price:5,lastUpdateTime:"2025-07-06 16:45:00"},{id:4,name:"新鲜猪肉",category:"肉类",sku:"MEA001",image:"https://via.placeholder.com/50x50?text=猪肉",stock:80,minStock:20,maxStock:150,price:35,lastUpdateTime:"2025-07-07 14:20:00"}],F=(l,t)=>l===0?"stock-out":l<=t?"stock-low":"stock-normal",M=(l,t)=>l===0?"缺货":l<=t?"库存不足":"库存充足",P=(l,t)=>l===0?"danger":l<=t?"warning":"success",S=async()=>{U.value=!0;try{const l={page:r.page,size:r.size,...m},t=await T.get(D.inventory.list,{params:l});t.code===200?(v.value=t.data.records||k,r.total=t.data.total||k.length):(v.value=k,r.total=k.length),q()}catch(l){console.error("获取库存列表失败:",l),v.value=k,r.total=k.length,q()}finally{U.value=!1}},q=()=>{const l={totalProducts:v.value.length,lowStock:0,outOfStock:0,totalValue:0};v.value.forEach(t=>{t.stock===0?l.outOfStock++:t.stock<=t.minStock&&l.lowStock++,l.totalValue+=t.stock*t.price}),l.totalValue=(l.totalValue/1e4).toFixed(1),Object.assign(b,l)},h=()=>{r.page=1,S()},R=()=>{Object.assign(m,{name:"",category:"",stockStatus:""}),h()},N=l=>{L.value=l},G=l=>{r.size=l,r.page=1,S()},H=l=>{r.page=l,S()},K=l=>{n.product=l,n.visible=!0},J=()=>{n.product=null,Object.assign(n.form,{type:"in",quantity:1,reason:""})},Q=async()=>{try{(await T.put(D.inventory.update,{id:n.product.id,...n.form})).code===200?(y.success("库存调整成功"),n.visible=!1,S()):y.error("库存调整失败")}catch(l){console.error("库存调整失败:",l),y.error("库存调整失败")}},W=l=>{y.info("库存日志功能开发中...")},X=()=>{const l=v.value.filter(c=>c.stock<=c.minStock||c.stock===0);if(l.length===0){y.success("当前没有库存预警商品");return}const t=l.map(c=>`${c.name}：当前库存 ${c.stock}，最低库存 ${c.minStock}`).join(`
`);ft.alert(t,"库存预警",{confirmButtonText:"知道了",type:"warning"})},Y=()=>{y.info("导出功能开发中...")};return dt(()=>{S()}),(l,t)=>{const c=i("el-icon"),f=i("el-button"),x=i("el-card"),g=i("el-col"),E=i("el-row"),O=i("el-input"),_=i("el-option"),j=i("el-select"),p=i("el-table-column"),Z=i("el-image"),tt=i("el-tag"),et=i("el-table"),ot=i("el-pagination"),w=i("el-form-item"),z=i("el-radio"),at=i("el-radio-group"),lt=i("el-input-number"),st=i("el-form"),nt=i("el-dialog"),it=ut("loading");return A(),ct("div",St,[e(x,null,{header:o(()=>[s("div",ht,[t[12]||(t[12]=s("h2",null,"库存管理",-1)),s("div",xt,[e(f,{type:"warning",onClick:X},{default:o(()=>[e(c,null,{default:o(()=>[e(V(gt))]),_:1}),t[10]||(t[10]=d(" 库存预警 "))]),_:1,__:[10]}),e(f,{onClick:Y},{default:o(()=>[e(c,null,{default:o(()=>[e(V(vt))]),_:1}),t[11]||(t[11]=d(" 导出库存 "))]),_:1,__:[11]})])])]),default:o(()=>[s("div",wt,[e(E,{gutter:20},{default:o(()=>[e(g,{span:6},{default:o(()=>[e(x,{class:"stat-card"},{default:o(()=>[s("div",Vt,[s("div",Ct,u(b.totalProducts),1),t[13]||(t[13]=s("div",{class:"stat-label"},"商品总数",-1))])]),_:1})]),_:1}),e(g,{span:6},{default:o(()=>[e(x,{class:"stat-card"},{default:o(()=>[s("div",Ut,[s("div",zt,u(b.lowStock),1),t[14]||(t[14]=s("div",{class:"stat-label"},"库存不足",-1))])]),_:1})]),_:1}),e(g,{span:6},{default:o(()=>[e(x,{class:"stat-card"},{default:o(()=>[s("div",It,[s("div",Tt,u(b.outOfStock),1),t[15]||(t[15]=s("div",{class:"stat-label"},"缺货商品",-1))])]),_:1})]),_:1}),e(g,{span:6},{default:o(()=>[e(x,{class:"stat-card"},{default:o(()=>[s("div",qt,[s("div",Et,u(b.totalValue),1),t[16]||(t[16]=s("div",{class:"stat-label"},"库存总值(万元)",-1))])]),_:1})]),_:1})]),_:1})]),s("div",Ot,[e(E,{gutter:20},{default:o(()=>[e(g,{span:6},{default:o(()=>[e(O,{modelValue:m.name,"onUpdate:modelValue":t[0]||(t[0]=a=>m.name=a),placeholder:"搜索商品名称",clearable:"",onChange:h},{prefix:o(()=>[e(c,null,{default:o(()=>[e(V(B))]),_:1})]),_:1},8,["modelValue"])]),_:1}),e(g,{span:4},{default:o(()=>[e(j,{modelValue:m.category,"onUpdate:modelValue":t[1]||(t[1]=a=>m.category=a),placeholder:"商品分类",clearable:"",onChange:h},{default:o(()=>[e(_,{label:"全部分类",value:""}),e(_,{label:"蔬菜类",value:"vegetables"}),e(_,{label:"水果类",value:"fruits"}),e(_,{label:"肉类",value:"meat"}),e(_,{label:"粮食类",value:"grains"})]),_:1},8,["modelValue"])]),_:1}),e(g,{span:4},{default:o(()=>[e(j,{modelValue:m.stockStatus,"onUpdate:modelValue":t[2]||(t[2]=a=>m.stockStatus=a),placeholder:"库存状态",clearable:"",onChange:h},{default:o(()=>[e(_,{label:"全部状态",value:""}),e(_,{label:"库存充足",value:"sufficient"}),e(_,{label:"库存不足",value:"low"}),e(_,{label:"缺货",value:"out"})]),_:1},8,["modelValue"])]),_:1}),e(g,{span:4},{default:o(()=>[e(f,{type:"primary",onClick:h},{default:o(()=>[e(c,null,{default:o(()=>[e(V(B))]),_:1}),t[17]||(t[17]=d(" 搜索 "))]),_:1,__:[17]}),e(f,{onClick:R},{default:o(()=>t[18]||(t[18]=[d("重置")])),_:1,__:[18]})]),_:1})]),_:1})]),pt((A(),_t(et,{data:v.value,style:{width:"100%"},onSelectionChange:N},{default:o(()=>[e(p,{type:"selection",width:"55"}),e(p,{label:"商品信息","min-width":"200"},{default:o(({row:a})=>[s("div",jt,[e(Z,{src:a.image,style:{width:"50px",height:"50px","margin-right":"12px"},fit:"cover"},null,8,["src"]),s("div",null,[s("div",At,u(a.name),1),s("div",Bt,u(a.category),1)])])]),_:1}),e(p,{prop:"sku",label:"SKU",width:"120"}),e(p,{label:"当前库存",width:"120"},{default:o(({row:a})=>[s("span",{class:mt(F(a.stock,a.minStock))},u(a.stock),3)]),_:1}),e(p,{prop:"minStock",label:"最低库存",width:"100"}),e(p,{prop:"maxStock",label:"最高库存",width:"100"}),e(p,{label:"库存状态",width:"100"},{default:o(({row:a})=>[e(tt,{type:P(a.stock,a.minStock)},{default:o(()=>[d(u(M(a.stock,a.minStock)),1)]),_:2},1032,["type"])]),_:1}),e(p,{prop:"price",label:"单价",width:"100"},{default:o(({row:a})=>[s("span",null,"¥"+u(a.price),1)]),_:1}),e(p,{label:"库存价值",width:"120"},{default:o(({row:a})=>[s("span",Dt,"¥"+u((a.stock*a.price).toFixed(2)),1)]),_:1}),e(p,{prop:"lastUpdateTime",label:"更新时间",width:"150"},{default:o(({row:a})=>[d(u(V(yt)(a.lastUpdateTime)),1)]),_:1}),e(p,{label:"操作",width:"180",fixed:"right"},{default:o(({row:a})=>[e(f,{type:"primary",size:"small",onClick:rt=>K(a)},{default:o(()=>t[19]||(t[19]=[d(" 调整库存 ")])),_:2,__:[19]},1032,["onClick"]),e(f,{type:"info",size:"small",onClick:rt=>W(a)},{default:o(()=>t[20]||(t[20]=[d(" 库存日志 ")])),_:2,__:[20]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[it,U.value]]),s("div",Lt,[e(ot,{"current-page":r.page,"onUpdate:currentPage":t[3]||(t[3]=a=>r.page=a),"page-size":r.size,"onUpdate:pageSize":t[4]||(t[4]=a=>r.size=a),"page-sizes":[10,20,50,100],total:r.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:G,onCurrentChange:H},null,8,["current-page","page-size","total"])])]),_:1}),e(nt,{modelValue:n.visible,"onUpdate:modelValue":t[9]||(t[9]=a=>n.visible=a),title:"调整库存",width:"500px",onClose:J},{footer:o(()=>[e(f,{onClick:t[8]||(t[8]=a=>n.visible=!1)},{default:o(()=>t[24]||(t[24]=[d("取消")])),_:1,__:[24]}),e(f,{type:"primary",onClick:Q},{default:o(()=>t[25]||(t[25]=[d("确定")])),_:1,__:[25]})]),default:o(()=>[e(st,{ref:"stockFormRef",model:n.form,rules:$,"label-width":"100px"},{default:o(()=>[e(w,{label:"商品名称"},{default:o(()=>{var a;return[s("span",null,u((a=n.product)==null?void 0:a.name),1)]}),_:1}),e(w,{label:"当前库存"},{default:o(()=>{var a;return[s("span",null,u((a=n.product)==null?void 0:a.stock),1)]}),_:1}),e(w,{label:"调整类型",prop:"type"},{default:o(()=>[e(at,{modelValue:n.form.type,"onUpdate:modelValue":t[5]||(t[5]=a=>n.form.type=a)},{default:o(()=>[e(z,{label:"in"},{default:o(()=>t[21]||(t[21]=[d("入库")])),_:1,__:[21]}),e(z,{label:"out"},{default:o(()=>t[22]||(t[22]=[d("出库")])),_:1,__:[22]}),e(z,{label:"set"},{default:o(()=>t[23]||(t[23]=[d("设置")])),_:1,__:[23]})]),_:1},8,["modelValue"])]),_:1}),e(w,{label:"调整数量",prop:"quantity"},{default:o(()=>[e(lt,{modelValue:n.form.quantity,"onUpdate:modelValue":t[6]||(t[6]=a=>n.form.quantity=a),min:1,max:9999,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),e(w,{label:"调整原因",prop:"reason"},{default:o(()=>[e(O,{modelValue:n.form.reason,"onUpdate:modelValue":t[7]||(t[7]=a=>n.form.reason=a),type:"textarea",rows:3,placeholder:"请输入调整原因"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},Gt=bt($t,[["__scopeId","data-v-02c89d23"]]);export{Gt as default};
