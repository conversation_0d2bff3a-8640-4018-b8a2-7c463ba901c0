import type { SetupContext } from 'vue';
import type { CheckboxValueType } from 'element-plus/es/components/checkbox';
import type { TransferPanelEmits, TransferPanelProps, TransferPanelState } from '../transfer-panel';
export declare const useCheck: (props: TransferPanelProps, panelState: TransferPanelState, emit: SetupContext<TransferPanelEmits>["emit"]) => {
    filteredData: import("vue").ComputedRef<import("../transfer").TransferDataItem[]>;
    checkableData: import("vue").ComputedRef<import("../transfer").TransferDataItem[]>;
    checkedSummary: import("vue").ComputedRef<string>;
    isIndeterminate: import("vue").ComputedRef<boolean>;
    updateAllChecked: () => void;
    handleAllCheckedChange: (value: CheckboxValueType) => void;
};
