import{u as le,r as P,a as U,o as ne,b as l,j as re,c as T,d as $,f as t,w as s,e as f,A as x,h as d,k as ce,y as ie,t as z,n as de,l as ue,E as n,z as D,B as pe,C as N}from"./index-2c4e09fa.js";import{c as v,m as y}from"./api-7a2a6e2d.js";import{a as _e}from"./utils-750ce239.js";import{_ as me}from"./_plugin-vue_export-helper-c27b6911.js";const ge={class:"products-page"},fe={class:"page-header"},he={class:"search-section"},ve={class:"price"},ye={key:0,class:"batch-actions"},be={class:"actions"},Ce={class:"pagination"},ke={__name:"Products",setup(we){const V=le(),S=P(!1),k=P([]),g=P([]),p=U({name:"",category:"",status:""}),r=U({page:1,size:10,total:0}),j={vegetables:"蔬菜类",fruits:"水果类",grains:"粮食类",meat:"肉类"},E=a=>j[a]||a,h=[{id:1,name:"新鲜有机西红柿",category:"vegetables",price:12.8,stock:150,sales:89,status:1,image:"https://via.placeholder.com/60x60?text=西红柿",createTime:"2025-07-01 10:30:00"},{id:2,name:"优质苹果",category:"fruits",price:8.5,stock:5,sales:156,status:1,image:"https://via.placeholder.com/60x60?text=苹果",createTime:"2025-07-02 14:20:00"},{id:3,name:"东北大米",category:"grains",price:25,stock:80,sales:45,status:0,image:"https://via.placeholder.com/60x60?text=大米",createTime:"2025-07-03 09:15:00"},{id:4,name:"新鲜猪肉",category:"meat",price:35,stock:25,sales:78,status:1,image:"https://via.placeholder.com/60x60?text=猪肉",createTime:"2025-07-04 16:45:00"}],_=async()=>{S.value=!0;try{const a={page:r.page,size:r.size,...p},e=v(),c=await y.products.list(e,a);c.code===0?(k.value=c.data.list||h,r.total=c.data.total||h.length):(k.value=h,r.total=h.length)}catch(a){console.error("获取商品列表失败:",a),k.value=h,r.total=h.length}finally{S.value=!1}},b=()=>{r.page=1,_()},M=()=>{Object.assign(p,{name:"",category:"",status:""}),b()},A=a=>{g.value=a},L=async a=>{try{const e=v();(await y.products.updateStatus(e,{id:a.id,status:a.status})).code===0?n.success(a.status?"商品已上架":"商品已下架"):(a.status=a.status?0:1,n.error("状态更新失败"))}catch(e){console.error("更新状态失败:",e),a.status=a.status?0:1,n.error("状态更新失败")}},O=a=>{r.size=a,r.page=1,_()},F=a=>{r.page=a,_()},I=()=>{V.push("/products/add")},R=a=>{V.push(`/products/edit/${a.id}`)},q=a=>{V.push(`/products/view/${a.id}`)},G=async a=>{try{await D.confirm(`确定要删除商品"${a.name}"吗？`,"删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=v();(await y.products.delete(e,[a.id])).code===0?(n.success("删除成功"),_()):n.error("删除失败")}catch(e){e!=="cancel"&&(console.error("删除失败:",e),n.error("删除失败"))}},H=async()=>{const a=g.value.map(e=>e.id);try{const e=v();(await y.products.batchUpdateStatus(e,{ids:a,status:1})).code===0?(n.success("批量上架成功"),_()):n.error("批量上架失败")}catch(e){console.error("批量上架失败:",e),n.error("批量上架失败")}},J=async()=>{const a=g.value.map(e=>e.id);try{const e=v();(await y.products.batchUpdateStatus(e,{ids:a,status:0})).code===0?(n.success("批量下架成功"),_()):n.error("批量下架失败")}catch(e){console.error("批量下架失败:",e),n.error("批量下架失败")}},K=async()=>{try{await D.confirm(`确定要删除选中的 ${g.value.length} 个商品吗？`,"批量删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const a=g.value.map(i=>i.id),e=v();(await y.products.batchDelete(e,a)).code===0?(n.success("批量删除成功"),_()):n.error("批量删除失败")}catch(a){a!=="cancel"&&(console.error("批量删除失败:",a),n.error("批量删除失败"))}};return ne(()=>{_()}),(a,e)=>{const c=l("el-icon"),i=l("el-button"),Q=l("el-input"),w=l("el-col"),m=l("el-option"),B=l("el-select"),W=l("el-row"),u=l("el-table-column"),X=l("el-image"),Y=l("el-tag"),Z=l("el-switch"),ee=l("el-table"),te=l("el-alert"),ae=l("el-pagination"),se=l("el-card"),oe=re("loading");return T(),$("div",ge,[t(se,null,{header:s(()=>[f("div",fe,[e[6]||(e[6]=f("h2",null,"商品管理",-1)),t(i,{type:"primary",onClick:I},{default:s(()=>[t(c,null,{default:s(()=>[t(x(pe))]),_:1}),e[5]||(e[5]=d(" 添加商品 "))]),_:1,__:[5]})])]),default:s(()=>[f("div",he,[t(W,{gutter:20},{default:s(()=>[t(w,{span:6},{default:s(()=>[t(Q,{modelValue:p.name,"onUpdate:modelValue":e[0]||(e[0]=o=>p.name=o),placeholder:"搜索商品名称",clearable:"",onChange:b},{prefix:s(()=>[t(c,null,{default:s(()=>[t(x(N))]),_:1})]),_:1},8,["modelValue"])]),_:1}),t(w,{span:4},{default:s(()=>[t(B,{modelValue:p.category,"onUpdate:modelValue":e[1]||(e[1]=o=>p.category=o),placeholder:"商品分类",clearable:"",onChange:b},{default:s(()=>[t(m,{label:"全部分类",value:""}),t(m,{label:"蔬菜类",value:"vegetables"}),t(m,{label:"水果类",value:"fruits"}),t(m,{label:"粮食类",value:"grains"}),t(m,{label:"肉类",value:"meat"})]),_:1},8,["modelValue"])]),_:1}),t(w,{span:4},{default:s(()=>[t(B,{modelValue:p.status,"onUpdate:modelValue":e[2]||(e[2]=o=>p.status=o),placeholder:"商品状态",clearable:"",onChange:b},{default:s(()=>[t(m,{label:"全部状态",value:""}),t(m,{label:"上架中",value:"1"}),t(m,{label:"已下架",value:"0"})]),_:1},8,["modelValue"])]),_:1}),t(w,{span:6},{default:s(()=>[t(i,{type:"primary",onClick:b},{default:s(()=>[t(c,null,{default:s(()=>[t(x(N))]),_:1}),e[7]||(e[7]=d(" 搜索 "))]),_:1,__:[7]}),t(i,{onClick:M},{default:s(()=>e[8]||(e[8]=[d("重置")])),_:1,__:[8]})]),_:1})]),_:1})]),ce((T(),ie(ee,{data:k.value,style:{width:"100%"},onSelectionChange:A},{default:s(()=>[t(u,{type:"selection",width:"55"}),t(u,{label:"商品图片",width:"100"},{default:s(({row:o})=>[t(X,{src:o.image||"/placeholder-product.jpg","preview-src-list":[o.image],style:{width:"60px",height:"60px"},fit:"cover"},null,8,["src","preview-src-list"])]),_:1}),t(u,{prop:"name",label:"商品名称","min-width":"150"}),t(u,{prop:"category",label:"分类",width:"100"},{default:s(({row:o})=>[t(Y,null,{default:s(()=>[d(z(E(o.category)),1)]),_:2},1024)]),_:1}),t(u,{prop:"price",label:"价格",width:"100"},{default:s(({row:o})=>[f("span",ve,"¥"+z(o.price),1)]),_:1}),t(u,{prop:"stock",label:"库存",width:"80"},{default:s(({row:o})=>[f("span",{class:de({"low-stock":o.stock<10})},z(o.stock),3)]),_:1}),t(u,{prop:"sales",label:"销量",width:"80"}),t(u,{prop:"status",label:"状态",width:"100"},{default:s(({row:o})=>[t(Z,{modelValue:o.status,"onUpdate:modelValue":C=>o.status=C,"active-value":1,"inactive-value":0,onChange:C=>L(o)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),t(u,{prop:"createTime",label:"创建时间",width:"150"},{default:s(({row:o})=>[d(z(x(_e)(o.createTime)),1)]),_:1}),t(u,{label:"操作",width:"200",fixed:"right"},{default:s(({row:o})=>[t(i,{type:"primary",size:"small",onClick:C=>R(o)},{default:s(()=>e[9]||(e[9]=[d(" 编辑 ")])),_:2,__:[9]},1032,["onClick"]),t(i,{type:"warning",size:"small",onClick:C=>q(o)},{default:s(()=>e[10]||(e[10]=[d(" 查看 ")])),_:2,__:[10]},1032,["onClick"]),t(i,{type:"danger",size:"small",onClick:C=>G(o)},{default:s(()=>e[11]||(e[11]=[d(" 删除 ")])),_:2,__:[11]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[oe,S.value]]),g.value.length>0?(T(),$("div",ye,[t(te,{title:`已选择 ${g.value.length} 个商品`,type:"info","show-icon":"",closable:!1},null,8,["title"]),f("div",be,[t(i,{type:"success",onClick:H},{default:s(()=>e[12]||(e[12]=[d("批量上架")])),_:1,__:[12]}),t(i,{type:"warning",onClick:J},{default:s(()=>e[13]||(e[13]=[d("批量下架")])),_:1,__:[13]}),t(i,{type:"danger",onClick:K},{default:s(()=>e[14]||(e[14]=[d("批量删除")])),_:1,__:[14]})])])):ue("",!0),f("div",Ce,[t(ae,{"current-page":r.page,"onUpdate:currentPage":e[3]||(e[3]=o=>r.page=o),"page-size":r.size,"onUpdate:pageSize":e[4]||(e[4]=o=>r.size=o),"page-sizes":[10,20,50,100],total:r.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:O,onCurrentChange:F},null,8,["current-page","page-size","total"])])]),_:1})])}}},Pe=me(ke,[["__scopeId","data-v-acda3b9d"]]);export{Pe as default};
