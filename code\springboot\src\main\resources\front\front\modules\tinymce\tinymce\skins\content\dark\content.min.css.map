{"version": 3, "sources": ["content/dark/content.css"], "names": [], "mappings": ";;;;;;AAMA,KACE,iBAAkB,QAClB,MAAO,QACP,YAAa,aAAa,CAAE,kBAAkB,CAAE,UAAU,CAAE,MAAM,CAAE,MAAM,CAAE,MAAM,CAAE,SAAS,CAAE,WAAW,CAAE,gBAAgB,CAAE,WAC9H,YAAa,IACb,OAAQ,KAEV,EACE,MAAO,QAET,MACE,gBAAiB,SAGnB,SADA,SAEE,OAAQ,IAAI,MAAM,QAClB,QAAS,MAEX,OACE,QAAS,MACT,OAAQ,KAAK,KAEf,kBACE,MAAO,QACP,QAAS,MACT,WAAY,OACZ,WAAY,OAEd,GACE,aAAc,QACd,aAAc,MACd,aAAc,IAAI,EAAE,EAAE,EAExB,KACE,iBAAkB,QAClB,cAAe,IACf,QAAS,MAAO,MAGlB,sBACA,sBACE,MAAO,KAET,4CACE,YAAa,IAAI,MAAM,QACvB,YAAa,OACb,aAAc,KAEhB,sCACE,aAAc,IAAI,MAAM,QACxB,aAAc,OACd,cAAe", "file": "content.min.css", "sourcesContent": ["/**\n * Copyright (c) Tiny Technologies, Inc. All rights reserved.\n * Licensed under the LGPL or a commercial license.\n * For LGPL see License.txt in the project root for license information.\n * For commercial licenses see https://www.tiny.cloud/\n */\nbody {\n  background-color: #2f3742;\n  color: #dfe0e4;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;\n  line-height: 1.4;\n  margin: 1rem;\n}\na {\n  color: #4099ff;\n}\ntable {\n  border-collapse: collapse;\n}\ntable th,\ntable td {\n  border: 1px solid #6d737b;\n  padding: 0.4rem;\n}\nfigure {\n  display: table;\n  margin: 1rem auto;\n}\nfigure figcaption {\n  color: #8a8f97;\n  display: block;\n  margin-top: 0.25rem;\n  text-align: center;\n}\nhr {\n  border-color: #6d737b;\n  border-style: solid;\n  border-width: 1px 0 0 0;\n}\ncode {\n  background-color: #6d737b;\n  border-radius: 3px;\n  padding: 0.1rem 0.2rem;\n}\n/* Make text in selected cells in tables dark and readable */\ntd[data-mce-selected],\nth[data-mce-selected] {\n  color: #333;\n}\n.mce-content-body:not([dir=rtl]) blockquote {\n  border-left: 2px solid #6d737b;\n  margin-left: 1.5rem;\n  padding-left: 1rem;\n}\n.mce-content-body[dir=rtl] blockquote {\n  border-right: 2px solid #6d737b;\n  margin-right: 1.5rem;\n  padding-right: 1rem;\n}\n"]}