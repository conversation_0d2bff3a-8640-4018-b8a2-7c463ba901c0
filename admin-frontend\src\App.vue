<template>
  <div id="app" class="">
    <router-view></router-view>
  </div>
</template>

<script>
export default {
  name: "app",
};
</script>

<style lang="scss">
@import "utils/theme.scss";/*引入全局颜色*/
@import "assets/css/style.scss";/*引入自定义样式*/
*{
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}
html,body{
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
}
#app{
  height: 100%;
  width: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  position: relative;
}
body {
  padding: 0;
  margin: 0;
}

/* 确保el-container可以滚动，这对购物车页面很重要 */
.el-container {
  overflow-y: auto;
  overflow-x: hidden;
}
</style>
