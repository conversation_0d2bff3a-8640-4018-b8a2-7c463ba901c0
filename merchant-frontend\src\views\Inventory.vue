<template>
  <div class="inventory-page">
    <el-card>
      <template #header>
        <div class="page-header">
          <h2>库存管理</h2>
          <div class="header-actions">
            <el-button type="warning" @click="showLowStockAlert">
              <el-icon><Warning /></el-icon>
              库存预警
            </el-button>
            <el-button @click="exportInventory">
              <el-icon><Download /></el-icon>
              导出库存
            </el-button>
          </div>
        </div>
      </template>

      <!-- 库存统计 -->
      <div class="stats-section">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-value">{{ inventoryStats.totalProducts }}</div>
                <div class="stat-label">商品总数</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-value warning">{{ inventoryStats.lowStock }}</div>
                <div class="stat-label">库存不足</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-value danger">{{ inventoryStats.outOfStock }}</div>
                <div class="stat-label">缺货商品</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-value success">{{ inventoryStats.totalValue }}</div>
                <div class="stat-label">库存总值(万元)</div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 搜索和筛选 -->
      <div class="search-section">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-input
              v-model="searchForm.name"
              placeholder="搜索商品名称"
              clearable
              @change="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="4">
            <el-select
              v-model="searchForm.category"
              placeholder="商品分类"
              clearable
              @change="handleSearch"
            >
              <el-option label="全部分类" value="" />
              <el-option label="蔬菜类" value="vegetables" />
              <el-option label="水果类" value="fruits" />
              <el-option label="肉类" value="meat" />
              <el-option label="粮食类" value="grains" />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select
              v-model="searchForm.stockStatus"
              placeholder="库存状态"
              clearable
              @change="handleSearch"
            >
              <el-option label="全部状态" value="" />
              <el-option label="库存充足" value="sufficient" />
              <el-option label="库存不足" value="low" />
              <el-option label="缺货" value="out" />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 库存列表 -->
      <el-table
        v-loading="loading"
        :data="inventoryList"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="商品信息" min-width="200">
          <template #default="{ row }">
            <div class="product-info">
              <el-image
                :src="row.image"
                style="width: 50px; height: 50px; margin-right: 12px"
                fit="cover"
              />
              <div>
                <div class="product-name">{{ row.name }}</div>
                <div class="product-category">{{ row.category }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="sku" label="SKU" width="120" />
        <el-table-column label="当前库存" width="120">
          <template #default="{ row }">
            <span :class="getStockClass(row.stock, row.minStock)">
              {{ row.stock }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="minStock" label="最低库存" width="100" />
        <el-table-column prop="maxStock" label="最高库存" width="100" />
        <el-table-column label="库存状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStockStatusType(row.stock, row.minStock)">
              {{ getStockStatusText(row.stock, row.minStock) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="price" label="单价" width="100">
          <template #default="{ row }">
            <span>¥{{ row.price }}</span>
          </template>
        </el-table-column>
        <el-table-column label="库存价值" width="120">
          <template #default="{ row }">
            <span class="stock-value">¥{{ (row.stock * row.price).toFixed(2) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="lastUpdateTime" label="更新时间" width="150">
          <template #default="{ row }">
            {{ formatDate(row.lastUpdateTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="updateStock(row)">
              调整库存
            </el-button>
            <el-button type="info" size="small" @click="viewStockLog(row)">
              库存日志
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 调整库存对话框 -->
    <el-dialog
      v-model="stockDialog.visible"
      title="调整库存"
      width="500px"
      @close="resetStockDialog"
    >
      <el-form
        ref="stockFormRef"
        :model="stockDialog.form"
        :rules="stockRules"
        label-width="100px"
      >
        <el-form-item label="商品名称">
          <span>{{ stockDialog.product?.name }}</span>
        </el-form-item>
        <el-form-item label="当前库存">
          <span>{{ stockDialog.product?.stock }}</span>
        </el-form-item>
        <el-form-item label="调整类型" prop="type">
          <el-radio-group v-model="stockDialog.form.type">
            <el-radio label="in">入库</el-radio>
            <el-radio label="out">出库</el-radio>
            <el-radio label="set">设置</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="调整数量" prop="quantity">
          <el-input-number
            v-model="stockDialog.form.quantity"
            :min="1"
            :max="9999"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="调整原因" prop="reason">
          <el-input
            v-model="stockDialog.form.reason"
            type="textarea"
            :rows="3"
            placeholder="请输入调整原因"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="stockDialog.visible = false">取消</el-button>
        <el-button type="primary" @click="confirmStockUpdate">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Warning, Download, Search } from '@element-plus/icons-vue'
import { createApiClient, merchantApi } from '../../../shared/api.js'
import { formatDate } from '../../../shared/utils.js'

const api = createApiClient()

// 响应式数据
const loading = ref(false)
const inventoryList = ref([])
const selectedItems = ref([])

// 库存统计
const inventoryStats = reactive({
  totalProducts: 0,
  lowStock: 0,
  outOfStock: 0,
  totalValue: 0
})

// 搜索表单
const searchForm = reactive({
  name: '',
  category: '',
  stockStatus: ''
})

// 分页数据
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 库存调整对话框
const stockDialog = reactive({
  visible: false,
  product: null,
  form: {
    type: 'in',
    quantity: 1,
    reason: ''
  }
})

// 表单验证规则
const stockRules = {
  type: [{ required: true, message: '请选择调整类型', trigger: 'change' }],
  quantity: [{ required: true, message: '请输入调整数量', trigger: 'blur' }],
  reason: [{ required: true, message: '请输入调整原因', trigger: 'blur' }]
}

// 模拟库存数据
const mockInventoryList = [
  {
    id: 1,
    name: '新鲜有机西红柿',
    category: '蔬菜类',
    sku: 'VEG001',
    image: 'https://via.placeholder.com/50x50?text=西红柿',
    stock: 150,
    minStock: 50,
    maxStock: 500,
    price: 12.80,
    lastUpdateTime: '2025-07-07 10:30:00'
  },
  {
    id: 2,
    name: '优质苹果',
    category: '水果类',
    sku: 'FRU001',
    image: 'https://via.placeholder.com/50x50?text=苹果',
    stock: 25,
    minStock: 30,
    maxStock: 200,
    price: 8.50,
    lastUpdateTime: '2025-07-07 09:15:00'
  },
  {
    id: 3,
    name: '东北大米',
    category: '粮食类',
    sku: 'GRA001',
    image: 'https://via.placeholder.com/50x50?text=大米',
    stock: 0,
    minStock: 20,
    maxStock: 100,
    price: 5.00,
    lastUpdateTime: '2025-07-06 16:45:00'
  },
  {
    id: 4,
    name: '新鲜猪肉',
    category: '肉类',
    sku: 'MEA001',
    image: 'https://via.placeholder.com/50x50?text=猪肉',
    stock: 80,
    minStock: 20,
    maxStock: 150,
    price: 35.00,
    lastUpdateTime: '2025-07-07 14:20:00'
  }
]

// 获取库存状态样式类
const getStockClass = (stock, minStock) => {
  if (stock === 0) return 'stock-out'
  if (stock <= minStock) return 'stock-low'
  return 'stock-normal'
}

// 获取库存状态文本
const getStockStatusText = (stock, minStock) => {
  if (stock === 0) return '缺货'
  if (stock <= minStock) return '库存不足'
  return '库存充足'
}

// 获取库存状态类型
const getStockStatusType = (stock, minStock) => {
  if (stock === 0) return 'danger'
  if (stock <= minStock) return 'warning'
  return 'success'
}

// 获取库存列表
const getInventoryList = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      ...searchForm
    }

    const response = await api.get(merchantApi.inventory.list, { params })

    if (response.code === 200) {
      inventoryList.value = response.data.records || mockInventoryList
      pagination.total = response.data.total || mockInventoryList.length
    } else {
      // 使用模拟数据
      inventoryList.value = mockInventoryList
      pagination.total = mockInventoryList.length
    }

    // 更新统计数据
    updateInventoryStats()
  } catch (error) {
    console.error('获取库存列表失败:', error)
    // 使用模拟数据
    inventoryList.value = mockInventoryList
    pagination.total = mockInventoryList.length
    updateInventoryStats()
  } finally {
    loading.value = false
  }
}

// 更新库存统计
const updateInventoryStats = () => {
  const stats = {
    totalProducts: inventoryList.value.length,
    lowStock: 0,
    outOfStock: 0,
    totalValue: 0
  }

  inventoryList.value.forEach(item => {
    if (item.stock === 0) stats.outOfStock++
    else if (item.stock <= item.minStock) stats.lowStock++

    stats.totalValue += item.stock * item.price
  })

  stats.totalValue = (stats.totalValue / 10000).toFixed(1) // 转换为万元

  Object.assign(inventoryStats, stats)
}

// 搜索处理
const handleSearch = () => {
  pagination.page = 1
  getInventoryList()
}

// 重置搜索
const resetSearch = () => {
  Object.assign(searchForm, {
    name: '',
    category: '',
    stockStatus: ''
  })
  handleSearch()
}

// 选择变化
const handleSelectionChange = (selection) => {
  selectedItems.value = selection
}

// 分页处理
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.page = 1
  getInventoryList()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  getInventoryList()
}

// 调整库存
const updateStock = (row) => {
  stockDialog.product = row
  stockDialog.visible = true
}

// 重置库存对话框
const resetStockDialog = () => {
  stockDialog.product = null
  Object.assign(stockDialog.form, {
    type: 'in',
    quantity: 1,
    reason: ''
  })
}

// 确认库存调整
const confirmStockUpdate = async () => {
  try {
    const response = await api.put(merchantApi.inventory.update, {
      id: stockDialog.product.id,
      ...stockDialog.form
    })

    if (response.code === 200) {
      ElMessage.success('库存调整成功')
      stockDialog.visible = false
      getInventoryList()
    } else {
      ElMessage.error('库存调整失败')
    }
  } catch (error) {
    console.error('库存调整失败:', error)
    ElMessage.error('库存调整失败')
  }
}

// 查看库存日志
const viewStockLog = (row) => {
  ElMessage.info('库存日志功能开发中...')
}

// 显示库存预警
const showLowStockAlert = () => {
  const lowStockItems = inventoryList.value.filter(item =>
    item.stock <= item.minStock || item.stock === 0
  )

  if (lowStockItems.length === 0) {
    ElMessage.success('当前没有库存预警商品')
    return
  }

  const alertContent = lowStockItems.map(item =>
    `${item.name}：当前库存 ${item.stock}，最低库存 ${item.minStock}`
  ).join('\n')

  ElMessageBox.alert(alertContent, '库存预警', {
    confirmButtonText: '知道了',
    type: 'warning'
  })
}

// 导出库存
const exportInventory = () => {
  ElMessage.info('导出功能开发中...')
}

// 组件挂载时获取数据
onMounted(() => {
  getInventoryList()
})
</script>

<style scoped>
.inventory-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.stats-section {
  margin: 20px 0;
}

.stat-card {
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  padding: 10px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
}

.stat-value.warning {
  color: #e6a23c;
}

.stat-value.danger {
  color: #f56c6c;
}

.stat-value.success {
  color: #67c23a;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-top: 5px;
}

.search-section {
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 4px;
  margin-bottom: 20px;
}

.product-info {
  display: flex;
  align-items: center;
}

.product-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.product-category {
  font-size: 12px;
  color: #999;
}

.stock-normal {
  color: #67c23a;
  font-weight: bold;
}

.stock-low {
  color: #e6a23c;
  font-weight: bold;
}

.stock-out {
  color: #f56c6c;
  font-weight: bold;
}

.stock-value {
  color: #409eff;
  font-weight: bold;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

:deep(.el-table .el-table__cell) {
  padding: 8px 0;
}

:deep(.el-image) {
  border-radius: 4px;
}

:deep(.el-dialog__body) {
  padding: 20px;
}
</style>
