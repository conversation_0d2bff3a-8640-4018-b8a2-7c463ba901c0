{"version": 3, "file": "use-lifecycle.mjs", "sources": ["../../../../../../../packages/components/slider/src/composables/use-lifecycle.ts"], "sourcesContent": ["import { nextTick, onMounted, ref } from 'vue'\nimport { useEventListener } from '@vueuse/core'\nimport { isArray, isNumber } from '@element-plus/utils'\n\nimport type { SliderInitData, SliderProps } from '../slider'\n\nexport const useLifecycle = (\n  props: SliderProps,\n  initData: SliderInitData,\n  resetSize: () => void\n) => {\n  const sliderWrapper = ref<HTMLElement>()\n\n  onMounted(async () => {\n    if (props.range) {\n      if (isArray(props.modelValue)) {\n        initData.firstValue = Math.max(props.min, props.modelValue[0])\n        initData.secondValue = Math.min(props.max, props.modelValue[1])\n      } else {\n        initData.firstValue = props.min\n        initData.secondValue = props.max\n      }\n      initData.oldValue = [initData.firstValue, initData.secondValue]\n    } else {\n      if (!isNumber(props.modelValue) || Number.isNaN(props.modelValue)) {\n        initData.firstValue = props.min\n      } else {\n        initData.firstValue = Math.min(\n          props.max,\n          Math.max(props.min, props.modelValue)\n        )\n      }\n      initData.oldValue = initData.firstValue\n    }\n\n    useEventListener(window, 'resize', resetSize)\n\n    await nextTick()\n    resetSize()\n  })\n\n  return {\n    sliderWrapper,\n  }\n}\n"], "names": [], "mappings": ";;;;;AAGY,MAAC,YAAY,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,SAAS,KAAK;AAC5D,EAAE,MAAM,aAAa,GAAG,GAAG,EAAE,CAAC;AAC9B,EAAE,SAAS,CAAC,YAAY;AACxB,IAAI,IAAI,KAAK,CAAC,KAAK,EAAE;AACrB,MAAM,IAAI,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE;AACrC,QAAQ,QAAQ,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;AACvE,QAAQ,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;AACxE,OAAO,MAAM;AACb,QAAQ,QAAQ,CAAC,UAAU,GAAG,KAAK,CAAC,GAAG,CAAC;AACxC,QAAQ,QAAQ,CAAC,WAAW,GAAG,KAAK,CAAC,GAAG,CAAC;AACzC,OAAO;AACP,MAAM,QAAQ,CAAC,QAAQ,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAC;AACtE,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE;AACzE,QAAQ,QAAQ,CAAC,UAAU,GAAG,KAAK,CAAC,GAAG,CAAC;AACxC,OAAO,MAAM;AACb,QAAQ,QAAQ,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC;AACzF,OAAO;AACP,MAAM,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC,UAAU,CAAC;AAC9C,KAAK;AACL,IAAI,gBAAgB,CAAC,MAAM,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC;AAClD,IAAI,MAAM,QAAQ,EAAE,CAAC;AACrB,IAAI,SAAS,EAAE,CAAC;AAChB,GAAG,CAAC,CAAC;AACL,EAAE,OAAO;AACT,IAAI,aAAa;AACjB,GAAG,CAAC;AACJ;;;;"}