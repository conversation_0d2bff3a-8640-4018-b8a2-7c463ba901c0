/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.2.0 (2020-02-13)
 */
!function(t){"use strict";var c=function(e){function t(){return n}var n=e;return{get:t,set:function(e){n=e},clone:function(){return c(t())}}},n=tinymce.util.Tools.resolve("tinymce.PluginManager"),a=function(e){return!(!/(^|[ ,])tinymcespellchecker([, ]|$)/.test(e.settings.plugins)||!n.get("tinymcespellchecker"))&&("undefined"!=typeof t.window.console&&t.window.console.log&&t.window.console.log("Spell Checker Pro is incompatible with Spell Checker plugin! Remove 'spellchecker' from the 'plugins' option."),!0)},l=tinymce.util.Tools.resolve("tinymce.util.Tools"),s=tinymce.util.Tools.resolve("tinymce.util.URI"),d=tinymce.util.Tools.resolve("tinymce.util.XHR"),f=function(e){return e.fire("SpellcheckStart")},o=function(e){return e.fire("SpellcheckEnd")},g=function(e){return e.getParam("spellchecker_languages","English=en,Danish=da,Dutch=nl,Finnish=fi,French=fr_FR,German=de,Italian=it,Polish=pl,Portuguese=pt_BR,Spanish=es,Swedish=sv")},u=function(e){var t=e.getParam("language","en");return e.getParam("spellchecker_language",t)},h=function(e){return e.getParam("spellchecker_rpc_url")},p=function(e){return e.getParam("spellchecker_callback")},m=function(e){var t=new RegExp('[^\\s!"#$%&()*+,-./:;<=>?@[\\]^_{|}`\xa7\xa9\xab\xae\xb1\xb6\xb7\xb8\xbb\xbc\xbd\xbe\xbf\xd7\xf7\xa4\u201d\u201c\u201e\xa0\u2002\u2003\u2009]+',"g");return e.getParam("spellchecker_wordchar_pattern",t)};function T(e){return e&&1===e.nodeType&&"false"===e.contentEditable}function r(c,r){var n,o,g,h,p,i=[],v=r.dom;function a(e,t){if(!e[0])throw new Error("findAndReplaceDOMText cannot handle zero-length matches");return{start:e.index,end:e.index+e[0].length,text:e[0],data:t}}function u(e){for(var t=e.parentNode;0<e.childNodes.length;)t.insertBefore(e.childNodes[0],e);t.removeChild(e)}function s(e){var t=c.getElementsByTagName("*"),n=[];e="number"==typeof e?""+e:null;for(var r=0;r<t.length;r++){var o=t[r],i=o.getAttribute("data-mce-index");null!==i&&i.length&&-1!==o.className.indexOf("mce-spellchecker-word")&&(i!==e&&null!==e||n.push(o))}return n}function l(e){for(var t=i.length;t--;)if(i[t]===e)return t;return-1}function e(e){for(var t=0,n=i.length;t<n&&!1!==e(i[t],t);t++);return this}function t(e){var t,n=s(e?l(e):null);for(t=n.length;t--;)u(n[t]);return this}function d(e){var t=s(l(e)),n=r.dom.createRng();return n.setStartBefore(t[0]),n.setEndAfter(t[t.length-1]),n}return g=r.schema.getBlockElements(),h=r.schema.getWhiteSpaceElements(),p=r.schema.getShortEndedElements(),{text:o=function f(e){var t;if(3===e.nodeType)return e.data;if(h[e.nodeName]&&!g[e.nodeName])return"";if(T(e))return"\n";if(t="",(g[e.nodeName]||p[e.nodeName])&&(t+="\n"),e=e.firstChild)for(;t+=f(e),e=e.nextSibling;);return t}(c),matches:i,each:e,filter:function m(n){var r=[];return e(function(e,t){n(e,t)&&r.push(e)}),i=r,this},reset:function x(){return i.splice(0,i.length),t(),this},matchFromElement:function k(e){return i[e.getAttribute("data-mce-index")]},elementFromMatch:function N(e){return s(l(e))[0]},find:function y(e,t){if(o&&e.global)for(;n=e.exec(o);)i.push(a(n,t));return this},add:function S(e,t,n){return i.push({start:e,end:e+t,text:o.substr(e,t),data:n}),this},wrap:function b(e){return i.length&&function f(e,t,n){var r,o,i,c,a,u=[],s=0,l=e,d=0;(t=t.slice(0)).sort(function(e,t){return e.start-t.start}),a=t.shift();e:for(;;){if((g[l.nodeName]||p[l.nodeName]||T(l))&&s++,3===l.nodeType&&(!o&&l.length+s>=a.end?(o=l,c=a.end-s):r&&u.push(l),!r&&l.length+s>a.start&&(r=l,i=a.start-s),s+=l.length),r&&o){if(l=n({startNode:r,startNodeIndex:i,endNode:o,endNodeIndex:c,innerNodes:u,match:a.text,matchIndex:d}),s-=o.length-c,o=r=null,u=[],d++,!(a=t.shift()))break}else if(h[l.nodeName]&&!g[l.nodeName]||!l.firstChild){if(l.nextSibling){l=l.nextSibling;continue}}else if(!T(l)){l=l.firstChild;continue}for(;;){if(l.nextSibling){l=l.nextSibling;break}if(l.parentNode===e)break e;l=l.parentNode}}}(c,i,function t(o){function m(e,t){var n=i[t];n.stencil||(n.stencil=o(n));var r=n.stencil.cloneNode(!1);return r.setAttribute("data-mce-index",t),e&&r.appendChild(v.doc.createTextNode(e)),r}return function(e){var t,n,r,o=e.startNode,i=e.endNode,c=e.matchIndex,a=v.doc;if(o===i){var u=o;r=u.parentNode,0<e.startNodeIndex&&(t=a.createTextNode(u.data.substring(0,e.startNodeIndex)),r.insertBefore(t,u));var s=m(e.match,c);return r.insertBefore(s,u),e.endNodeIndex<u.length&&(n=a.createTextNode(u.data.substring(e.endNodeIndex)),r.insertBefore(n,u)),u.parentNode.removeChild(u),s}t=a.createTextNode(o.data.substring(0,e.startNodeIndex)),n=a.createTextNode(i.data.substring(e.endNodeIndex));for(var l=m(o.data.substring(e.startNodeIndex),c),d=0,f=e.innerNodes.length;d<f;++d){var g=e.innerNodes[d],h=m(g.data,c);g.parentNode.replaceChild(h,g)}var p=m(i.data.substring(0,e.endNodeIndex),c);return(r=o.parentNode).insertBefore(t,o),r.insertBefore(l,o),r.removeChild(o),(r=i.parentNode).insertBefore(p,i),r.insertBefore(n,i),r.removeChild(i),p}}(e)),this},unwrap:t,replace:function w(e,t){var n=d(e);return n.deleteContents(),0<t.length&&n.insertNode(r.dom.doc.createTextNode(t)),n},rangeFromMatch:d,indexOf:l}}function e(){}function i(e){return function(){return e}}function v(){return y}var x,k=i(!1),N=i(!0),y=(x={fold:function(e,t){return e()},is:k,isSome:k,isNone:N,getOr:w,getOrThunk:b,getOrDie:function(e){throw new Error(e||"error: getOrDie called on none.")},getOrNull:i(null),getOrUndefined:i(undefined),or:w,orThunk:b,map:v,each:e,bind:v,exists:k,forall:N,filter:v,equals:S,equals_:S,toArray:function(){return[]},toString:i("none()")},Object.freeze&&Object.freeze(x),x);function S(e){return e.isNone()}function b(e){return e()}function w(e){return e}function I(e,t){if(!t.get()){var n=r(e.getBody(),e);t.set(n)}return t.get()}function B(e,t,n,r,o,i,c){var a=p(e);(a||function(c,a,u){return function(e,t,r,o){var n={method:e,lang:u.get()},i="";n["addToDictionary"===e?"word":"text"]=t,l.each(n,function(e,t){i&&(i+="&"),i+=t+"="+encodeURIComponent(e)}),d.send({url:new s(a).toAbsolute(h(c)),type:"post",content_type:"application/x-www-form-urlencoded",data:i,success:function(e){var t=JSON.parse(e);if(t)t.error?o(t.error):r(t);else{var n=c.translate("Server response wasn't proper JSON.");o(n)}},error:function(){var e=c.translate("The spelling service was not found: (")+h(c)+c.translate(")");o(e)}})}}(e,t,n)).call(e.plugins.spellchecker,r,o,i,c)}function A(e,t,n){e.dom.select("span.mce-spellchecker-word").length||C(e,t,n)}function E(e){var t=e.getAttribute("data-mce-index");return"number"==typeof t?""+t:t}var P=Object.hasOwnProperty,C=function(e,t,n){var r=e.selection.getBookmark();if(I(e,n).reset(),e.selection.moveToBookmark(r),n.set(null),t.get())return t.set(!1),o(e),!0},O=function(t,e,n,r,o){var i=!!o.dictionary,c=o.words;if(t.setProgressState(!1),function(e){for(var t in e)if(P.call(e,t))return!1;return!0}(c)){var a=t.translate("No misspellings found.");return t.notificationManager.open({text:a,type:"info"}),void e.set(!1)}r.set({suggestions:c,hasDictionarySupport:i});var u=t.selection.getBookmark();I(t,n).find(m(t)).filter(function(e){return!!c[e.text]}).wrap(function(e){return t.dom.create("span",{"class":"mce-spellchecker-word","aria-invalid":"spelling","data-mce-bogus":1,"data-mce-word":e.text})}),t.selection.moveToBookmark(u),e.set(!0),f(t)},_={spellcheck:function(t,e,n,r,o,i){if(!C(t,n,r)){t.setProgressState(!0),B(t,e,i,"spellcheck",I(t,r).text,function(e){O(t,n,r,o,e)},function(e){t.notificationManager.open({text:e,type:"error"}),t.setProgressState(!1),C(t,n,r)}),t.focus()}},checkIfFinished:A,addToDictionary:function(t,e,n,r,o,i,c){t.setProgressState(!0),B(t,e,o,"addToDictionary",i,function(){t.setProgressState(!1),t.dom.remove(c,!0),A(t,n,r)},function(e){t.notificationManager.open({text:e,type:"error"}),t.setProgressState(!1)})},ignoreWord:function(t,e,n,r,o,i){t.selection.collapse(),i?l.each(t.dom.select("span.mce-spellchecker-word"),function(e){e.getAttribute("data-mce-word")===r&&t.dom.remove(e,!0)}):t.dom.remove(o,!0),A(t,e,n)},findSpansByIndex:function(e,t){var n,r=[];if((n=l.toArray(e.getBody().getElementsByTagName("span"))).length)for(var o=0;o<n.length;o++){var i=E(n[o]);null!==i&&i.length&&i===t.toString()&&r.push(n[o])}return r},getElmIndex:E,markErrors:O},D=function(t,n,r,o,e,i){return{getTextMatcher:function(){return o.get()},getWordCharPattern:function(){return m(t)},markErrors:function(e){_.markErrors(t,n,o,r,e)},getLanguage:function(){return e.get()}}},R=function(e,t,n,r,o,i){e.addCommand("mceSpellCheck",function(){_.spellcheck(e,t,n,r,o,i)})},M=function(){return(M=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},F="SpellcheckStart SpellcheckEnd",j=function(n,e,r,t,o,i){function c(){_.spellcheck(n,e,r,t,i,o)}var a=function(e,t){var n=[];return l.each(t,function(e){n.push({selectable:!0,text:e.name,data:e.value})}),n}(0,function(e){return l.map(g(e).split(","),function(e){return{name:(e=e.split("="))[0],value:e[1]}})}(n)),u={tooltip:"Spellcheck",onAction:c,icon:"spell-check",onSetup:function(e){function t(){e.setActive(r.get())}return n.on(F,t),function(){n.off(F,t)}}},s=M(M({},u),{type:"splitbutton",select:function(e){return e===o.get()},fetch:function(e){e(l.map(a,function(e){return{type:"choiceitem",value:e.data,text:e.text}}))},onItemAction:function(e,t){o.set(t)}});1<a.length?n.ui.registry.addSplitButton("spellchecker",s):n.ui.registry.addToggleButton("spellchecker",u),n.ui.registry.addToggleMenuItem("spellchecker",{text:"Spellcheck",onSetup:function(e){e.setActive(r.get());function t(){e.setActive(r.get())}return n.on(F,t),function(){n.off(F,t)}},onAction:c})},W=function(o,i,c,a,u,s){o.ui.registry.addContextMenu("spellchecker",{update:function(e){var t=e;if("mce-spellchecker-word"!==t.className)return[];var n=_.findSpansByIndex(o,_.getElmIndex(t));if(0<n.length){var r=o.dom.createRng();return r.setStartBefore(n[0]),r.setEndAfter(n[n.length-1]),o.selection.setRng(r),function(t,e,n,r,o,i,c,a){var u=[],s=n.get().suggestions[c];return l.each(s,function(e){u.push({text:e,onAction:function(){t.insertContent(t.dom.encode(e)),t.dom.remove(a),_.checkIfFinished(t,r,o)}})}),n.get().hasDictionarySupport&&(u.push({type:"separator"}),u.push({text:"Add to dictionary",onAction:function(){_.addToDictionary(t,e,r,o,i,c,a)}})),u.push.apply(u,[{type:"separator"},{text:"Ignore",onAction:function(){_.ignoreWord(t,r,o,c,a)}},{text:"Ignore all",onAction:function(){_.ignoreWord(t,r,o,c,a,!0)}}]),u}(o,i,c,a,u,s,t.getAttribute("data-mce-word"),n)}}})};!function z(){n.add("spellchecker",function(e,t){if(!1===a(e)){var n=c(!1),r=c(u(e)),o=c(null),i=c(null);return j(e,t,n,o,r,i),W(e,t,i,n,o,r),R(e,t,n,o,i,r),D(e,n,i,o,r,t)}})}()}(window);