import { createRouter, createWebHistory } from 'vue-router'

// 简单的token和用户管理
const tokenManager = {
  getToken() {
    return localStorage.getItem('merchant_token')
  },
  setToken(token) {
    localStorage.setItem('merchant_token', token)
  },
  removeToken() {
    localStorage.removeItem('merchant_token')
  }
}

const userManager = {
  getUser() {
    const userStr = localStorage.getItem('merchant_user')
    return userStr ? JSON.parse(userStr) : null
  },
  setUser(user) {
    localStorage.setItem('merchant_user', JSON.stringify(user))
  },
  removeUser() {
    localStorage.removeItem('merchant_user')
  }
}

// 路由配置
const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/Login.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('../views/Register.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/',
    name: 'Layout',
    component: () => import('../views/Layout.vue'),
    meta: { requiresAuth: true },
    redirect: '/dashboard',
    children: [
      {
        path: '/dashboard',
        name: 'Dashboard',
        component: () => import('../views/Dashboard.vue'),
        meta: { title: '数据概览' }
      },
      {
        path: '/products',
        name: 'Products',
        component: () => import('../views/Products.vue'),
        meta: { title: '商品管理' }
      },
      {
        path: '/products/add',
        name: 'ProductAdd',
        component: () => import('../views/ProductForm.vue'),
        meta: { title: '添加商品' }
      },
      {
        path: '/products/edit/:id',
        name: 'ProductEdit',
        component: () => import('../views/ProductForm.vue'),
        meta: { title: '编辑商品' }
      },
      {
        path: '/orders',
        name: 'Orders',
        component: () => import('../views/Orders.vue'),
        meta: { title: '订单管理' }
      },
      {
        path: '/orders/detail/:id',
        name: 'OrderDetail',
        component: () => import('../views/OrderDetail.vue'),
        meta: { title: '订单详情' }
      },
      {
        path: '/inventory',
        name: 'Inventory',
        component: () => import('../views/Inventory.vue'),
        meta: { title: '库存管理' }
      },
      {
        path: '/analytics',
        name: 'Analytics',
        component: () => import('../views/Analytics.vue'),
        meta: { title: '数据分析' }
      },
      {
        path: '/profile',
        name: 'Profile',
        component: () => import('../views/Profile.vue'),
        meta: { title: '店铺设置' }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('../views/NotFound.vue')
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const token = tokenManager.getToken()
  const user = userManager.getUser()
  
  // 检查是否需要认证
  if (to.meta.requiresAuth !== false) {
    if (!token || !user) {
      next('/login')
      return
    }
    
    // 检查用户角色是否为商家
    if (user.role !== 'merchant') {
      console.error('权限不足：需要商家角色')
      next('/login')
      return
    }
  }
  
  // 如果已登录且访问登录页，重定向到首页
  if (to.path === '/login' && token && user?.role === 'merchant') {
    next('/')
    return
  }
  
  next()
})

export default router
