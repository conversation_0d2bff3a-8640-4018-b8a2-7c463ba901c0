<template>
  <div class="cart-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>购物车</h1>
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item>购物车</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 购物车内容 -->
    <div class="cart-content">
      <div v-if="cartStore.cartItems.length === 0" class="empty-cart">
        <el-empty description="购物车是空的">
          <el-button type="primary" @click="router.push('/products')">
            去购物
          </el-button>
        </el-empty>
      </div>

      <div v-else class="cart-container">
        <!-- 购物车表格 -->
        <div class="cart-table-container">
          <el-table
            :data="cartStore.cartItems"
            style="width: 100%"
            @selection-change="handleSelectionChange"
          >
            <!-- 选择列 -->
            <el-table-column type="selection" width="55" />
            
            <!-- 商品信息 -->
            <el-table-column label="商品信息" min-width="300">
              <template #default="{ row }">
                <div class="product-info">
                  <div class="product-image">
                    <img :src="getImageUrl(row.nongchanpinPhoto)" :alt="row.nongchanpinName">
                  </div>
                  <div class="product-details">
                    <h3 class="product-name">{{ row.nongchanpinName }}</h3>
                    <p class="product-desc">{{ row.nongchanpinContent }}</p>
                    <div class="product-meta">
                      <span class="product-location">{{ row.nongchanpinAddress }}</span>
                      <span class="product-category">{{ row.nongchanpinTypes }}</span>
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>
            
            <!-- 单价 -->
            <el-table-column label="单价" width="120" align="center">
              <template #default="{ row }">
                <div class="price-info">
                  <span class="current-price">¥{{ row.nongchanpinNewMoney }}</span>
                  <span v-if="row.nongchanpinOldMoney" class="old-price">
                    ¥{{ row.nongchanpinOldMoney }}
                  </span>
                </div>
              </template>
            </el-table-column>
            
            <!-- 数量 -->
            <el-table-column label="数量" width="150" align="center">
              <template #default="{ row }">
                <el-input-number
                  v-model="row.buynumber"
                  :min="1"
                  :max="row.nongchanpinKucunNumber"
                  size="small"
                  @change="updateQuantity(row.id, row.buynumber)"
                />
              </template>
            </el-table-column>
            
            <!-- 小计 -->
            <el-table-column label="小计" width="120" align="center">
              <template #default="{ row }">
                <span class="subtotal">¥{{ (row.nongchanpinNewMoney * row.buynumber).toFixed(2) }}</span>
              </template>
            </el-table-column>
            
            <!-- 操作 -->
            <el-table-column label="操作" width="100" align="center">
              <template #default="{ row }">
                <el-button
                  type="danger"
                  size="small"
                  icon="Delete"
                  @click="removeItem(row.id)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 购物车操作栏 -->
        <div class="cart-actions">
          <div class="left-actions">
            <el-checkbox
              v-model="selectAll"
              :indeterminate="isIndeterminate"
              @change="handleSelectAll"
            >
              全选
            </el-checkbox>
            <el-button
              type="danger"
              size="small"
              :disabled="selectedItems.length === 0"
              @click="removeSelectedItems"
            >
              删除选中商品
            </el-button>
          </div>
          
          <div class="right-actions">
            <div class="total-info">
              <span class="selected-count">
                已选择 {{ selectedItems.length }} 件商品
              </span>
              <span class="total-price">
                合计：<span class="price">¥{{ selectedTotalPrice.toFixed(2) }}</span>
              </span>
            </div>
            <el-button
              type="primary"
              size="large"
              :disabled="selectedItems.length === 0"
              @click="checkout"
            >
              结算 ({{ selectedItems.length }})
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 推荐商品 -->
    <div v-if="recommendedProducts.length > 0" class="recommended-section">
      <h3>为您推荐</h3>
      <div class="recommend-grid">
        <div
          v-for="product in recommendedProducts"
          :key="product.id"
          class="recommend-item"
          @click="goToProduct(product.id)"
        >
          <img :src="getImageUrl(product.nongchanpinPhoto)" :alt="product.nongchanpinName">
          <div class="recommend-info">
            <h4>{{ product.nongchanpinName }}</h4>
            <p class="recommend-price">¥{{ product.nongchanpinNewMoney }}</p>
            <el-button
              type="primary"
              size="small"
              @click.stop="addToCart(product.id)"
            >
              加入购物车
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { createApiClient, userApi } from '../../../shared/api.js'
import { getImageUrl } from '../../../shared/utils.js'
import { useCartStore } from '../stores/cart.js'

const router = useRouter()
const cartStore = useCartStore()

// 响应式数据
const selectedItems = ref([])
const recommendedProducts = ref([])

// 计算属性
const selectAll = computed({
  get() {
    return cartStore.cartItems.length > 0 && selectedItems.value.length === cartStore.cartItems.length
  },
  set(value) {
    if (value) {
      selectedItems.value = [...cartStore.cartItems]
    } else {
      selectedItems.value = []
    }
  }
})

const isIndeterminate = computed(() => {
  return selectedItems.value.length > 0 && selectedItems.value.length < cartStore.cartItems.length
})

const selectedTotalPrice = computed(() => {
  return selectedItems.value.reduce((total, item) => {
    return total + (item.nongchanpinNewMoney * item.buynumber)
  }, 0)
})

// 处理选择变化
const handleSelectionChange = (selection) => {
  selectedItems.value = selection
}

// 处理全选
const handleSelectAll = (value) => {
  selectAll.value = value
}

// 更新商品数量
const updateQuantity = async (cartId, quantity) => {
  await cartStore.updateCartItem(cartId, quantity)
}

// 删除单个商品
const removeItem = async (cartId) => {
  try {
    await ElMessageBox.confirm('确定要删除这件商品吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await cartStore.removeFromCart(cartId)
    
    // 从选中列表中移除
    selectedItems.value = selectedItems.value.filter(item => item.id !== cartId)
  } catch {
    // 用户取消
  }
}

// 删除选中商品
const removeSelectedItems = async () => {
  if (selectedItems.value.length === 0) {
    ElMessage.warning('请选择要删除的商品')
    return
  }
  
  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${selectedItems.value.length} 件商品吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const selectedIds = selectedItems.value.map(item => item.id)
    await cartStore.removeSelectedItems()
    
    // 清空选中列表
    selectedItems.value = []
  } catch {
    // 用户取消
  }
}

// 结算
const checkout = () => {
  if (selectedItems.value.length === 0) {
    ElMessage.warning('请选择要结算的商品')
    return
  }
  
  // 跳转到订单确认页面
  router.push({
    path: '/checkout',
    query: {
      items: selectedItems.value.map(item => item.id).join(',')
    }
  })
}

// 跳转到商品详情
const goToProduct = (productId) => {
  router.push(`/product/${productId}`)
}

// 添加推荐商品到购物车
const addToCart = async (productId) => {
  const success = await cartStore.addToCart(productId, 1)
  if (success) {
    ElMessage.success('已添加到购物车')
  }
}

// 加载推荐商品
const loadRecommendedProducts = async () => {
  try {
    const apiClient = createApiClient()
    const response = await userApi.products.list(apiClient, {
      page: 1,
      limit: 4,
      sort: 'clicknum',
      order: 'desc'
    })
    
    if (response.code === 0) {
      recommendedProducts.value = response.data.list || []
    }
  } catch (error) {
    console.error('加载推荐商品失败:', error)
  }
}

// 组件挂载时加载数据
onMounted(() => {
  cartStore.loadCart()
  loadRecommendedProducts()
})
</script>

<style scoped>
.cart-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* 页面头部 */
.page-header {
  margin-bottom: 30px;
}

.page-header h1 {
  font-size: 28px;
  color: #333;
  margin-bottom: 10px;
}

/* 购物车内容 */
.cart-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 30px;
}

.empty-cart {
  padding: 60px 20px;
  text-align: center;
}

.cart-container {
  padding: 20px;
}

/* 商品信息 */
.product-info {
  display: flex;
  gap: 15px;
  align-items: flex-start;
}

.product-image {
  width: 80px;
  height: 80px;
  border-radius: 6px;
  overflow: hidden;
  flex-shrink: 0;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-details {
  flex: 1;
}

.product-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-desc {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-meta {
  display: flex;
  gap: 15px;
  font-size: 12px;
  color: #999;
}

/* 价格信息 */
.price-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.current-price {
  font-size: 16px;
  font-weight: 600;
  color: #e74c3c;
}

.old-price {
  font-size: 12px;
  color: #999;
  text-decoration: line-through;
}

.subtotal {
  font-size: 16px;
  font-weight: 600;
  color: #e74c3c;
}

/* 购物车操作栏 */
.cart-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
  border-top: 1px solid #f0f0f0;
  margin-top: 20px;
}

.left-actions {
  display: flex;
  align-items: center;
  gap: 20px;
}

.right-actions {
  display: flex;
  align-items: center;
  gap: 20px;
}

.total-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 5px;
}

.selected-count {
  font-size: 14px;
  color: #666;
}

.total-price {
  font-size: 16px;
  color: #333;
}

.total-price .price {
  font-size: 20px;
  font-weight: 600;
  color: #e74c3c;
}

/* 推荐商品 */
.recommended-section {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.recommended-section h3 {
  font-size: 20px;
  color: #333;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #409eff;
}

.recommend-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
}

.recommend-item {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.recommend-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

.recommend-item img {
  width: 100%;
  height: 150px;
  object-fit: cover;
}

.recommend-info {
  padding: 15px;
}

.recommend-info h4 {
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.recommend-price {
  font-size: 16px;
  font-weight: 600;
  color: #e74c3c;
  margin-bottom: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .cart-actions {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .left-actions,
  .right-actions {
    justify-content: center;
  }
  
  .total-info {
    align-items: center;
  }
  
  .recommend-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
  }
  
  .product-info {
    flex-direction: column;
    text-align: center;
  }
  
  .product-image {
    align-self: center;
  }
}
</style>
