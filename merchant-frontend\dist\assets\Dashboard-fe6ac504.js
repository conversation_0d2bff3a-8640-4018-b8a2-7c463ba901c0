import{u as X,r as _,x as D,o as j,b as o,c as H,d as J,f as s,w as a,e,t as c,A as g,h as n}from"./index-2c4e09fa.js";import{c as K,m as L}from"./api-7a2a6e2d.js";import{f as T,a as Q}from"./utils-750ce239.js";import{_ as Y}from"./_plugin-vue_export-helper-c27b6911.js";const Z={class:"dashboard"},$={class:"stats-content"},tt={class:"stats-icon sales"},st={class:"stats-info"},at={class:"stats-content"},et={class:"stats-icon orders"},ot={class:"stats-info"},nt={class:"stats-content"},lt={class:"stats-icon products"},dt={class:"stats-info"},rt={class:"stats-content"},ut={class:"stats-icon visitors"},it={class:"stats-info"},ct={class:"card-header"},_t={class:"quick-actions"},pt={class:"card-header"},mt={__name:"Dashboard",setup(ft){const f=X(),w=_("7days"),x=_(12580.5),C=_(45),N=_(128),k=_(1250),A=_([{orderNo:"ORD001",customerName:"张三",amount:158.5,status:"pending",createTime:new Date},{orderNo:"ORD002",customerName:"李四",amount:299,status:"paid",createTime:new Date(Date.now()-36e5)},{orderNo:"ORD003",customerName:"王五",amount:89.9,status:"shipped",createTime:new Date(Date.now()-72e5)}]);D(()=>({tooltip:{trigger:"axis"},xAxis:{type:"category",data:["周一","周二","周三","周四","周五","周六","周日"]},yAxis:{type:"value"},series:[{data:[1200,1500,1800,1400,2200,2800,2500],type:"line",smooth:!0,itemStyle:{color:"#409EFF"}}]})),D(()=>({tooltip:{trigger:"item"},series:[{type:"pie",radius:"70%",data:[{value:35,name:"蔬菜"},{value:25,name:"水果"},{value:20,name:"粮食"},{value:15,name:"肉类"},{value:5,name:"其他"}],emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]}));const V=l=>({pending:"warning",paid:"success",shipped:"info",completed:"success",cancelled:"danger"})[l]||"info",O=l=>({pending:"待付款",paid:"已付款",shipped:"已发货",completed:"已完成",cancelled:"已取消"})[l]||"未知",S=()=>{f.push("/products/add")},b=()=>{f.push("/orders")},M=()=>{f.push("/inventory")},P=()=>{f.push("/analytics")};j(()=>{B()});const B=async()=>{try{const l=K(),t=await L.dashboard.stats(l);t.code===0?console.log("仪表板数据加载成功:",t.data):console.log("使用模拟数据")}catch(l){console.error("加载数据失败:",l),console.log("使用模拟数据")}};return(l,t)=>{const R=o("TrendCharts"),d=o("el-icon"),r=o("el-card"),u=o("el-col"),h=o("Document"),q=o("Goods"),E=o("View"),v=o("el-row"),y=o("el-radio-button"),F=o("el-radio-group"),G=o("Plus"),p=o("el-button"),I=o("Warning"),W=o("DataAnalysis"),m=o("el-table-column"),z=o("el-tag"),U=o("el-table");return H(),J("div",Z,[s(v,{gutter:20,class:"stats-row"},{default:a(()=>[s(u,{span:6},{default:a(()=>[s(r,{class:"stats-card"},{default:a(()=>[e("div",$,[e("div",tt,[s(d,null,{default:a(()=>[s(R)]),_:1})]),e("div",st,[e("h3",null,c(g(T)(x.value)),1),t[1]||(t[1]=e("p",null,"今日销售额",-1)),t[2]||(t[2]=e("span",{class:"stats-change positive"},"+12.5%",-1))])])]),_:1})]),_:1}),s(u,{span:6},{default:a(()=>[s(r,{class:"stats-card"},{default:a(()=>[e("div",at,[e("div",et,[s(d,null,{default:a(()=>[s(h)]),_:1})]),e("div",ot,[e("h3",null,c(C.value),1),t[3]||(t[3]=e("p",null,"今日订单数",-1)),t[4]||(t[4]=e("span",{class:"stats-change positive"},"+8.2%",-1))])])]),_:1})]),_:1}),s(u,{span:6},{default:a(()=>[s(r,{class:"stats-card"},{default:a(()=>[e("div",nt,[e("div",lt,[s(d,null,{default:a(()=>[s(q)]),_:1})]),e("div",dt,[e("h3",null,c(N.value),1),t[5]||(t[5]=e("p",null,"商品总数",-1)),t[6]||(t[6]=e("span",{class:"stats-change neutral"},"0%",-1))])])]),_:1})]),_:1}),s(u,{span:6},{default:a(()=>[s(r,{class:"stats-card"},{default:a(()=>[e("div",rt,[e("div",ut,[s(d,null,{default:a(()=>[s(E)]),_:1})]),e("div",it,[e("h3",null,c(k.value),1),t[7]||(t[7]=e("p",null,"今日访客数",-1)),t[8]||(t[8]=e("span",{class:"stats-change positive"},"+15.3%",-1))])])]),_:1})]),_:1})]),_:1}),s(v,{gutter:20,class:"charts-row"},{default:a(()=>[s(u,{span:16},{default:a(()=>[s(r,{class:"chart-card"},{header:a(()=>[e("div",ct,[t[12]||(t[12]=e("span",null,"销售趋势",-1)),s(F,{modelValue:w.value,"onUpdate:modelValue":t[0]||(t[0]=i=>w.value=i),size:"small"},{default:a(()=>[s(y,{label:"7days"},{default:a(()=>t[9]||(t[9]=[n("近7天")])),_:1,__:[9]}),s(y,{label:"30days"},{default:a(()=>t[10]||(t[10]=[n("近30天")])),_:1,__:[10]}),s(y,{label:"90days"},{default:a(()=>t[11]||(t[11]=[n("近90天")])),_:1,__:[11]})]),_:1},8,["modelValue"])])]),default:a(()=>[t[13]||(t[13]=e("div",{class:"chart-container"},[e("div",{class:"chart-placeholder"},"销售趋势图表 (开发中)")],-1))]),_:1,__:[13]})]),_:1}),s(u,{span:8},{default:a(()=>[s(r,{class:"chart-card"},{header:a(()=>t[14]||(t[14]=[e("span",null,"商品分类销售占比",-1)])),default:a(()=>[t[15]||(t[15]=e("div",{class:"chart-container"},[e("div",{class:"chart-placeholder"},"分类销售占比图表 (开发中)")],-1))]),_:1,__:[15]})]),_:1})]),_:1}),s(v,{gutter:20,class:"bottom-row"},{default:a(()=>[s(u,{span:8},{default:a(()=>[s(r,{class:"quick-actions-card"},{header:a(()=>t[16]||(t[16]=[e("span",null,"快捷操作",-1)])),default:a(()=>[e("div",_t,[s(p,{type:"primary",onClick:S},{default:a(()=>[s(d,null,{default:a(()=>[s(G)]),_:1}),t[17]||(t[17]=n(" 添加商品 "))]),_:1,__:[17]}),s(p,{type:"success",onClick:b},{default:a(()=>[s(d,null,{default:a(()=>[s(h)]),_:1}),t[18]||(t[18]=n(" 查看订单 "))]),_:1,__:[18]}),s(p,{type:"warning",onClick:M},{default:a(()=>[s(d,null,{default:a(()=>[s(I)]),_:1}),t[19]||(t[19]=n(" 库存预警 "))]),_:1,__:[19]}),s(p,{type:"info",onClick:P},{default:a(()=>[s(d,null,{default:a(()=>[s(W)]),_:1}),t[20]||(t[20]=n(" 数据分析 "))]),_:1,__:[20]})])]),_:1})]),_:1}),s(u,{span:16},{default:a(()=>[s(r,{class:"recent-orders-card"},{header:a(()=>[e("div",pt,[t[22]||(t[22]=e("span",null,"最新订单",-1)),s(p,{type:"text",onClick:b},{default:a(()=>t[21]||(t[21]=[n("查看全部")])),_:1,__:[21]})])]),default:a(()=>[s(U,{data:A.value,style:{width:"100%"}},{default:a(()=>[s(m,{prop:"orderNo",label:"订单号",width:"120"}),s(m,{prop:"customerName",label:"客户",width:"100"}),s(m,{prop:"amount",label:"金额",width:"100"},{default:a(i=>[n(c(g(T)(i.row.amount)),1)]),_:1}),s(m,{prop:"status",label:"状态",width:"100"},{default:a(i=>[s(z,{type:V(i.row.status)},{default:a(()=>[n(c(O(i.row.status)),1)]),_:2},1032,["type"])]),_:1}),s(m,{prop:"createTime",label:"下单时间"},{default:a(i=>[n(c(g(Q)(i.row.createTime)),1)]),_:1})]),_:1},8,["data"])]),_:1})]),_:1})]),_:1})])}}},bt=Y(mt,[["__scopeId","data-v-61a262bc"]]);export{bt as default};
