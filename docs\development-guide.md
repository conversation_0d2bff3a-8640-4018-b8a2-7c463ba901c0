# 农产品销售系统开发指南

## 项目架构

### 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户端 (C端)   │    │   商家端 (B端)   │    │   管理端 (Admin) │
│   Port: 3000    │    │   Port: 3001    │    │   Port: 3002    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   后端服务       │
                    │   Port: 8080    │
                    │   Spring Boot   │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │   MySQL数据库    │
                    │   Port: 3306    │
                    └─────────────────┘
```

### 目录结构
```
├── backend/                 # 后端服务 (Spring Boot)
│   ├── src/main/java/
│   ├── src/main/resources/
│   └── pom.xml
├── user-frontend/           # 用户端 (Vue3 + Vant)
│   ├── src/
│   ├── public/
│   ├── package.json
│   └── vite.config.js
├── merchant-frontend/       # 商家端 (Vue3 + Element Plus)
│   ├── src/
│   ├── public/
│   ├── package.json
│   └── vite.config.js
├── admin-frontend/          # 管理端 (Vue3 + Element Plus)
│   ├── src/
│   ├── public/
│   ├── package.json
│   └── vite.config.js
├── shared/                  # 共享资源
│   ├── api.js              # API接口定义
│   ├── utils.js            # 工具函数
│   └── constants.js        # 常量定义
└── docs/                   # 项目文档
    ├── api.md              # API文档
    ├── deployment.md       # 部署文档
    └── development-guide.md # 开发指南
```

## 技术栈

### 后端
- **框架**: Spring Boot 2.6.13
- **数据库**: MySQL 8.0
- **ORM**: MyBatis Plus
- **认证**: JWT Token
- **构建工具**: Maven

### 前端
- **框架**: Vue 3.3.4
- **构建工具**: Vite 4.4.5
- **状态管理**: Pinia 2.1.6
- **路由**: Vue Router 4.2.4
- **HTTP客户端**: Axios 1.5.0

#### 用户端 (C端)
- **UI组件库**: Vant 4.6.2 (移动端优先)
- **特色**: 响应式设计，移动端友好

#### 商家端 (B端)
- **UI组件库**: Element Plus 2.3.8
- **图表库**: ECharts 5.4.3
- **特色**: 数据可视化，专业管理界面

#### 管理端 (Admin)
- **UI组件库**: Element Plus 2.3.8
- **图表库**: ECharts 5.4.3
- **特色**: 系统管理，数据监控

## 开发规范

### 代码规范
1. **命名规范**
   - 组件名: PascalCase (如: UserProfile.vue)
   - 文件名: kebab-case (如: user-profile.vue)
   - 变量名: camelCase (如: userName)
   - 常量名: UPPER_SNAKE_CASE (如: API_BASE_URL)

2. **目录结构**
   ```
   src/
   ├── components/          # 公共组件
   ├── views/              # 页面组件
   ├── router/             # 路由配置
   ├── stores/             # Pinia状态管理
   ├── api/                # API接口
   ├── utils/              # 工具函数
   ├── assets/             # 静态资源
   └── styles/             # 样式文件
   ```

3. **组件规范**
   - 使用 Composition API
   - 组件要有明确的 props 定义
   - 使用 TypeScript (可选)

### API规范
1. **请求格式**
   ```javascript
   // 统一的请求格式
   {
     method: 'POST',
     url: '/api/resource/action',
     data: { ... },
     headers: {
       'Authorization': 'Bearer token',
       'Content-Type': 'application/json'
     }
   }
   ```

2. **响应格式**
   ```javascript
   // 成功响应
   {
     code: 0,
     msg: "success",
     data: { ... }
   }
   
   // 错误响应
   {
     code: 500,
     msg: "error message",
     data: null
   }
   ```

### 权限控制
1. **角色定义**
   - `user`: 普通用户
   - `merchant`: 商家
   - `admin`: 管理员

2. **权限验证**
   - 前端: 路由守卫 + 组件权限
   - 后端: JWT Token + 角色验证

## 开发流程

### 环境准备
1. **安装依赖**
   ```bash
   # 用户端
   cd user-frontend
   npm install
   
   # 商家端
   cd merchant-frontend
   npm install
   
   # 管理端
   cd admin-frontend
   npm install
   ```

2. **启动服务**
   ```bash
   # 后端服务
   cd backend
   mvn spring-boot:run
   
   # 用户端 (端口: 3000)
   cd user-frontend
   npm run dev
   
   # 商家端 (端口: 3001)
   cd merchant-frontend
   npm run dev
   
   # 管理端 (端口: 3002)
   cd admin-frontend
   npm run dev
   ```

### 开发步骤
1. **后端API开发**
   - 设计API接口
   - 实现业务逻辑
   - 编写单元测试

2. **前端页面开发**
   - 设计页面布局
   - 实现交互逻辑
   - 对接后端API

3. **集成测试**
   - 前后端联调
   - 功能测试
   - 性能优化

## 部署说明

### 开发环境
- 后端: http://localhost:8080
- 用户端: http://localhost:3000
- 商家端: http://localhost:3001
- 管理端: http://localhost:3002

### 生产环境
- 域名规划:
  - 用户端: https://yourdomain.com
  - 商家端: https://yourdomain.com/merchant
  - 管理端: https://yourdomain.com/admin
  - API: https://api.yourdomain.com

## 注意事项

1. **跨域问题**: 开发环境通过Vite代理解决，生产环境需要配置Nginx
2. **Token管理**: 统一使用localStorage存储，注意安全性
3. **错误处理**: 统一的错误处理机制，用户友好的错误提示
4. **性能优化**: 路由懒加载，组件按需引入
5. **SEO优化**: 用户端考虑SSR或预渲染

## 常见问题

### Q: 如何在三个前端项目间共享代码？
A: 使用 `shared/` 目录存放共享的API、工具函数等，通过别名 `@shared` 引入。

### Q: 如何处理不同端的权限控制？
A: 后端统一验证，前端根据用户角色显示不同的界面和功能。

### Q: 如何保证三端的UI一致性？
A: 使用统一的设计规范，共享样式变量和组件。
