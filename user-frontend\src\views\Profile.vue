<template>
  <div class="profile-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>个人中心</h1>
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item>个人中心</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <div class="profile-content">
      <!-- 用户信息卡片 -->
      <div class="user-card">
        <div class="user-avatar">
          <el-avatar :size="80" :src="userInfo.avatar">
            {{ userInfo.realname?.charAt(0) || userInfo.username?.charAt(0) }}
          </el-avatar>
        </div>
        <div class="user-info">
          <h2>{{ userInfo.realname || userInfo.username }}</h2>
          <p class="user-phone">{{ userInfo.phone }}</p>
          <p class="user-address">{{ userInfo.address }}</p>
        </div>
        <div class="user-actions">
          <el-button type="primary" @click="showEditDialog = true">
            编辑资料
          </el-button>
        </div>
      </div>

      <!-- 快捷操作 -->
      <div class="quick-actions">
        <div class="action-item" @click="router.push('/orders')">
          <el-icon size="24" color="#409eff"><ShoppingBag /></el-icon>
          <span>我的订单</span>
        </div>
        <div class="action-item" @click="router.push('/cart')">
          <el-icon size="24" color="#67c23a"><ShoppingCart /></el-icon>
          <span>购物车</span>
        </div>
        <div class="action-item" @click="router.push('/favorites')">
          <el-icon size="24" color="#e6a23c"><Star /></el-icon>
          <span>我的收藏</span>
        </div>
        <div class="action-item" @click="showPasswordDialog = true">
          <el-icon size="24" color="#f56c6c"><Lock /></el-icon>
          <span>修改密码</span>
        </div>
      </div>

      <!-- 订单统计 -->
      <div class="order-stats">
        <h3>订单统计</h3>
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-number">{{ orderStats.pending }}</div>
            <div class="stat-label">待付款</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ orderStats.paid }}</div>
            <div class="stat-label">待发货</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ orderStats.shipped }}</div>
            <div class="stat-label">待收货</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ orderStats.completed }}</div>
            <div class="stat-label">已完成</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑资料对话框 -->
    <el-dialog
      v-model="showEditDialog"
      title="编辑个人资料"
      width="500px"
      @close="resetEditForm"
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="editRules"
        label-width="80px"
      >
        <el-form-item label="真实姓名" prop="realname">
          <el-input v-model="editForm.realname" />
        </el-form-item>
        <el-form-item label="性别" prop="sex">
          <el-select v-model="editForm.sex" style="width: 100%">
            <el-option label="男" value="男" />
            <el-option label="女" value="女" />
          </el-select>
        </el-form-item>
        <el-form-item label="联系电话" prop="phone">
          <el-input v-model="editForm.phone" />
        </el-form-item>
        <el-form-item label="联系地址" prop="address">
          <el-input v-model="editForm.address" type="textarea" :rows="3" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showEditDialog = false">取消</el-button>
        <el-button type="primary" :loading="editLoading" @click="handleEditSubmit">
          保存
        </el-button>
      </template>
    </el-dialog>

    <!-- 修改密码对话框 -->
    <el-dialog
      v-model="showPasswordDialog"
      title="修改密码"
      width="400px"
      @close="resetPasswordForm"
    >
      <el-form
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="passwordRules"
        label-width="80px"
      >
        <el-form-item label="原密码" prop="oldPassword">
          <el-input v-model="passwordForm.oldPassword" type="password" show-password />
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input v-model="passwordForm.newPassword" type="password" show-password />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input v-model="passwordForm.confirmPassword" type="password" show-password />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showPasswordDialog = false">取消</el-button>
        <el-button type="primary" :loading="passwordLoading" @click="handlePasswordSubmit">
          确认修改
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { createApiClient, userApi } from '../../../shared/api.js'
import { userManager, validationRules } from '../../../shared/utils.js'

const router = useRouter()

// 响应式数据
const showEditDialog = ref(false)
const showPasswordDialog = ref(false)
const editLoading = ref(false)
const passwordLoading = ref(false)
const orderStats = reactive({
  pending: 0,
  paid: 0,
  shipped: 0,
  completed: 0
})

// 表单引用
const editFormRef = ref()
const passwordFormRef = ref()

// 用户信息
const userInfo = computed(() => userManager.get() || {})

// 编辑表单
const editForm = reactive({
  realname: '',
  sex: '',
  phone: '',
  address: ''
})

// 密码表单
const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 确认密码验证
const validateConfirmPassword = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请确认新密码'))
  } else if (value !== passwordForm.newPassword) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

// 编辑表单验证规则
const editRules = {
  realname: [
    validationRules.required('请输入真实姓名'),
    { min: 2, max: 10, message: '姓名长度在 2 到 10 个字符', trigger: 'blur' }
  ],
  sex: [
    validationRules.required('请选择性别')
  ],
  phone: [
    validationRules.required('请输入联系电话'),
    validationRules.phone()
  ],
  address: [
    validationRules.required('请输入联系地址'),
    { min: 5, max: 100, message: '地址长度在 5 到 100 个字符', trigger: 'blur' }
  ]
}

// 密码表单验证规则
const passwordRules = {
  oldPassword: [
    validationRules.required('请输入原密码')
  ],
  newPassword: [
    validationRules.required('请输入新密码'),
    validationRules.password()
  ],
  confirmPassword: [
    { validator: validateConfirmPassword, trigger: 'blur' }
  ]
}

// 初始化编辑表单
const initEditForm = () => {
  editForm.realname = userInfo.value.realname || ''
  editForm.sex = userInfo.value.sex || ''
  editForm.phone = userInfo.value.phone || ''
  editForm.address = userInfo.value.address || ''
}

// 重置编辑表单
const resetEditForm = () => {
  if (editFormRef.value) {
    editFormRef.value.resetFields()
  }
  initEditForm()
}

// 重置密码表单
const resetPasswordForm = () => {
  if (passwordFormRef.value) {
    passwordFormRef.value.resetFields()
  }
  Object.assign(passwordForm, {
    oldPassword: '',
    newPassword: '',
    confirmPassword: ''
  })
}

// 处理编辑提交
const handleEditSubmit = async () => {
  if (!editFormRef.value) return
  
  try {
    await editFormRef.value.validate()
    
    editLoading.value = true
    const apiClient = createApiClient()
    const response = await userApi.profile.update(apiClient, editForm)
    
    if (response.code === 0) {
      // 更新本地用户信息
      const updatedUserInfo = { ...userInfo.value, ...editForm }
      userManager.set(updatedUserInfo)
      
      ElMessage.success('个人资料更新成功')
      showEditDialog.value = false
    } else {
      ElMessage.error(response.msg || '更新失败')
    }
  } catch (error) {
    if (error.message) {
      return // 表单验证错误
    }
    console.error('更新个人资料失败:', error)
    ElMessage.error('更新失败，请重试')
  } finally {
    editLoading.value = false
  }
}

// 处理密码修改提交
const handlePasswordSubmit = async () => {
  if (!passwordFormRef.value) return
  
  try {
    await passwordFormRef.value.validate()
    
    passwordLoading.value = true
    const apiClient = createApiClient()
    const response = await userApi.profile.changePassword(apiClient, {
      oldPassword: passwordForm.oldPassword,
      newPassword: passwordForm.newPassword
    })
    
    if (response.code === 0) {
      ElMessage.success('密码修改成功')
      showPasswordDialog.value = false
      resetPasswordForm()
    } else {
      ElMessage.error(response.msg || '密码修改失败')
    }
  } catch (error) {
    if (error.message) {
      return // 表单验证错误
    }
    console.error('修改密码失败:', error)
    ElMessage.error('修改失败，请重试')
  } finally {
    passwordLoading.value = false
  }
}

// 加载订单统计
const loadOrderStats = async () => {
  try {
    const apiClient = createApiClient()
    const response = await userApi.orders.stats(apiClient)
    
    if (response.code === 0) {
      Object.assign(orderStats, response.data)
    }
  } catch (error) {
    console.error('加载订单统计失败:', error)
  }
}

// 组件挂载时初始化
onMounted(() => {
  initEditForm()
  loadOrderStats()
})
</script>

<style scoped>
.profile-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* 页面头部 */
.page-header {
  margin-bottom: 30px;
}

.page-header h1 {
  font-size: 28px;
  color: #333;
  margin-bottom: 10px;
}

.profile-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

/* 用户信息卡片 */
.user-card {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 30px;
}

.user-info {
  flex: 1;
}

.user-info h2 {
  font-size: 24px;
  color: #333;
  margin-bottom: 10px;
}

.user-phone,
.user-address {
  color: #666;
  margin-bottom: 5px;
}

/* 快捷操作 */
.quick-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.action-item {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.action-item:hover {
  transform: translateY(-3px);
}

.action-item span {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

/* 订单统计 */
.order-stats {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.order-stats h3 {
  font-size: 20px;
  color: #333;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #409eff;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
}

.stat-item {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-number {
  font-size: 32px;
  font-weight: 600;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  color: #666;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .user-card {
    flex-direction: column;
    text-align: center;
  }
  
  .quick-actions {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
