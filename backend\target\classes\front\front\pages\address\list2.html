<!DOCTYPE html>
<html>
<head lang="en">
    <meta charset="utf-8">
    <title>收货地址</title>
    <meta name="keywords" content=""/>
    <meta name="description" content=""/>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <link rel="stylesheet" href="../../layui/css/layui.css">
    <link rel="stylesheet" href="../../xznstatic/css/common.css"/>
    <link rel="stylesheet" href="../../xznstatic/css/style.css"/>
    <script type="text/javascript" src="../../xznstatic/js/jquery-1.11.3.min.js"></script>
    <script type="text/javascript" src="../../xznstatic/js/jquery.SuperSlide.2.1.1.js"></script>
    <link rel="stylesheet" href="../../xznstatic/css/bootstrap.min.css" />
    <link rel="stylesheet" href="../../css/theme.css"/>
</head>
<style>
    .center-container {
        width: 1200px;
        margin: 0 auto;
        margin-top: 20px;
        display: flex;
        margin-bottom: 20px;
    }
.prolist .left_nav {
        background: #fff;
        width: 220px;
    }
    .prolist .left_nav .dlx1 {
        padding: 15px 0;
        padding-left: 20px;
        background: var(--publicMainColor);
        color: #fff;
    }
    .prolist .left_nav .dlx1 dt {
        font-size: 22px;
        font-weight: bold;
    }
    .prolist .left_nav .dlx1 dd {
        padding-top: 5px;
        font-size: 14px;
        font-weight: 200;
    }
    .prolist .left_nav .dlx2 {
        padding: 15px 0;
        padding-left: 20px;
        background: var(--publicMainColor);
        color: #fff;
    }
    .prolist .left_nav .dlx2 dt {
        font-size: 14px;
    }
    .prolist .left_nav .dlx2 dd {
        padding-top: 5px;
        font-size: 22px;
        color:  var(--publicSubColor);
        font-family: impact;
    }
    .prolist .left_nav ul {
        padding: 20px;
    }
    .prolist .left_nav ul li {
        display: block;
        margin-bottom: 15px;
    }
    .prolist .left_nav ul li:last-child {
        margin-bottom: 0;
    }
    .prolist .left_nav ul li {
        background-color: var(--publicSubColor);
        display: block;
        border: 1px solid #ddd;
        padding: 15px 10px;
        color: #666;
        font-size: 12px;
    }
    .prolist .left_nav ul li i {
        color:  var(--publicMainColor);
        margin-right: 10px;
    }
    .prolist .left_nav ul li:hover {
        border: 1px solid var(--publicMainColor);
        background: var(--publicMainColor);
        color: #fff;
    }
    .prolist .left_nav ul li:hover i {
        color: var(--publicSubColor);
    }
    .onclickbiaoqian{
        color: #fff !important;
        background-color: var(--publicMainColor) !important;
    }
    .onclickbiaoqian i{
        color:  var(--publicSubColor) !important;
    }</style>
<body class='bodyClass'>
<div id="app">
    <el-dialog title="弹出内容" :visible.sync="showContentModal" :modal-append-to-body="false">
        <div class="content" style="word-break: break-all;" v-html="showContent">
        </div>
        <div slot="footer" class="dialog-footer">
            <el-button @click="showContentModal = false">关 闭</el-button>
        </div>
    </el-dialog>
    <!-- 新增修改模态框 -->
    <el-dialog :title="addresTitle" :visible.sync="dialogFormVisible" :modal-append-to-body="false">
        <el-form :model="addressFrom" :rules="rules" ref="ruleForm">
            <el-form-item label="收货人" :label-width="formLabelWidth" prop="addressName">
                <el-input v-model="addressFrom.addressName" autocomplete="off"></el-input>
            </el-form-item>
            <el-form-item label="电话" :label-width="formLabelWidth" prop="addressPhone">
                <el-input v-model="addressFrom.addressPhone" autocomplete="off"></el-input>
            </el-form-item>
            <el-form-item label="地址" :label-width="formLabelWidth" prop="addressDizhi">
                <el-input v-model="addressFrom.addressDizhi" autocomplete="off"></el-input>
            </el-form-item>
            <el-form-item label="是否默认地址" :label-width="formLabelWidth" prop="isdefaultTypes">
                <el-switch v-model="addressFrom.isdefaultTypes"></el-switch>
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button @click="dialogFormVisible = false">取 消</el-button>
            <el-button type="primary" @click="saveaddress()">确 定</el-button>
        </div>
    </el-dialog>

<!-- 标题 -->

    <!-- 标题 -->
    <div class="center-container">
        <!-- 个人中心菜单 config.js-->
<div class="prolist">
    <div class="left_nav">
        <dl class="dlx1">
            <dt>收货地址</dt>
            <dd>USER / CENTER</dd>
        </dl>
        <ul>
            <li  v-for="(item,index) in centerMenu" v-bind:key="index" @click="jump(item.url)"
                 :class="item.url=='../address/list.html'?'onclickbiaoqian':''"><i class="layui-icon">&#xe6b1;</i>{{item.name}}</li>
        </ul>
    </div>
</div>        <!-- 个人中心菜单 -->
        <div class="right-container sub_borderColor" :style='{"padding":"20px","boxShadow":"0px rgba(255,0,0,.8)","margin":"0","backgroundColor":"#fff","borderRadius":"0","borderWidth":"1px","borderStyle":"solid","width":"80%"}'>
            <div class="btn-container" :style='{"padding":"0 10px","boxShadow":"0 0 0px rgba(255,0,0,.8)","margin":"10px 0 10px 0","borderColor":"rgba(0,0,0,.3)","backgroundColor":"rgba(255, 255, 255, 1)","borderRadius":"4px","alignItems":"flex-end","borderWidth":"0","borderStyle":"solid","justifyContent":"flex-end","height":"64px","textAlign":"right"}'>
                <button @click="addaddress" class="layui-btn layui-btn-lg btn-theme sub_backgroundColor" :style='{"padding":"0 15px","boxShadow":"0 0 8px rgba(0,0,0,0)","margin":"0 0 0 10px","borderColor":"#409EFF","color":"#fff","borderRadius":"4px","borderWidth":"0","width":"auto","fontSize":"14px","borderStyle":"solid","height":"40px"}'>
                    <i v-if="true" class="layui-icon">&#xe654;</i> 添加新地址
                </button>
            </div>
            <table class="layui-table" lay-skin="nob">
                <thead>
                <tr>
                    <th>主键 </th>
                    <th>收货人 </th>
                    <th>电话 </th>
                    <th>地址 </th>
                    <th>是否默认地址 </th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                <tr v-for="(item,index) in dataList" v-bind:key="index">
                    <th style="width: auto">{{item.id}}</th>
                    <th style="width: auto">{{item.addressName}}</th>
                    <th style="width: auto">{{item.addressPhone}}</th>
                    <th>{{item.addressDizhi}}</th>
                    <th style="width: auto">{{item.isdefaultValue}}</th>
                    <td style="width: auto">
                        <button @click="updateeaddress(item)" type="button"
                                class="layui-btn layui-btn-radius layui-btn-sm layui-btn-warm">
                            修改
                        </button>
                        <button @click="deleteClick(item.id)" type="button"
                                class="layui-btn layui-btn-radius layui-btn-sm btn-theme">
                            <i class="layui-icon">&#xe640;</i> 删除
                        </button>
                    </td>
                </tr>
                </tbody>
            </table>
            <div class="pager" id="pager"></div>
        </div>
    </div></div>

<script src="../../xznstatic/js/bootstrap.min.js" type="text/javascript" charset="utf-8"></script>
<script src="../../xznstatic/js/echarts.min.js" type="text/javascript" charset="utf-8"></script>
<script src="../../layui/layui.js"></script>
<script src="../../js/vue.js"></script>
<!-- 引入element组件库 -->
<script src="../../xznstatic/js/element.min.js"></script>
<!-- 引入element样式 -->
<link rel="stylesheet" href="../../xznstatic/css/element.min.css">
<script src="../../js/config.js"></script>
<script src="../../modules/config.js"></script>
<script src="../../js/utils.js"></script>

<script type="text/javascript">
    Vue.prototype.myFilters = function (msg) {
        if(msg==null || msg==""){
            return "";
        }else if (msg.length>20){
            msg.replace(/\n/g, "<br>");
            return msg.substring(0,30)+"......";
        }else{
            return msg.replace(/\n/g, "<br>");
        }
    };
    var vue = new Vue({
        el: '#app',
        data: {
            userId: localStorage.getItem("userid"),//当前登录人的id
            sessionTable: localStorage.getItem("userTable"),//登录账户所在表名
            role: localStorage.getItem("role"),//权限
            form:{
                yonghuId: '',
                addressName: '',
                addressPhone: '',
                addressDizhi: '',
                isdefaultTypes: '',//数字
                isdefaultValue: '',//数字对应的值
                insertTime: '',
                updateTime: '',
                createTime: '',
            },
            //小菜单
            centerMenu: centerMenu,
            //项目路径
            baseUrl:"",
            //弹出内容模态框
            showContentModal:false,
            showContent:"",
            isdefaultTypesList: [],

            //查询条件
            searchForm: {
                page: 1
                ,limit: 8
                ,sort: "id"//字段
                ,order: "desc"//asc desc
                ,yonghuId: localStorage.getItem('userid')//只能查询自己
                    ,addressName: ""
                ,addressPhone: ""
                ,addressDizhi: ""
                ,isdefaultTypes: ""
            },

            //地址相关
            centerMenu: centerMenu,
            addresTitle: null,
            dialogFormVisible: false,
            formLabelWidth: '120px',
            addressFrom: {
                id: "",
                addressName: "",
                addressPhone: "",
                addressDizhi: "",
                isdefaultTypes: false,
            },
            rules: {
                addressName: [
                    { required: true, message: '收货人 不能为空', trigger: 'blur' },
                ],
                 addressPhone: [
                     {  required: true, message: '电话不能为空', trigger: 'blur' },
                     {  pattern: /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/,
                         message: '电话格式不对',
                         trigger: 'blur'
                     }
                 ],
                addressDizhi: [
                    { required: true, message: '地址 不能为空', trigger: 'blur' },
                ],
                isdefaultTypes: [
                    { required: true, message: '是否默认地址 不能为空', trigger: 'blur' },
                ],
             },

            dataList: [],
        },
        filters: {
            subString: function(val) {
                if (val) {
                    val = val.replace(/<[^<>]+>/g, '').replace(/undefined/g, '');
                    if (val.length > 60) {
                        val = val.substring(0, 60);
                        val+='...';
                    }
                    return val;
                }
                return '';
            }
        },
        computed: {
        },
        methods: {
            isAuth(tablename, button) {
                return isFrontAuth(tablename, button);
            },
            jump(url) {
                jump(url);
            },
            jumpCheck(url,check1,check2) {
                if(check1 == "2" || check1 == 2){//级联表的逻辑删除字段[1:未删除 2:已删除]
                    layui.layer.msg("已经被删除", {
                        time: 2000,
                        icon: 2
                    });
                    return false;
                }
                if(check2 == "2"  || check2 == 2){//是否下架[1:上架 2:下架]
                    layui.layer.msg("已经下架", {
                        time: 2000,
                        icon: 2
                    });
                    return false;
                }
                this.jump(url);
            },
            showContentFunction(content) {
                this.showContentModal=true;
                this.showContent=content.replaceAll(/\n/g, "<br>").replaceAll("src=\"upload/","src=\""+this.baseUrl+"upload/");
            },
            wuyong(id) {
                var mymessage = confirm("确定要    吗？");if(!mymessage){return false;}
                layui.http.requestJson(`address/update`, 'post', {
                    id:id,
//                    addressTypes:1,
                }, function (res) {
                    if(res.code == 0){
                        layui.layer.msg('操作成功', {time: 2000, icon: 6 }, function () {window.location.reload();});
                    }else{
                        layui.layer.msg(res.msg, {time: 5000,icon: 5});
                    }
                });
            },
            deleteData(data){
                var mymessage = confirm("确定要删除这条数据吗？");
                if(!mymessage){
                    return false;
                }
                // 删除信息
                layui.http.requestJson(`address/delete`, 'post', [data.id], (res) => {
                    if(res.code==0){
                        layer.msg('删除成功', {
                            time: 2000,
                            icon: 6
                        });
                        window.location.reload();
                    }else{
                        layer.msg(res.msg, {
                            time: 2000,
                            icon: 2
                        });
                    }
                });
            },
			deleteClick(id) {
				layui.layer.confirm('是否确认删除？', {
					btn: ['删除', '取消'] //按钮
				}, function() {
					layui.http.requestJson(`address/delete`, 'post', [id], function(res) {
						layer.msg('删除成功', {
							time: 2000,
							icon: 6
						}, function(res) {
							window.location.reload();
						});
					})
				});
			},
			updateeaddress(res) {
				this.addressFrom = null
				this.addresTitle = '修改地址'
				this.dialogFormVisible = true
				if(res.isdefaultTypes == 2){
					res.isdefaultTypes = true
				}else{
					res.isdefaultTypes = false
				}
                this.addressFrom = res
			},
			addaddress() {
                this.addressFrom={
					id: "",
					addressName: "",
					addressPhone: "",
					addressDizhi: "",
					isdefaultTypes: false,
                },
                this.addresTitle = '新增地址'
                this.dialogFormVisible = true
			},
			saveaddress(){
                let _this = this
                if(this.addressFrom.isdefaultTypes){
                    this.addressFrom.isdefaultTypes = 2
                }else{
                    this.addressFrom.isdefaultTypes = 1
                }

                this.$refs["ruleForm"].validate(valid => {
                    if (valid) {
                        layui.http.requestJson(`address/${!this.addressFrom.id ? "save" : "update"}`, 'post', this.addressFrom, function (res) {
                            layer.msg('操作成功', {
                                time: 2000,
                                icon: 6
                            }, function () {
                                _this.dialogFormVisible = false
                                window.location.reload();
                            });
                            _this.dialogFormVisible = false
                        });
                    }
                });
            },
        }
    });

    layui.use(['layer', 'element', 'carousel', 'laypage', 'http', 'jquery', 'laydate', 'tinymce'], function() {
        var layer = layui.layer;
        var element = layui.element;
        var carousel = layui.carousel;
        var laypage = layui.laypage;
        var http = layui.http;
        var laydate = layui.laydate;
        var tinymce = layui.tinymce;
        window.jQuery = window.$ = jquery = layui.jquery;
        vue.baseUrl = http.baseurl

        localStorage.setItem("goUtl","./pages/address/list2.html")

        // var id = http.getParam('id');


           //当前表的 是否默认地址 字段 字典表查询方法
           function isdefaultTypesSelect() {
               http.request("dictionary/page?page=1&limit=100&sort=&order=&dicCode=isdefault_types", 'get', {}, function (res) {
                   if(res.code == 0){
                       vue.isdefaultTypesList = res.data.list;
                   }
               });
           }
		// 获取列表数据
		http.request('address/page', 'get', {
			page: 1,
			limit: 5,
			yonghu: localStorage.getItem('userid')
		}, function(res) {
			vue.dataList = res.data.list
			// 分页
			laypage.render({
				elem: 'pager',
				count: res.data.total,
				limit: 5,
				jump: function(obj, first) {
					//首次不执行
					if (!first) {
						http.request('address/list', 'get', {
							page: obj.curr,
							limit: obj.limit,
							yonghu: localStorage.getItem('userid')
						}, function(res) {
							vue.dataList = res.data.list
						})
					}
				}
			});
		})    });

    window.xznSlide = function () {
        jQuery(".banner").slide({mainCell: ".bd ul", autoPlay: true, interTime: 5000});
        jQuery("#ifocus").slide({
            titCell: "#ifocus_btn li",
            mainCell: "#ifocus_piclist ul",
            effect: "leftLoop",
            delayTime: 200,
            autoPlay: true,
            triggerTime: 0
        });
        jQuery("#ifocus").slide({titCell: "#ifocus_btn li", mainCell: "#ifocus_tx ul", delayTime: 0, autoPlay: true});
        jQuery(".product_list").slide({
            mainCell: ".bd ul",
            autoPage: true,
            effect: "leftLoop",
            autoPlay: true,
            vis: 5,
            trigger: "click",
            interTime: 4000
        });
    };
</script>
</body>
</html>
