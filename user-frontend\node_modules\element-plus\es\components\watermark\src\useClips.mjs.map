{"version": 3, "file": "useClips.mjs", "sources": ["../../../../../../packages/components/watermark/src/useClips.ts"], "sourcesContent": ["import { isArray } from '@element-plus/utils'\n\nimport type { WatermarkProps } from './watermark'\n\nexport const FontGap = 3\n\nfunction prepareCanvas(\n  width: number,\n  height: number,\n  ratio = 1\n): [\n  ctx: CanvasRenderingContext2D,\n  canvas: HTMLCanvasElement,\n  realWidth: number,\n  realHeight: number\n] {\n  const canvas = document.createElement('canvas')\n  const ctx = canvas.getContext('2d')!\n  const realWidth = width * ratio\n  const realHeight = height * ratio\n  canvas.setAttribute('width', `${realWidth}px`)\n  canvas.setAttribute('height', `${realHeight}px`)\n  ctx.save()\n\n  return [ctx, canvas, realWidth, realHeight]\n}\n\n/**\n * Get the clips of text content.\n * This is a lazy hook function since SSR no need this\n */\nexport default function useClips() {\n  // Get single clips\n  function getClips(\n    content: NonNullable<WatermarkProps['content']> | HTMLImageElement,\n    rotate: number,\n    ratio: number,\n    width: number,\n    height: number,\n    font: Required<NonNullable<WatermarkProps['font']>>,\n    gapX: number,\n    gapY: number\n  ): [dataURL: string, finalWidth: number, finalHeight: number] {\n    // ================= Text / Image =================\n    const [ctx, canvas, contentWidth, contentHeight] = prepareCanvas(\n      width,\n      height,\n      ratio\n    )\n\n    if (content instanceof HTMLImageElement) {\n      // Image\n      ctx.drawImage(content, 0, 0, contentWidth, contentHeight)\n    } else {\n      // Text\n      const {\n        color,\n        fontSize,\n        fontStyle,\n        fontWeight,\n        fontFamily,\n        textAlign,\n        textBaseline,\n      } = font\n      const mergedFontSize = Number(fontSize) * ratio\n\n      ctx.font = `${fontStyle} normal ${fontWeight} ${mergedFontSize}px/${height}px ${fontFamily}`\n      ctx.fillStyle = color\n      ctx.textAlign = textAlign\n      ctx.textBaseline = textBaseline\n      const contents = isArray(content) ? content : [content]\n      contents?.forEach((item, index) => {\n        ctx.fillText(\n          item ?? '',\n          contentWidth / 2,\n          index * (mergedFontSize + FontGap * ratio)\n        )\n      })\n    }\n\n    // ==================== Rotate ====================\n    const angle = (Math.PI / 180) * Number(rotate)\n    const maxSize = Math.max(width, height)\n    const [rCtx, rCanvas, realMaxSize] = prepareCanvas(maxSize, maxSize, ratio)\n\n    // Copy from `ctx` and rotate\n    rCtx.translate(realMaxSize / 2, realMaxSize / 2)\n    rCtx.rotate(angle)\n    if (contentWidth > 0 && contentHeight > 0) {\n      rCtx.drawImage(canvas, -contentWidth / 2, -contentHeight / 2)\n    }\n\n    // Get boundary of rotated text\n    function getRotatePos(x: number, y: number) {\n      const targetX = x * Math.cos(angle) - y * Math.sin(angle)\n      const targetY = x * Math.sin(angle) + y * Math.cos(angle)\n      return [targetX, targetY]\n    }\n\n    let left = 0\n    let right = 0\n    let top = 0\n    let bottom = 0\n\n    const halfWidth = contentWidth / 2\n    const halfHeight = contentHeight / 2\n    const points = [\n      [0 - halfWidth, 0 - halfHeight],\n      [0 + halfWidth, 0 - halfHeight],\n      [0 + halfWidth, 0 + halfHeight],\n      [0 - halfWidth, 0 + halfHeight],\n    ]\n    points.forEach(([x, y]) => {\n      const [targetX, targetY] = getRotatePos(x, y)\n      left = Math.min(left, targetX)\n      right = Math.max(right, targetX)\n      top = Math.min(top, targetY)\n      bottom = Math.max(bottom, targetY)\n    })\n\n    const cutLeft = left + realMaxSize / 2\n    const cutTop = top + realMaxSize / 2\n    const cutWidth = right - left\n    const cutHeight = bottom - top\n\n    // ================ Fill Alternate ================\n    const realGapX = gapX * ratio\n    const realGapY = gapY * ratio\n    const filledWidth = (cutWidth + realGapX) * 2\n    const filledHeight = cutHeight + realGapY\n\n    const [fCtx, fCanvas] = prepareCanvas(filledWidth, filledHeight)\n\n    function drawImg(targetX = 0, targetY = 0) {\n      fCtx.drawImage(\n        rCanvas,\n        cutLeft,\n        cutTop,\n        cutWidth,\n        cutHeight,\n        targetX,\n        targetY,\n        cutWidth,\n        cutHeight\n      )\n    }\n    drawImg()\n    drawImg(cutWidth + realGapX, -cutHeight / 2 - realGapY / 2)\n    drawImg(cutWidth + realGapX, +cutHeight / 2 + realGapY / 2)\n\n    return [fCanvas.toDataURL(), filledWidth / ratio, filledHeight / ratio]\n  }\n\n  return getClips\n}\n"], "names": [], "mappings": ";;AACY,MAAC,OAAO,GAAG,EAAE;AACzB,SAAS,aAAa,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,GAAG,CAAC,EAAE;AACjD,EAAE,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AAClD,EAAE,MAAM,GAAG,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;AACtC,EAAE,MAAM,SAAS,GAAG,KAAK,GAAG,KAAK,CAAC;AAClC,EAAE,MAAM,UAAU,GAAG,MAAM,GAAG,KAAK,CAAC;AACpC,EAAE,MAAM,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;AACjD,EAAE,MAAM,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC,EAAE,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC;AACnD,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC;AACb,EAAE,OAAO,CAAC,GAAG,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;AAC9C,CAAC;AACc,SAAS,QAAQ,GAAG;AACnC,EAAE,SAAS,QAAQ,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AAC7E,IAAI,MAAM,CAAC,GAAG,EAAE,MAAM,EAAE,YAAY,EAAE,aAAa,CAAC,GAAG,aAAa,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;AAC3F,IAAI,IAAI,OAAO,YAAY,gBAAgB,EAAE;AAC7C,MAAM,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,YAAY,EAAE,aAAa,CAAC,CAAC;AAChE,KAAK,MAAM;AACX,MAAM,MAAM;AACZ,QAAQ,KAAK;AACb,QAAQ,QAAQ;AAChB,QAAQ,SAAS;AACjB,QAAQ,UAAU;AAClB,QAAQ,UAAU;AAClB,QAAQ,SAAS;AACjB,QAAQ,YAAY;AACpB,OAAO,GAAG,IAAI,CAAC;AACf,MAAM,MAAM,cAAc,GAAG,MAAM,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAC;AACtD,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,EAAE,SAAS,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,EAAE,cAAc,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC,CAAC;AACnG,MAAM,GAAG,CAAC,SAAS,GAAG,KAAK,CAAC;AAC5B,MAAM,GAAG,CAAC,SAAS,GAAG,SAAS,CAAC;AAChC,MAAM,GAAG,CAAC,YAAY,GAAG,YAAY,CAAC;AACtC,MAAM,MAAM,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,OAAO,GAAG,CAAC,OAAO,CAAC,CAAC;AAC9D,MAAM,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,KAAK;AACpE,QAAQ,GAAG,CAAC,QAAQ,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,GAAG,EAAE,EAAE,YAAY,GAAG,CAAC,EAAE,KAAK,IAAI,cAAc,GAAG,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC;AAC7G,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;AACjD,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AAC5C,IAAI,MAAM,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,CAAC,GAAG,aAAa,CAAC,OAAO,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;AAChF,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,CAAC,EAAE,WAAW,GAAG,CAAC,CAAC,CAAC;AACrD,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACvB,IAAI,IAAI,YAAY,GAAG,CAAC,IAAI,aAAa,GAAG,CAAC,EAAE;AAC/C,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,YAAY,GAAG,CAAC,EAAE,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC;AACpE,KAAK;AACL,IAAI,SAAS,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE;AAChC,MAAM,MAAM,OAAO,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAChE,MAAM,MAAM,OAAO,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAChE,MAAM,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AAChC,KAAK;AACL,IAAI,IAAI,IAAI,GAAG,CAAC,CAAC;AACjB,IAAI,IAAI,KAAK,GAAG,CAAC,CAAC;AAClB,IAAI,IAAI,GAAG,GAAG,CAAC,CAAC;AAChB,IAAI,IAAI,MAAM,GAAG,CAAC,CAAC;AACnB,IAAI,MAAM,SAAS,GAAG,YAAY,GAAG,CAAC,CAAC;AACvC,IAAI,MAAM,UAAU,GAAG,aAAa,GAAG,CAAC,CAAC;AACzC,IAAI,MAAM,MAAM,GAAG;AACnB,MAAM,CAAC,CAAC,GAAG,SAAS,EAAE,CAAC,GAAG,UAAU,CAAC;AACrC,MAAM,CAAC,CAAC,GAAG,SAAS,EAAE,CAAC,GAAG,UAAU,CAAC;AACrC,MAAM,CAAC,CAAC,GAAG,SAAS,EAAE,CAAC,GAAG,UAAU,CAAC;AACrC,MAAM,CAAC,CAAC,GAAG,SAAS,EAAE,CAAC,GAAG,UAAU,CAAC;AACrC,KAAK,CAAC;AACN,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK;AAC/B,MAAM,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,GAAG,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACpD,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AACrC,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AACvC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;AACnC,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;AACzC,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,OAAO,GAAG,IAAI,GAAG,WAAW,GAAG,CAAC,CAAC;AAC3C,IAAI,MAAM,MAAM,GAAG,GAAG,GAAG,WAAW,GAAG,CAAC,CAAC;AACzC,IAAI,MAAM,QAAQ,GAAG,KAAK,GAAG,IAAI,CAAC;AAClC,IAAI,MAAM,SAAS,GAAG,MAAM,GAAG,GAAG,CAAC;AACnC,IAAI,MAAM,QAAQ,GAAG,IAAI,GAAG,KAAK,CAAC;AAClC,IAAI,MAAM,QAAQ,GAAG,IAAI,GAAG,KAAK,CAAC;AAClC,IAAI,MAAM,WAAW,GAAG,CAAC,QAAQ,GAAG,QAAQ,IAAI,CAAC,CAAC;AAClD,IAAI,MAAM,YAAY,GAAG,SAAS,GAAG,QAAQ,CAAC;AAC9C,IAAI,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,GAAG,aAAa,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;AACrE,IAAI,SAAS,OAAO,CAAC,OAAO,GAAG,CAAC,EAAE,OAAO,GAAG,CAAC,EAAE;AAC/C,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC;AAC3G,KAAK;AACL,IAAI,OAAO,EAAE,CAAC;AACd,IAAI,OAAO,CAAC,QAAQ,GAAG,QAAQ,EAAE,CAAC,SAAS,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,CAAC,CAAC;AAChE,IAAI,OAAO,CAAC,QAAQ,GAAG,QAAQ,EAAE,CAAC,SAAS,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,CAAC,CAAC;AAChE,IAAI,OAAO,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,WAAW,GAAG,KAAK,EAAE,YAAY,GAAG,KAAK,CAAC,CAAC;AAC5E,GAAG;AACH,EAAE,OAAO,QAAQ,CAAC;AAClB;;;;"}