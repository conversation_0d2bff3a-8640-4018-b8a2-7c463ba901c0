{"version": 3, "file": "tabs.mjs", "sources": ["../../../../../../packages/components/tabs/src/tabs.tsx"], "sourcesContent": ["import {\n  computed,\n  createVNode,\n  defineComponent,\n  getCurrentInstance,\n  nextTick,\n  provide,\n  ref,\n  renderSlot,\n  watch,\n} from 'vue'\nimport { omit } from 'lodash-unified'\nimport {\n  buildProps,\n  definePropType,\n  isNumber,\n  isString,\n  isUndefined,\n} from '@element-plus/utils'\nimport { EVENT_CODE, UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport ElIcon from '@element-plus/components/icon'\nimport { Plus } from '@element-plus/icons-vue'\nimport { useNamespace, useOrderedChildren } from '@element-plus/hooks'\nimport { tabsRootContextKey } from './constants'\nimport TabNav from './tab-nav'\n\nimport type { ExtractPropTypes, VNode } from 'vue'\nimport type { Awaitable } from '@element-plus/utils'\nimport type { TabNavInstance } from './tab-nav'\nimport type { TabPaneName, TabsPaneContext } from './constants'\n\nexport const tabsProps = buildProps({\n  /**\n   * @description type of Tab\n   */\n  type: {\n    type: String,\n    values: ['card', 'border-card', ''],\n    default: '',\n  },\n  /**\n   * @description whether Tab is closable\n   */\n  closable: Boolean,\n  /**\n   * @description whether Tab is addable\n   */\n  addable: Boolean,\n  /**\n   * @description binding value, name of the selected tab\n   */\n  modelValue: {\n    type: [String, Number],\n  },\n  /**\n   * @description whether Tab is addable and closable\n   */\n  editable: Boolean,\n  /**\n   * @description position of tabs\n   */\n  tabPosition: {\n    type: String,\n    values: ['top', 'right', 'bottom', 'left'],\n    default: 'top',\n  },\n  /**\n   * @description hook function before switching tab. If `false` is returned or a `Promise` is returned and then is rejected, switching will be prevented\n   */\n  beforeLeave: {\n    type: definePropType<\n      (newName: TabPaneName, oldName: TabPaneName) => Awaitable<void | boolean>\n    >(Function),\n    default: () => true,\n  },\n  /**\n   * @description whether width of tab automatically fits its container\n   */\n  stretch: Boolean,\n} as const)\nexport type TabsProps = ExtractPropTypes<typeof tabsProps>\n\nconst isPaneName = (value: unknown): value is string | number =>\n  isString(value) || isNumber(value)\n\nexport const tabsEmits = {\n  [UPDATE_MODEL_EVENT]: (name: TabPaneName) => isPaneName(name),\n  tabClick: (pane: TabsPaneContext, ev: Event) => ev instanceof Event,\n  tabChange: (name: TabPaneName) => isPaneName(name),\n  edit: (paneName: TabPaneName | undefined, action: 'remove' | 'add') =>\n    ['remove', 'add'].includes(action),\n  tabRemove: (name: TabPaneName) => isPaneName(name),\n  tabAdd: () => true,\n}\nexport type TabsEmits = typeof tabsEmits\n\nexport type TabsPanes = Record<number, TabsPaneContext>\n\nconst Tabs = defineComponent({\n  name: 'ElTabs',\n\n  props: tabsProps,\n  emits: tabsEmits,\n\n  setup(props, { emit, slots, expose }) {\n    const ns = useNamespace('tabs')\n\n    const isVertical = computed(() =>\n      ['left', 'right'].includes(props.tabPosition)\n    )\n\n    const {\n      children: panes,\n      addChild: registerPane,\n      removeChild: unregisterPane,\n      ChildrenSorter: PanesSorter,\n    } = useOrderedChildren<TabsPaneContext>(getCurrentInstance()!, 'ElTabPane')\n\n    const nav$ = ref<TabNavInstance>()\n    const currentName = ref<TabPaneName>(props.modelValue ?? '0')\n\n    const setCurrentName = async (value?: TabPaneName, trigger = false) => {\n      // should do nothing.\n      if (currentName.value === value || isUndefined(value)) return\n\n      try {\n        let canLeave\n        if (props.beforeLeave) {\n          const result = props.beforeLeave(value, currentName.value)\n          canLeave = result instanceof Promise ? await result : result\n        } else {\n          canLeave = true\n        }\n\n        if (canLeave !== false) {\n          const isFocusInsidePane = panes.value\n            .find((item) => item.paneName === currentName.value)\n            ?.isFocusInsidePane()\n\n          currentName.value = value\n          if (trigger) {\n            emit(UPDATE_MODEL_EVENT, value)\n            emit('tabChange', value)\n          }\n\n          nav$.value?.removeFocus?.()\n          if (isFocusInsidePane) {\n            nav$.value?.focusActiveTab()\n          }\n        }\n      } catch {}\n    }\n\n    const handleTabClick = (\n      tab: TabsPaneContext,\n      tabName: TabPaneName,\n      event: Event\n    ) => {\n      if (tab.props.disabled) return\n      emit('tabClick', tab, event)\n      setCurrentName(tabName, true)\n    }\n\n    const handleTabRemove = (pane: TabsPaneContext, ev: Event) => {\n      if (pane.props.disabled || isUndefined(pane.props.name)) return\n      ev.stopPropagation()\n      emit('edit', pane.props.name, 'remove')\n      emit('tabRemove', pane.props.name)\n    }\n\n    const handleTabAdd = () => {\n      emit('edit', undefined, 'add')\n      emit('tabAdd')\n    }\n\n    const swapChildren = (\n      vnode: VNode & {\n        el: HTMLDivElement\n        children: VNode<HTMLDivElement>[]\n      }\n    ) => {\n      const actualFirstChild = vnode.el.firstChild!\n      const firstChild = ['bottom', 'right'].includes(props.tabPosition)\n        ? vnode.children[0].el!\n        : vnode.children[1].el!\n\n      if (actualFirstChild !== firstChild) {\n        actualFirstChild.before(firstChild)\n      }\n    }\n\n    watch(\n      () => props.modelValue,\n      (modelValue) => setCurrentName(modelValue)\n    )\n\n    watch(currentName, async () => {\n      await nextTick()\n      nav$.value?.scrollToActiveTab()\n    })\n\n    provide(tabsRootContextKey, {\n      props,\n      currentName,\n      registerPane,\n      unregisterPane,\n      nav$,\n    })\n\n    expose({\n      currentName,\n      get tabNavRef() {\n        return omit(nav$.value, ['scheduleRender'])\n      },\n    })\n\n    return () => {\n      const addSlot = slots['add-icon']\n      const newButton =\n        props.editable || props.addable ? (\n          <div\n            class={[\n              ns.e('new-tab'),\n              isVertical.value && ns.e('new-tab-vertical'),\n            ]}\n            tabindex=\"0\"\n            onClick={handleTabAdd}\n            onKeydown={(ev: KeyboardEvent) => {\n              if ([EVENT_CODE.enter, EVENT_CODE.numpadEnter].includes(ev.code))\n                handleTabAdd()\n            }}\n          >\n            {addSlot ? (\n              renderSlot(slots, 'add-icon')\n            ) : (\n              <ElIcon class={ns.is('icon-plus')}>\n                <Plus />\n              </ElIcon>\n            )}\n          </div>\n        ) : null\n\n      const tabNav = () => (\n        <TabNav\n          ref={nav$}\n          currentName={currentName.value}\n          editable={props.editable}\n          type={props.type}\n          panes={panes.value}\n          stretch={props.stretch}\n          onTabClick={handleTabClick}\n          onTabRemove={handleTabRemove}\n        />\n      )\n\n      const header = (\n        <div\n          class={[\n            ns.e('header'),\n            isVertical.value && ns.e('header-vertical'),\n            ns.is(props.tabPosition),\n          ]}\n        >\n          {createVNode(PanesSorter, null, {\n            default: tabNav,\n            $stable: true,\n          })}\n          {newButton}\n        </div>\n      )\n\n      const panels = (\n        <div class={ns.e('content')}>{renderSlot(slots, 'default')}</div>\n      )\n\n      return (\n        <div\n          class={[\n            ns.b(),\n            ns.m(props.tabPosition),\n            {\n              [ns.m('card')]: props.type === 'card',\n              [ns.m('border-card')]: props.type === 'border-card',\n            },\n          ]}\n          // @ts-ignore\n          onVnodeMounted={swapChildren}\n          onVnodeUpdated={swapChildren}\n        >\n          {panels}\n          {header}\n        </div>\n      )\n    }\n  },\n})\n\nexport type TabsInstance = InstanceType<typeof Tabs> & {\n  currentName: TabPaneName\n  tabNavRef: TabNavInstance | undefined\n}\n\nexport default Tabs\n"], "names": ["tabsProps", "buildProps", "type", "values", "default", "closable", "addable", "modelValue", "editable", "tabPosition", "beforeLeave", "definePropType", "stretch", "Boolean", "ev", "tabChange", "isPaneName", "edit", "paneName", "tabRemove", "tabAdd", "defineComponent", "name", "props", "emits", "expose", "children", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "unregisterPane", "<PERSON><PERSON><PERSON><PERSON>", "getCurrentInstance", "nav$", "currentName", "setCurrentName", "value", "result", "canLeave", "emit", "handleTabClick", "disabled", "pane", "_createVNode", "isUndefined", "handleTabAdd", "swapChildren", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "watch", "nextTick", "scrollToActiveTab", "registerPane", "tabNavRef", "newButton"], "mappings": ";;;;;;;;;;;;;;AA+BaA,MAAAA,SAAS,GAAGC,UAAU,CAAC;AAClC,EAAA,IAAA,EAAA;AACF,IAAA,IAAA,EAAA,MAAA;AACA,IAAA,MAAA,EAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA;AACEC,IAAAA,OAAM,EAAA,EAAA;AACJA,GAAAA;AACAC,EAAAA,QAAM,EAAE,OAAA;AACRC,EAAAA,OAAAA,EAAO,OAAE;EAHL,UAJ4B,EAAA;;AASlC,GAAA;AACF,EAAA,QAAA,EAAA,OAAA;AACA,EAAA,WAAA,EAAA;AACEC,IAAAA,IAAAA,EAAQ,MAZ0B;;AAalC,IAAA,OAAA,EAAA,KAAA;AACF,GAAA;AACA,EAAA,WAAA,EAAA;AACEC,IAAAA,IAAAA,gBAhBkC,CAAA,QAAA,CAAA;;AAiBlC,GAAA;AACF,EAAA,OAAA,EAAA,OAAA;AACA,CAAA,EAAA;AACEC,MAAAA,UAAY,GAAA,CAAA,KAAA,KAAA,QAAA,CAAA,KAAA,CAAA,IAAA,QAAA,CAAA,KAAA,CAAA,CAAA;AACJ,MAAA,SAAA,GAAA;EADI,CApBsB,kBAAA,GAAA,CAAA,IAAA,KAAA,UAAA,CAAA,IAAA,CAAA;;AAuBlC,EAAA,SAAA,EAAA,CAAA,IAAA,KAAA,UAAA,CAAA,IAAA,CAAA;AACF,EAAA,IAAA,EAAA,CAAA,QAAA,EAAA,MAAA,KAAA,CAAA,QAAA,EAAA,KAAA,CAAA,CAAA,QAAA,CAAA,MAAA,CAAA;AACA,EAAA,SAAA,EAAA,CAAA,IAAA,KAAA,UAAA,CAAA,IAAA,CAAA;AACEC,EAAAA,MAAAA,EAAQ,MA1B0B,IAAA;;AA2BlC,MAAA,IAAA,GAAA,eAAA,CAAA;AACF,EAAA,IAAA,EAAA,QAAA;AACA,EAAA,KAAA,EAAA,SAAA;AACEC,EAAAA,KAAAA,EAAAA,SAAa;AACXP,EAAAA,KAAAA,CAAI,KADO,EAAA;IAEXC,IAAM;AACNC,IAAAA,KAAAA;IAjCgC,MAAA;;AAmClC,IAAA,IAAA,EAAA,CAAA;AACF,IAAA,MAAA,EAAA,GAAA,YAAA,CAAA,MAAA,CAAA,CAAA;AACA,IAAA,MAAA,UAAA,GAAA,QAAA,CAAA,MAAA,CAAA,MAAA,EAAA,OAAA,CAAA,CAAA,QAAA,CAAA,KAAA,CAAA,WAAA,CAAA,CAAA,CAAA;AACEM,IAAAA,MAAAA;AACER,MAAAA,QAAMS,EAAAA,KAAAA;AAGNP,MAAAA,QAAe,EAAA,YAAA;MA1CiB,WAAA,EAAA,cAAA;;AA4ClC,KAAA,GAAA,kBAAA,CAAA,kBAAA,EAAA,EAAA,WAAA,CAAA,CAAA;AACF,IAAA,MAAA,IAAA,GAAA,GAAA,EAAA,CAAA;AACA,IAAA,MAAA,WAAA,GAAA,GAAA,CAAA,CAAA,EAAA,GAAA,KAAA,CAAA,UAAA,KAAA,IAAA,GAAA,EAAA,GAAA,GAAA,CAAA,CAAA;AACEQ,IAAAA,MAASC,cAAAA,GAAAA,OAAAA,KAAAA,EAAAA,OAAAA,GAAAA,KAAAA,KAAAA;AA/CyB,MAA7B,IAAA,GAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,CAAA;;AAmDP,QAAgB,OAAA;;AAGhB,QAAO,IAAA;AACL;UACQ,MAAE,MAAsCC,GAAAA,iBAFzB,CAAA,KAAA,EAAA,WAAA,CAAA,KAAA,CAAA,CAAA;AAGvBC,UAAAA,QAAW,GAAuBC,MAAU,YAHrB,OAAA,GAAA,MAAA,MAAA,GAAA,MAAA,CAAA;AAIvBC,SAAOC,MAAD;AAENC,UAAAA,QAAW,GAAuBH,IAAU,CAAA;AAC5CI;AAPuB,QAAlB,IAAA,QAAA,KAAA,KAAA,EAAA;AAaP,UAAU,MAAGC,iBAAgB,GAAA,CAAA,GAAA,GAAA,KAAA,CAAA,KAAA,CAAA,IAAA,CAAA,CAAA,IAAA,KAAA,IAAA,CAAA,QAAA,KAAA,WAAA,CAAA,KAAA,CAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,GAAA,CAAA,iBAAA,EAAA,CAAA;AAC3BC,UAD2B,WAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AAG3BC,UAH2B,IAAA,OAAA,EAAA;AAI3BC,YAJ2B,IAAA,CAAA,kBAAA,EAAA,KAAA,CAAA,CAAA;;;UAMd,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,WAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA;UAAA,IAAA,iBAAA,EAAA;AAAeC,YAAAA,CAAAA,EAAAA,GAAAA,IAAAA,CAAAA,KAAAA,KAAAA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,EAAAA,CAAAA,cAAAA,EAAAA,CAAAA;AAAf,WAAyB;AACpC,SAAA;AAEA,OAAA,CAAA;OAIM;AACJC,KAAAA,CAAAA;AACAC,IAAAA,MAAAA,cAFI,GAAA,CAAA,GAAA,EAAA,OAAA,EAAA,KAAA,KAAA;AAGJC,MAAAA,IAAAA,GAAAA,CAAAA,KAAaC,CAHT,QAAA;AAIJC,QAAAA,OAAAA;AAJI,MAAA,oBAKgB,EAAkBC,KAAAA,CAAAA,CAAAA;MAElCC,cAAU,CAAhB,OAAA,EAAA,IAAA,CAAA,CAAA;KACMC,CAAAA;;MAEAC,IAAAA,IAAAA,CAAAA,KAAAA,CAAAA,QAAwBC,IAAAA,WAA4B,CAAA,IAAA,CAAA,MAAnC,IAAgD,CAAA;AACrE,QAAA,OAAA;MACA,EAAIF,CAAAA,eAAYE,EAAAA,CAAZ;;MAEJ,IAAI,CAAA,WAAA,EAAA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,CAAA;AACF,KAAA,CAAA;;UACIZ,CAAAA,MAAK,EAACb,KAAAA,CAAAA,EAAAA,KAAa,CAAA,CAAA;UACrB,CAAM0B,QAAAA,CAAAA,CAAAA;;AAEP,IAAA,MAAM,YAAA,GAAA,CAAA,KAAA,KAAA;AACLC,MAAAA,MAAAA,gBAAA,GAAA,KAAA,CAAA,EAAA,CAAA,UAAA,CAAA;AACD,MAAA,MAAA,UAAA,GAAA,CAAA,QAAA,EAAA,OAAA,CAAA,CAAA,QAAA,CAAA,KAAA,CAAA,WAAA,CAAA,GAAA,KAAA,CAAA,QAAA,CAAA,CAAA,CAAA,CAAA,EAAA,GAAA,KAAA,CAAA,QAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;;QAED,gBAAiB,CAAA,MAAO,CAAA,UAAA,CAAA,CAAA;AACtB,OAAA;;;AAKA,IAAA,KAAA,CAAA,WAAA,EAAa,YAAA;AACXC,MAAAA,IAAAA,GAAAA,CAAAA;AACAA,MAAAA,MAAAA,QAAK,EAAA,CAAA;AACN,MAAA,CAAA,GAAA,GAAA,IAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,GAAA,CAAA,iBAAA,EAAA,CAAA;;WAEG,CAAA,kBAAJ,EAAA;;AACA,MAAA;kBACE;AACD,MAAA,cAAA;AACF,MAAA,IAAA;MACF,CAzBD;IA0BD,MA9BD,CAAA;;MAgCMC,IAAAA,SAAAA,GAAAA;AAKJ,QAAA,OAAI,IAAA,CAAUC,UAAU,EAAA,CAAA,gBAAA,CAAA,CAAA,CAAA;AACxBF,OAAAA;AACAJ,KAAAA,CAAAA,CAAAA;IACD,OARD,MAAA;;AAUA,MAAA,uBAAyBO,CAAD,QAAsC,IAAA,KAAA,CAAA,OAAA,GAAAC,WAAA,CAAA,KAAA,EAAA;AAC5D,QAAA,OAASnB,EAAL,CAAA,EAAA,CAAWiB,WAAYG,CAAAA,EAAAA,UAAYF,CAAAA,KAAKlB,IAAL,EAAWD,CAAAA,CAAAA,CAAZ,kBAAmB,CAAA,CAAA;AACzDR,QAAE,UAAF,EAAA,GAAA;QACI,SAAA,EAAS2B,YAAWnB;QACpB,cAAA,EAAcmB,KAAKlB;UAJzB,IAAA,CAAA,UAAA,CAAA,KAAA,EAAA,UAAA,CAAA,WAAA,CAAA,CAAA,QAAA,CAAA,EAAA,CAAA,IAAA,CAAA;;SAOMqB;AACJN,OAAAA,EAAAA,CAAI,OAAA,GAAA,UAAoB,MAAxB,EAAA,UAAA,CAAA,GAAAI,WAAA,CAAA,MAAA,EAAA;QACI,WAAA,CAAJ,EAAA,CAAA,WAAA,CAAA;OAFF,EAAA;;OAKMG,CAAAA,CAAAA,CAAAA,GAAAA,IAAAA,CAAAA;AAMJ,MAAA,MAAMC,0BAAmB,CAASC,MAAlC,EAAA;QACMA,KAAAA,EAAAA,IAAAA;;QAIFD,UAAAA,EAAAA,KAAAA,CAAgB,QAAKC;QACvBD,MAAgB,EAAA,KAAA,CAAA,IAAhB;AACD,QAAA,OAAA,EAAA,KAAA,CAAA,KAAA;QAbH,SAAA,EAAA,KAAA,CAAA,OAAA;;AAgBAE,QAAAA,aACa,EADR,eAEH;OAGG,EAAA,IAAA,CAAA,CAAA;AACH,MAAA,MAAMC,SAANP,WAAA,CAAA,KAAA,EAAA;QACI,OAAJ,EAAA,CAAYQ,EAAZ,CAAA,CAAA,CAAA,QAAA,CAAA,EAAA,UAAA,CAAA,KAAA,IAAA,EAAA,CAAA,CAAA,CAAA,iBAAA,CAAA,EAAA,EAAA,CAAA,EAAA,CAAA,KAAA,CAAA,WAAA,CAAA,CAAA;AACD,OAHD,EAAA,CAAA,WAAA,CAAA,WAAA,EAAA,IAAA,EAAA;QAKO;QAAqB,OAAA,EAAA,IAAA;OAAA,CAAA,EAAA,SAAA,CAAA,CAAA,CAAA;MAG1BC,MAH0B,MAAA,GAAAT,WAAA,CAAA,KAAA,EAAA;QAAA,OAAA,EAAA,EAAA,CAAA,CAAA,CAAA,SAAA,CAAA;AAK1BV,OAAAA,EAAAA,CAAAA,UAAAA,CAAAA,KAAAA,EAAAA,SAAAA,CAAAA,CAAAA,CAAAA,CAAAA;AAL0B,MAA5B,OAAAU,WAAA,CAAA,KAAA,EAAA;AAQAjB,QAAAA,OAAO,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,EAAA,EAAA,CAAA,CAAA,CAAA,KAAA,CAAA,WAAA,CAAA,EAAA;UAAA,CAAA,EAAA,CAAA,CAAA,CAAA,MAAA,CAAA,GAAA,KAAA,CAAA,IAAA,KAAA,MAAA;;AAEL,SAAA,CAAI2B;QACF,gBAAgB,cAAQ;AACzB,QAAA,gBAAA,EAAA,YAAA;;AAJI,KAAD,CAAN;AAOA,GAAA;AACE,CAAA,CAAA,CAAA;aACMC,IAAAA;;;;"}