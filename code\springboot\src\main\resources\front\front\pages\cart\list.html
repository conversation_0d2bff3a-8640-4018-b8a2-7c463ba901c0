<!DOCTYPE html>
<html>
<head lang="en">
    <meta charset="utf-8">
    <title>购物车</title>
    <meta name="keywords" content=""/>
    <meta name="description" content=""/>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <link rel="stylesheet" href="../../layui/css/layui.css">
    <link rel="stylesheet" href="../../xznstatic/css/common.css"/>
    <link rel="stylesheet" href="../../xznstatic/css/style.css"/>
    <script type="text/javascript" src="../../xznstatic/js/jquery-1.11.3.min.js"></script>
    <script type="text/javascript" src="../../xznstatic/js/jquery.SuperSlide.2.1.1.js"></script>
    <link rel="stylesheet" href="../../xznstatic/css/bootstrap.min.css" />

    <link rel="stylesheet" href="../../css/theme.css"/>
</head>
<style>

    body, h1, h2, h3, h4, h5, h6, hr, p, blockquote,
        /* structural elements 结构元素 */

    dl, dt, dd, ul, ol, li,
        /* list elements 列表元素 */


    th, td {
        /* table elements 表格元素 */
        margin: 0;
        padding: 0;
    }
    /* 设置默认字体 */

    body, button, input, select, textarea {
        /* for ie */
        /*font: 12px/1 Tahoma, Helvetica, Arial, "宋体", sans-serif;*/
        font: 12px/1 Tahoma, Helvetica, Arial, "\5b8b\4f53", sans-serif;
        /* 用 ascii 字符表示，使得在任何编码下都无问题 */
    }


    ul, ol {
        list-style: none;
    }
    /* 重置文本格式元素 */

    a {
        text-decoration: none;
        color: #000;
    }


    fieldset, img {
        border: none;
    }
    /* img 搭车：让链接里的 img 无边框 */
    /* 注：optgroup 无法扶正 */


    .cartMain{
        position: relative;
        width: 1200px;
        min-width: 1200px;
        max-width: 1200px;
        margin: 0 auto;
        padding: 0px 0px 100px;
        min-height: 210px;
    }

    /*购物车头部*/
    .cartMain_hd{
        background:#eee;
        width: 1200px;
        height: 50px;
        line-height: 50px;
        color: #3c3c3c;
    }
    .cartMain_hd .cartTop{
        height: 50px;
    }
    .cartMain_hd .cartTop .list_chk{
        width: 5%;
        text-indent: 30px;
    }
    .cartMain_hd .cartTop .list_con{
        width: 30%;
    }
    .cartMain_hd .cartTop .list_chk label{
        position: absolute;
        left: 10px;
        top:19px;
        margin: 0;
    }
    .cartMain_hd .cartTop .list_info{
        padding:0px;
        text-indent: 40px;
    }
    .cartMain_hd .cartTop .list_con{
        text-indent: -20px;
    }

    .cartMain_hd .order_lists .list_price{
        width: 15%;
    }
    .cartMain_hd .order_lists .list_amount{
        width: 15%;
    }
    .cartMain_hd .order_lists .list_sum{
        width: 20%;
    }

    .cartBox{
        width: 100%;
        margin-bottom: 15px;
    }
    .cartBox .shop_info{
        position: relative;
        width: 100%;
        height: 38px;
        background-color: #fff;
        line-height: 38px;
        vertical-align: baseline;
    }
    .cartBox .shop_info .all_check{
        position: relative;
        float: left;
        width: 30px;
        height: 38px;
    }

    .cartBox .shop_info .all_check input[type="checkbox"]{
        position: absolute;
        z-index: 0;
        left: -20px;
        top: -20px;
    }
    .cartBox .shop_info .all_check .shop{
        position: absolute;
        top:13px;
    }
    .cartBox .shop_info .shop_name{
        float: left;
    }
    /*商品列表*/
    .cartBox .order_content{
        border-top: 1px solid #ccc;
    }
    .cartBox .order_content a{
        display: block;
    }
    .order_lists{
        width: 100%;
        height: 130px;
        border-bottom: 1px solid #e7e7e7;
    }
    .order_lists:last-child{
        border-bottom: none;
    }
    .order_lists li{
        float: left;
        height: 100%;
    }

    .order_lists .list_chk{
        position: relative;
        width: 5%;
    }
    .order_lists .list_chk input[type="checkbox"]{
        position: absolute;
        z-index: 0;
        left: -20px;
        top: -20px;
    }
    .order_lists .list_chk label{
        margin: 50px 0 0 24px;
    }

    .order_lists .list_con{
        width: 30%;
    }
    .order_lists .list_con .list_img{
        width: 30%;
        height: 90px;
        margin-top: 20px;
        float: left;
    }
    .order_lists .list_con .list_img img{
        width: 100%;
        vertical-align: top;
    }
    .order_lists .list_con .list_text{
        margin: 50px 0 0 30px;
        line-height: 18px;
        width: 200px;
        float: left;
    }
    .order_lists .list_con .list_text a{
        color: #3c3c3c;
    }
    .order_lists .list_con .list_text a:hover{
        color: #fb0000;
        text-decoration: underline;
    }

    .order_lists .list_info{
        width: 252px;
        box-sizing: border-box;
        padding: 30px 0;
    }
    .order_lists .list_info p{
        color: #4c4c4c;
        line-height: 32px;
        margin-left: 35px;
    }
    .order_lists .list_price{
        width: 13%;
    }
    .order_lists .list_price .price{
        margin-top: 50px;
        line-height: 18px;
        color: #4c4c4c;
    }
    .order_lists .list_amount{
        width: 15%;
    }
    .order_lists .list_amount .amount_box{
        margin-top: 50px;
        width: 77px;
        height: 25px;
        position: relative;
    }
    .order_lists .list_amount .amount_box input{
        width: 39px;
        height: 15px;
        line-height: 15px;
        border:none;
        outline:none;
        color: #4c4c4c;
        text-align: center;
        padding: 4px 0;
        z-index: 2;
        position: absolute;
        left: 18px;
        float: left;
    }
    .order_lists .list_amount .amount_box a{
        float: left;
        height: 23px;
        width: 17px;
        border: 1px solid #e5e5e5;
        background: #f0f0f0;
        text-align: center;
        line-height: 23px;
        color: #444;
        position: absolute;
        top:0;
    }
    .order_lists .list_amount .amount_box a:hover{
        border-color: #ff873e;
        text-decoration: none;
        color: #fb0000;
        z-index: 3;
    }

    .order_lists .list_amount .amount_box .reduce{
        width:20px;
        height:20px;
        border-radius:10px;
        border:1px solid #b8b8b8;
        line-height:20px;
        left: -10px;
    }

    .order_lists .list_amount .amount_box .reSty{
        color: #4c4c4c;
    }
    .order_lists .list_amount .amount_box .reSty:hover{
        border-right: none;
        border-color: #fb0000;
        text-decoration: none;
        color: #cbcbcb;
    }

    .order_lists .list_amount .amount_box .plus{
        width:20px;
        height:20px;
        border-radius:10px;
        border:1px solid #b8b8b8;
        line-height:20px;
        right: -10px;
    }


    .order_lists .list_sum{
        margin-left:20px;
        width: 20%;
    }
    .order_lists .list_sum .sum_price{
        margin-left:10px;
        line-height: 18px;
        margin-top: 50px;
        font:"微软雅黑", "宋体", "黑体";
        font-size:18px;
        color: #fb0000;
    }
    .order_lists .list_op{
        width: 10%;
    }
    .order_lists .list_op .del{
        margin-top: 50px;
        line-height: 18px;
    }

    /*底部总计算价*/
    .bar-wrapper{
        width: 1200px;
        height: 50px;
        position: fixed;
        bottom: -1px;
        z-index: 99;
        background: #eee;
    }
    .bar-wrapper .bar-right{
        float: right;
        color: #3c3c3c;
    }
    .bar-wrapper .bar-right strong{
        color: #fb0000;
    }

    .bar-wrapper .bar-right .piece{
        float: left;
        min-width: 125px;
        margin-right: 20px;
        height: 50px;
        line-height: 50px;
    }
    .bar-wrapper .bar-right .piece .piece_num{
        display: inline-block;
        padding: 0 10px;
        font-size: 18px;
    }
    .bar-wrapper .bar-right .totalMoney{
        float: left;
        min-width: 100px;
        height: 50px;
        line-height: 50px;
    }
    .bar-wrapper .bar-right .totalMoney .total_text{
        float: right;
        font-weight: 400;
        font-size: 20px;
        vertical-align: middle;
        margin-right: 10px;
        margin-left: 15px;
    }
    .bar-wrapper .bar-right .calBtn{
        float: left;
    }
    .bar-wrapper .bar-right .calBtn a{
        display: block;
        width: 120px;
        height: 50px;
        color: #fff;
        background: #fb0000;
        cursor: pointer;
        font-size: 22px;
        letter-spacing: 5px;
        text-decoration: none;
        line-height: 50px;
        text-align: center;
        border-radius: 8px;
    }
</style>
<body class='bodyClass'>
<div id="app">
    <el-dialog title="弹出内容" :visible.sync="showContentModal" :modal-append-to-body="false">
        <div class="content" style="word-break: break-all;" v-html="showContent">
        </div>
        <div slot="footer" class="dialog-footer">
            <el-button @click="showContentModal = false">关 闭</el-button>
        </div>
    </el-dialog>

    <div style="height: 100px;width: 1200px;margin: 0 auto;">
        <h1 style="font-size: 30px;margin-top: 35px;float: left;width: 150px"><i style="font-size: 30px;" class="layui-icon">&#xe657;</i>购物车 </h1>
        <el-steps :active="1" finish-status="success" simple style="margin-top: 20px;float: right;width: 500px">
            <el-step title="选购商品" ></el-step>
            <el-step title="确认订单" ></el-step>
            <el-step title="完成订单" ></el-step>
        </el-steps>
    </div>
    <!-- 标题 -->
    <section class="cartMain">
        <div class="cartMain_hd">
            <ul class="order_lists cartTop">
                <li class="list_chk">
                </li>
                <li class="list_con"><span style="margin-left: 20px">名称</span></li>
                <li class="list_price">单价（元）</li>
                <li class="list_amount">数量</li>
                <li class="list_sum">小计（元）</li>
                <li class="list_op">操作</li>
            </ul>
        </div>
        <div class="cartBox">
            <div class="shop_info">
            </div>
            <div class="order_content">
                <ul class="order_lists" v-for="(item,index) in dataList" v-bind:key="index">
                    <li class="list_chk">
                    </li>
                    <li class="list_con">
                        <div class="list_img"><img :src="formatImageUrl(item.nongchanpinPhoto, baseUrl)"></div>
                        <div class="list_text">{{item.nongchanpinName}}</div>
                    </li>
                    <li class="list_price">
                        <p class="price">{{item.nongchanpinNewMoney}} RMB</p>
                    </li>
                    <li class="list_amount">
                        <div class="amount_box">
                            <a @click="reduceCartNum(index)" class="reduce reSty">-</a>
                            <input type="text" :value="item.buyNumber" id="buyNumber" name="buyNumber" class="sum">
                            <a @click="addCartNum(index)" class="plus">+</a>
                        </div>
                    </li>
                    <li class="list_sum">
                        <p class="sum_price">{{(item.nongchanpinNewMoney*item.buyNumber).toFixed(2)}} RMB</p>
                    </li>
                    <li class="list_op">
                        <p class="del"><a  @click="deleteCart(index)"class="delBtn"><i class="layui-icon">&#xe640;</i> 移除商品</a></p>
                    </li>
                </ul>
            </div>
        </div>

        <!--底部-->
        <div class="bar-wrapper">
            <div class="bar-right">
                <div class="piece">共<strong class="piece_num">{{dataList.length}}</strong>件商品</div>
                <div class="totalMoney">共计: <strong class="total_text">{{totalZongjiaMoney.toFixed(2)}}RMB</strong></div>
                <div class="calBtn"><a @click="buyClick" >下单</a></div>
            </div>
        </div>
    </section>

</div></div>

<script src="../../xznstatic/js/bootstrap.min.js" type="text/javascript" charset="utf-8"></script>
<script src="../../xznstatic/js/echarts.min.js" type="text/javascript" charset="utf-8"></script>
<script src="../../layui/layui.js"></script>
<script src="../../js/vue.js"></script>
<!-- 引入element组件库 -->
<script src="../../xznstatic/js/element.min.js"></script>
<!-- 引入element样式 -->
<link rel="stylesheet" href="../../xznstatic/css/element.min.css">
<script src="../../js/config.js"></script>
<script src="../../modules/config.js"></script>
<script src="../../js/utils.js"></script>

<script type="text/javascript">
    Vue.prototype.myFilters = function (msg) {
        if(msg==null || msg==""){
            return "";
        }else if (msg.length>20){
            msg.replace(/\n/g, "<br>");
            return msg.substring(0,30)+"......";
        }else{
            return msg.replace(/\n/g, "<br>");
        }
    };
    var vue = new Vue({
        el: '#app',
        data: {
            userId: localStorage.getItem("userid"),//当前登录人的id
            sessionTable: localStorage.getItem("userTable"),//登录账户所在表名
            role: localStorage.getItem("role"),//权限
            user:{},//当前登录用户信息
            form:{
                yonghuId: '',
                nongchanpinId: '',
                buyNumber: '',
                createTime: '',
                updateTime: '',
                insertTime: '',
            },
            //小菜单
            centerMenu: centerMenu,
            //项目路径
            baseUrl:"",
            //弹出内容模态框
            showContentModal:false,
            showContent:"",
            //查询条件
            searchForm: {
                page: 1
                ,limit: 8
                ,sort: "id"//字段
                ,order: "desc"//asc desc
                    },

            dataList: [],
        },
        filters: {
            subString: function(val) {
                if (val) {
                    val = val.replace(/<[^<>]+>/g, '').replace(/undefined/g, '');
                    if (val.length > 60) {
                        val = val.substring(0, 60);
                        val+='...';
                    }
                    return val;
                }
                return '';
            }
        },
        computed: {
            totalZongjiaMoney: function() {
                var total = 0;
                for (var item of this.dataList) {
                    total += item.nongchanpinNewMoney * item.buyNumber
                }
                return total;
            }
        },
        methods: {
            formatImageUrl(url, baseUrl) {
                return formatImageUrl(url, baseUrl);
            },
            isAuth(tablename, button) {
                return isFrontAuth(tablename, button);
            },
            jump(url) {
                jump(url);
            },
            jumpCheck(url,check1,check2) {
                if(check1 == "2" || check1 == 2){//级联表的逻辑删除字段[1:未删除 2:已删除]
                    layui.layer.msg("已经被删除", {
                        time: 2000,
                        icon: 2
                    });
                    return false;
                }
                if(check2 == "2"  || check2 == 2){//是否下架[1:上架 2:下架]
                    layui.layer.msg("已经下架", {
                        time: 2000,
                        icon: 2
                    });
                    return false;
                }
                this.jump(url);
            },
            showContentFunction(content) {
                this.showContentModal=true;
                this.showContent=content.replaceAll(/\n/g, "<br>").replaceAll("src=\"upload/","src=\""+this.baseUrl+"upload/");
            },
            wuyong(id) {
                var mymessage = confirm("确定要    吗？");if(!mymessage){return false;}
                layui.http.requestJson(`cart/update`, 'post', {
                    id:id,
//                    cartTypes:1,
                }, function (res) {
                    if(res.code == 0){
                        layui.layer.msg('操作成功', {time: 2000, icon: 6 }, function () {window.location.reload();});
                    }else{
                        layui.layer.msg(res.msg, {time: 5000,icon: 5});
                    }
                });
            },
            deleteData(data){
                var mymessage = confirm("确定要删除这条数据吗？");
                if(!mymessage){
                    return false;
                }
                // 删除信息
                layui.http.requestJson(`cart/delete`, 'post', [data.id], (res) => {
                    if(res.code==0){
                        layer.msg('操作成功', {
                            time: 2000,
                            icon: 6
                        });
                        window.location.reload();
                    }else{
                        layer.msg(res.msg, {
                            time: 2000,
                            icon: 2
                        });
                    }
                });
            },
			// 添加数量
			addCartNum(index) {
				// 查询对应的商品的详情信息，判断是否有商品限次，库存的判断
				var item = this.dataList[index];
				layui.http.request(`nongchanpin/info/${item.nongchanpinId}`, 'get', {}, function(res) {
					// 库存限制
					if (res.data.nongchanpinKucunNumber && res.data.nongchanpinKucunNumber > 0 && res.data.nongchanpinKucunNumber <= item.buyNumber) {
						layui.layer.msg(`农产品库存不足`, {
							time: 2000,
							icon: 5
						});
						return
					}
					item.buyNumber = item.buyNumber + 1;
					layui.http.requestJson(`cart/update`, 'post', item, function(res) {});
				});
			},
			// 减少数量
			reduceCartNum(index) {
				var item = this.dataList[index];
				if (item.buyNumber > 1) {
					item.buyNumber = item.buyNumber - 1;
					layui.http.requestJson(`cart/update`, 'post', item, function(res) {});
				}
			},
			// 删除
			deleteCart(index) {
				var item = this.dataList[index];
				layui.http.requestJson(`cart/delete`, 'post', [item.id], function(res) {
					window.location.reload();
				});
			},
			// 购买
			buyClick() {
			 	if(this.dataList.length<=0){
                        layui.layer.msg(`您的购物车为空，快去将自己喜欢的商品放入购物车吧！`, {
                        time: 2000,
                        icon: 5
                    });
                    return false;
                }
                
                // 处理图片URL，确保存储到localStorage中的图片URL是正确的
                let processedData = JSON.parse(JSON.stringify(this.dataList));
                processedData.forEach(item => {
                    if (item.nongchanpinPhoto) {
                        // 先将图片URL处理成完整URL
                        item.nongchanpinPhoto = formatImageUrl(item.nongchanpinPhoto, this.baseUrl);
                    }
                });
                
				localStorage.setItem('nongchanpins', JSON.stringify(processedData));
				window.location.href = '../nongchanpinOrder/confirm.html'
			},
        }
    });

    layui.use(['layer', 'element', 'carousel', 'laypage', 'http', 'jquery', 'laydate', 'tinymce'], function() {
        var layer = layui.layer;
        var element = layui.element;
        var carousel = layui.carousel;
        var laypage = layui.laypage;
        var http = layui.http;
        var laydate = layui.laydate;
        var tinymce = layui.tinymce;
        window.jQuery = window.$ = jquery = layui.jquery;

        localStorage.setItem("goUtl","./pages/cart/list.html")

        vue.baseUrl = http.baseurl

        let table = localStorage.getItem("userTable");
        if(table){
            http.request(table+"/session", 'get', {}, function (data) {
                vue.user = data.data;
            });
        }




                                                        
        // 获取列表数据
        http.request('cart/page', 'get', {
            page: 1,
            limit: vue.searchForm.limit,
            yonghuId: localStorage.getItem('userid')
        }, function(res) {
            vue.dataList = res.data.list
            // 分页
            laypage.render({
                elem: 'pager',
                count: res.data.total,
                limit: vue.searchForm.limit,
                jump: function(obj, first) {
                    //首次不执行
                    if (!first) {
                        http.request('news/list', 'get', {
                            page: obj.curr,
                            limit: obj.limit,
                            yonghuId: localStorage.getItem('userid')
                        }, function(res) {
                            vue.dataList = res.data.list
                        })
                    }
                }
            });
        })    });

    window.xznSlide = function () {
        jQuery(".banner").slide({mainCell: ".bd ul", autoPlay: true, interTime: 5000});
        jQuery("#ifocus").slide({
            titCell: "#ifocus_btn li",
            mainCell: "#ifocus_piclist ul",
            effect: "leftLoop",
            delayTime: 200,
            autoPlay: true,
            triggerTime: 0
        });
        jQuery("#ifocus").slide({titCell: "#ifocus_btn li", mainCell: "#ifocus_tx ul", delayTime: 0, autoPlay: true});
        jQuery(".product_list").slide({
            mainCell: ".bd ul",
            autoPage: true,
            effect: "leftLoop",
            autoPlay: true,
            vis: 5,
            trigger: "click",
            interTime: 4000
        });
    };
</script>
</body>
</html>
