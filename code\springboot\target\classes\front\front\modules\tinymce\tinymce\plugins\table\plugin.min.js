/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.2.0 (2020-02-13)
 */
!function(f){"use strict";function o(e){return e}var R=function(e){function n(){return t}var t=e;return{get:n,set:function(e){t=e},clone:function(){return R(n())}}},T=function(){},O=function(t,r){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return t(r.apply(null,e))}},D=function(e){return function(){return e}};function b(r){for(var o=[],e=1;e<arguments.length;e++)o[e-1]=arguments[e];return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];var t=o.concat(e);return r.apply(null,t)}}function d(t){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return!t.apply(null,e)}}function e(){return u}var n,s=D(!1),i=D(!0),u=(n={fold:function(e,n){return e()},is:s,isSome:s,isNone:i,getOr:c,getOrThunk:r,getOrDie:function(e){throw new Error(e||"error: getOrDie called on none.")},getOrNull:D(null),getOrUndefined:D(undefined),or:c,orThunk:r,map:e,each:T,bind:e,exists:s,forall:i,filter:e,equals:t,equals_:t,toArray:function(){return[]},toString:D("none()")},Object.freeze&&Object.freeze(n),n);function t(e){return e.isNone()}function r(e){return e()}function c(e){return e}function a(n){return function(e){return function(e){if(null===e)return"null";var n=typeof e;return"object"==n&&(Array.prototype.isPrototypeOf(e)||e.constructor&&"Array"===e.constructor.name)?"array":"object"==n&&(String.prototype.isPrototypeOf(e)||e.constructor&&"String"===e.constructor.name)?"string":n}(e)===n}}function l(e,n){return-1<function(e,n){return qe.call(e,n)}(e,n)}function m(e,n){for(var t=0,r=e.length;t<r;t++){if(n(e[t],t))return!0}return!1}function g(e,n){for(var t=e.length,r=new Array(t),o=0;o<t;o++){var i=e[o];r[o]=n(i,o)}return r}function p(e,n){for(var t=0,r=e.length;t<r;t++){n(e[t],t)}}function h(e,n){for(var t=[],r=0,o=e.length;r<o;r++){var i=e[r];n(i,r)&&t.push(i)}return t}function v(e,n,t){return function(e,n){for(var t=e.length-1;0<=t;t--){n(e[t],t)}}(e,function(e){t=n(t,e)}),t}function w(e,n,t){return p(e,function(e){t=n(t,e)}),t}function y(e,n){for(var t=0,r=e.length;t<r;t++){var o=e[t];if(n(o,t))return We.some(o)}return We.none()}function C(e,n){for(var t=0,r=e.length;t<r;t++){if(n(e[t],t))return We.some(t)}return We.none()}function S(e){for(var n=[],t=0,r=e.length;t<r;++t){if(!je(e[t]))throw new Error("Arr.flatten item "+t+" was not an array, input: "+e);Ve.apply(n,e[t])}return n}function x(e,n){return S(g(e,n))}function A(e,n){for(var t=0,r=e.length;t<r;++t){if(!0!==n(e[t],t))return!1}return!0}function E(e){var n=Ue.call(e,0);return n.reverse(),n}function N(e,n){for(var t=0;t<e.length;t++){var r=n(e[t],t);if(r.isSome())return r}return We.none()}function k(e,n){for(var t=Ge(e),r=0,o=t.length;r<o;r++){var i=t[r];n(e[i],i)}}function I(e,t){return Ke(e,function(e,n){return{k:n,v:t(e,n)}})}function B(e,n){return Xe(e,n)?We.from(e[n]):We.none()}function P(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];if(n.length!==t.length)throw new Error('Wrong number of arguments to struct. Expected "['+n.length+']", got '+t.length+" arguments");var r={};return p(n,function(e,n){r[e]=D(t[n])}),r}}function M(e){return e.slice(0).sort()}function W(e,n){throw new Error("All required keys ("+M(e).join(", ")+") were not specified. Specified keys were: "+M(n).join(", ")+".")}function _(e){throw new Error("Unsupported keys for object: "+M(e).join(", "))}function L(n,e){if(!je(e))throw new Error("The "+n+" fields must be an array. Was: "+e+".");p(e,function(e){if(!Le(e))throw new Error("The value "+e+" in the "+n+" fields was not a string.")})}function j(e){var t=M(e);y(t,function(e,n){return n<t.length-1&&e===t[n+1]}).each(function(e){throw new Error("The field: "+e+" occurs more than once in the combined fields: ["+t.join(", ")+"].")})}function z(e){return e.dom().nodeType}function H(n){return function(e){return z(e)===n}}function F(e){return z(e)===Je||"#comment"===nn(e)}function U(e,n,t){if(!(Le(t)||ze(t)||Fe(t)))throw f.console.error("Invalid call to Attr.set. Key ",n,":: Value ",t,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(n,t+"")}function q(e,n,t){U(e.dom(),n,t)}function V(e,n){var t=e.dom();k(n,function(e,n){U(t,n,e)})}function G(e,n){var t=e.dom().getAttribute(n);return null===t?undefined:t}function Y(e,n){var t=e.dom();return!(!t||!t.hasAttribute)&&t.hasAttribute(n)}function K(e,n){e.dom().removeAttribute(n)}function X(e){return w(e.dom().attributes,function(e,n){return e[n.name]=n.value,e},{})}function $(e,n,t){return""===n||!(e.length<n.length)&&e.substr(t,t+n.length)===n}function J(e,n){return-1!==e.indexOf(n)}function Q(e,n){return $(e,n,0)}function Z(e){return e.style!==undefined&&He(e.style.getPropertyValue)}function ee(t){var r,o=!1;return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return o||(o=!0,r=t.apply(null,e)),r}}function ne(e){var n=rn(e)?e.dom().parentNode:e.dom();return n!==undefined&&null!==n&&n.ownerDocument.body.contains(n)}function te(e,n,t){if(!Le(t))throw f.console.error("Invalid call to CSS.set. Property ",n,":: Value ",t,":: Element ",e),new Error("CSS value must be a string: "+t);Z(e)&&e.style.setProperty(n,t)}function re(e,n,t){var r=e.dom();te(r,n,t)}function oe(e,n){var t=e.dom();k(n,function(e,n){te(t,n,e)})}function ie(e,n){var t=e.dom(),r=f.window.getComputedStyle(t).getPropertyValue(n),o=""!==r||ne(e)?r:ln(t,n);return null===o?undefined:o}function ue(e,n){var t=e.dom(),r=ln(t,n);return We.from(r).filter(function(e){return 0<e.length})}function ce(e,n){!function(e,n){Z(e)&&e.style.removeProperty(n)}(e.dom(),n),Y(e,"style")&&""===function(e){return e.replace(/^\s+|\s+$/g,"")}(G(e,"style"))&&K(e,"style")}function ae(e,n,t){return 0!=(e.compareDocumentPosition(n)&t)}function le(e,n){var t=function(e,n){for(var t=0;t<e.length;t++){var r=e[t];if(r.test(n))return r}return undefined}(e,n);if(!t)return{major:0,minor:0};function r(e){return Number(n.replace(t,"$"+e))}return mn(r(1),r(2))}function fe(e,n){return function(){return n===e}}function se(e,n){return function(){return n===e}}function de(e,n){var t=String(n).toLowerCase();return y(e,function(e){return e.search(t)})}function me(n){return function(e){return J(e,n)}}function ge(){return kn.get()}function pe(e,n){var t=e.dom();if(t.nodeType!==In)return!1;var r=t;if(r.matches!==undefined)return r.matches(n);if(r.msMatchesSelector!==undefined)return r.msMatchesSelector(n);if(r.webkitMatchesSelector!==undefined)return r.webkitMatchesSelector(n);if(r.mozMatchesSelector!==undefined)return r.mozMatchesSelector(n);throw new Error("Browser lacks native selectors")}function he(e){return e.nodeType!==In&&e.nodeType!==Bn||0===e.childElementCount}function ve(e){return un.fromDom(e.dom().ownerDocument)}function be(e){return We.from(e.dom().parentNode).map(un.fromDom)}function we(e,n){for(var t=He(n)?n:s,r=e.dom(),o=[];null!==r.parentNode&&r.parentNode!==undefined;){var i=r.parentNode,u=un.fromDom(i);if(o.push(u),!0===t(u))break;r=i}return o}function ye(e){return We.from(e.dom().previousSibling).map(un.fromDom)}function Ce(e){return We.from(e.dom().nextSibling).map(un.fromDom)}function Se(e){return g(e.dom().childNodes,un.fromDom)}function xe(e,n){var t=e.dom().childNodes;return We.from(t[n]).map(un.fromDom)}function Re(n,t){be(n).each(function(e){e.dom().insertBefore(t.dom(),n.dom())})}function Te(e,n){Ce(e).fold(function(){be(e).each(function(e){_n(e,n)})},function(e){Re(e,n)})}function Oe(n,t){(function(e){return xe(e,0)})(n).fold(function(){_n(n,t)},function(e){n.dom().insertBefore(t.dom(),e.dom())})}function De(e,n){Re(e,n),_n(n,e)}function Ae(r,o){p(o,function(e,n){var t=0===n?r:o[n-1];Te(t,e)})}function Ee(n,e){p(e,function(e){_n(n,e)})}function Ne(e){e.dom().textContent="",p(Se(e),function(e){Ln(e)})}function ke(e){var n=Se(e);0<n.length&&function(n,e){p(e,function(e){Re(n,e)})}(e,n),Ln(e)}function Ie(e,n,t){return function(e,n,t){return h(we(e,t),n)}(e,function(e){return pe(e,n)},t)}function Be(e,n){return function(e,n){return h(Se(e),n)}(e,function(e){return pe(e,n)})}function Pe(e,n){return function(e,n){var t=n===undefined?f.document:n.dom();return he(t)?[]:g(t.querySelectorAll(e),un.fromDom)}(n,e)}var Me=function(t){function e(){return o}function n(e){return e(t)}var r=D(t),o={fold:function(e,n){return n(t)},is:function(e){return t===e},isSome:i,isNone:s,getOr:r,getOrThunk:r,getOrDie:r,getOrNull:r,getOrUndefined:r,or:e,orThunk:e,map:function(e){return Me(e(t))},each:function(e){e(t)},bind:n,exists:n,forall:n,filter:function(e){return e(t)?o:u},toArray:function(){return[t]},toString:function(){return"some("+t+")"},equals:function(e){return e.is(t)},equals_:function(e,n){return e.fold(s,function(e){return n(t,e)})}};return o},We={some:Me,none:e,from:function(e){return null===e||e===undefined?u:Me(e)}},_e=tinymce.util.Tools.resolve("tinymce.PluginManager"),Le=a("string"),je=a("array"),ze=a("boolean"),He=a("function"),Fe=a("number"),Ue=Array.prototype.slice,qe=Array.prototype.indexOf,Ve=Array.prototype.push,Ge=(He(Array.from)&&Array.from,Object.keys),Ye=Object.hasOwnProperty,Ke=function(e,r){var o={};return k(e,function(e,n){var t=r(e,n);o[t.k]=t.v}),o},Xe=function(e,n){return Ye.call(e,n)},$e=function(o,i){var u=o.concat(i);if(0===u.length)throw new Error("You must specify at least one required or optional field.");return L("required",o),L("optional",i),j(u),function(n){var t=Ge(n);A(o,function(e){return l(t,e)})||W(o,t);var e=h(t,function(e){return!l(u,e)});0<e.length&&_(e);var r={};return p(o,function(e){r[e]=D(n[e])}),p(i,function(e){r[e]=D(Object.prototype.hasOwnProperty.call(n,e)?We.some(n[e]):We.none())}),r}},Je=(f.Node.ATTRIBUTE_NODE,f.Node.CDATA_SECTION_NODE,f.Node.COMMENT_NODE),Qe=f.Node.DOCUMENT_NODE,Ze=(f.Node.DOCUMENT_TYPE_NODE,f.Node.DOCUMENT_FRAGMENT_NODE,f.Node.ELEMENT_NODE),en=f.Node.TEXT_NODE,nn=(f.Node.PROCESSING_INSTRUCTION_NODE,f.Node.ENTITY_REFERENCE_NODE,f.Node.ENTITY_NODE,f.Node.NOTATION_NODE,"undefined"!=typeof f.window?f.window:Function("return this;")(),function(e){return e.dom().nodeName.toLowerCase()}),tn=H(Ze),rn=H(en),on=function(e){if(null===e||e===undefined)throw new Error("Node cannot be null or undefined");return{dom:D(e)}},un={fromHtml:function(e,n){var t=(n||f.document).createElement("div");if(t.innerHTML=e,!t.hasChildNodes()||1<t.childNodes.length)throw f.console.error("HTML does not have a single root node",e),new Error("HTML must have a single root node");return on(t.childNodes[0])},fromTag:function(e,n){var t=(n||f.document).createElement(e);return on(t)},fromText:function(e,n){var t=(n||f.document).createTextNode(e);return on(t)},fromDom:on,fromPoint:function(e,n,t){var r=e.dom();return We.from(r.elementFromPoint(n,t)).map(on)}},cn=ee(function(){return an(un.fromDom(f.document))}),an=function(e){var n=e.dom().body;if(null===n||n===undefined)throw new Error("Body is not available yet");return un.fromDom(n)},ln=function(e,n){return Z(e)?e.style.getPropertyValue(n):""},fn=function(e,n){return ae(e,n,f.Node.DOCUMENT_POSITION_CONTAINED_BY)},sn=function(){return(sn=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++)for(var o in n=arguments[t])Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o]);return e}).apply(this,arguments)},dn=function(){return mn(0,0)},mn=function(e,n){return{major:e,minor:n}},gn={nu:mn,detect:function(e,n){var t=String(n).toLowerCase();return 0===e.length?dn():le(e,t)},unknown:dn},pn="Firefox",hn=function(e){var n=e.current;return{current:n,version:e.version,isEdge:fe("Edge",n),isChrome:fe("Chrome",n),isIE:fe("IE",n),isOpera:fe("Opera",n),isFirefox:fe(pn,n),isSafari:fe("Safari",n)}},vn={unknown:function(){return hn({current:undefined,version:gn.unknown()})},nu:hn,edge:D("Edge"),chrome:D("Chrome"),ie:D("IE"),opera:D("Opera"),firefox:D(pn),safari:D("Safari")},bn="Windows",wn="Android",yn="Solaris",Cn="FreeBSD",Sn="ChromeOS",xn=function(e){var n=e.current;return{current:n,version:e.version,isWindows:se(bn,n),isiOS:se("iOS",n),isAndroid:se(wn,n),isOSX:se("OSX",n),isLinux:se("Linux",n),isSolaris:se(yn,n),isFreeBSD:se(Cn,n),isChromeOS:se(Sn,n)}},Rn={unknown:function(){return xn({current:undefined,version:gn.unknown()})},nu:xn,windows:D(bn),ios:D("iOS"),android:D(wn),linux:D("Linux"),osx:D("OSX"),solaris:D(yn),freebsd:D(Cn),chromeos:D(Sn)},Tn=function(e,t){return de(e,t).map(function(e){var n=gn.detect(e.versionRegexes,t);return{current:e.name,version:n}})},On=function(e,t){return de(e,t).map(function(e){var n=gn.detect(e.versionRegexes,t);return{current:e.name,version:n}})},Dn=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,An=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(e){return J(e,"edge/")&&J(e,"chrome")&&J(e,"safari")&&J(e,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,Dn],search:function(e){return J(e,"chrome")&&!J(e,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(e){return J(e,"msie")||J(e,"trident")}},{name:"Opera",versionRegexes:[Dn,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:me("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:me("firefox")},{name:"Safari",versionRegexes:[Dn,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(e){return(J(e,"safari")||J(e,"mobile/"))&&J(e,"applewebkit")}}],En=[{name:"Windows",search:me("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(e){return J(e,"iphone")||J(e,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:me("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:me("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:me("linux"),versionRegexes:[]},{name:"Solaris",search:me("sunos"),versionRegexes:[]},{name:"FreeBSD",search:me("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:me("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],Nn={browsers:D(An),oses:D(En)},kn=R(function(e,n){var t=Nn.browsers(),r=Nn.oses(),o=Tn(t,e).fold(vn.unknown,vn.nu),i=On(r,e).fold(Rn.unknown,Rn.nu);return{browser:o,os:i,deviceType:function(e,n,t,r){var o=e.isiOS()&&!0===/ipad/i.test(t),i=e.isiOS()&&!o,u=e.isiOS()||e.isAndroid(),c=u||r("(pointer:coarse)"),a=o||!i&&u&&r("(min-device-width:768px)"),l=i||u&&!a,f=n.isSafari()&&e.isiOS()&&!1===/safari/i.test(t),s=!l&&!a&&!f;return{isiPad:D(o),isiPhone:D(i),isTablet:D(a),isPhone:D(l),isTouch:D(c),isAndroid:e.isAndroid,isiOS:e.isiOS,isWebView:D(f),isDesktop:D(s)}}(i,o,e,n)}}(f.navigator.userAgent,function(e){return f.window.matchMedia(e).matches})),In=Ze,Bn=Qe,Pn=function(e,n){return e.dom()===n.dom()},Mn=ge().browser.isIE()?function(e,n){return fn(e.dom(),n.dom())}:function(e,n){var t=e.dom(),r=n.dom();return t!==r&&t.contains(r)},Wn=pe,_n=(P("element","offset"),function(e,n){e.dom().appendChild(n.dom())}),Ln=function(e){var n=e.dom();null!==n.parentNode&&n.parentNode.removeChild(n)},jn=(P("width","height"),P("width","height"),P("rows","columns")),zn=P("row","column"),Hn=(P("x","y"),P("element","rowspan","colspan")),Fn=P("element","rowspan","colspan","isNew"),Un=P("element","rowspan","colspan","row","column"),qn=P("element","cells","section"),Vn=P("element","isNew"),Gn=P("element","cells","section","isNew"),Yn=P("cells","section"),Kn=P("details","section"),Xn=P("startRow","startCol","finishRow","finishCol"),$n=function(e,n){var t=[];return p(Se(e),function(e){n(e)&&(t=t.concat([e])),t=t.concat($n(e,n))}),t};function Jn(e,n,t,r,o){return e(t,r)?We.some(t):He(o)&&o(t)?We.none():n(t,r,o)}function Qn(e,n,t){for(var r=e.dom(),o=He(t)?t:D(!1);r.parentNode;){r=r.parentNode;var i=un.fromDom(r);if(n(i))return We.some(i);if(o(i))break}return We.none()}function Zn(e,n,t){return Qn(e,function(e){return pe(e,n)},t)}function et(e,n){return function(e,n){return y(e.dom().childNodes,function(e){return n(un.fromDom(e))}).map(un.fromDom)}(e,function(e){return pe(e,n)})}function nt(e,n){return function(e,n){var t=n===undefined?f.document:n.dom();return he(t)?We.none():We.from(t.querySelector(e)).map(un.fromDom)}(n,e)}function tt(e,n,t){return Jn(pe,Zn,e,n,t)}function rt(e,n,t){return void 0===t&&(t=s),t(n)?We.none():l(e,nn(n))?We.some(n):Zn(n,e.join(","),function(e){return pe(e,"table")||t(e)})}function ot(n,e){return be(e).map(function(e){return Be(e,n)})}function it(e,n){return parseInt(G(e,n),10)}function ut(e,n){return e+","+n}var ct=function(e,n,t){return x(Se(e),function(e){return pe(e,n)?t(e)?[e]:[]:ct(e,n,t)})},at={firstLayer:function(e,n){return ct(e,n,D(!0))},filterFirstLayer:ct},lt=b(ot,"th,td"),ft=b(ot,"tr"),st={cell:function(e,n){return rt(["td","th"],e,n)},firstCell:function(e){return nt(e,"th,td")},cells:function(e){return at.firstLayer(e,"th,td")},neighbourCells:lt,table:function(e,n){return tt(e,"table",n)},row:function(e,n){return rt(["tr"],e,n)},rows:function(e){return at.firstLayer(e,"tr")},notCell:function(e,n){return rt(["caption","tr","tbody","tfoot","thead"],e,n)},neighbourRows:ft,attr:it,grid:function(e,n,t){var r=it(e,n),o=it(e,t);return jn(r,o)}},dt=function(e){var n=st.rows(e);return g(n,function(e){var n=e,t=be(n).map(function(e){var n=nn(e);return"tfoot"===n||"thead"===n||"tbody"===n?n:"tbody"}).getOr("tbody"),r=g(st.cells(e),function(e){var n=Y(e,"rowspan")?parseInt(G(e,"rowspan"),10):1,t=Y(e,"colspan")?parseInt(G(e,"colspan"),10):1;return Hn(e,n,t)});return qn(n,r,t)})},mt=function(e,t){return g(e,function(e){var n=g(st.cells(e),function(e){var n=Y(e,"rowspan")?parseInt(G(e,"rowspan"),10):1,t=Y(e,"colspan")?parseInt(G(e,"colspan"),10):1;return Hn(e,n,t)});return qn(e,n,t.section())})},gt=function(e,n){var t=x(e.all(),function(e){return e.cells()});return h(t,n)},pt={generate:function(e){var l={},n=[],t=e.length,f=0;p(e,function(e,c){var a=[];p(e.cells(),function(e){for(var n=0;l[ut(c,n)]!==undefined;)n++;for(var t=Un(e.element(),e.rowspan(),e.colspan(),c,n),r=0;r<e.colspan();r++)for(var o=0;o<e.rowspan();o++){var i=n+r,u=ut(c+o,i);l[u]=t,f=Math.max(f,i+1)}a.push(t)}),n.push(qn(e.element(),a,e.section()))});var r=jn(t,f);return{grid:D(r),access:D(l),all:D(n)}},getAt:function(e,n,t){var r=e.access()[ut(n,t)];return r!==undefined?We.some(r):We.none()},findItem:function(e,n,t){var r=gt(e,function(e){return t(n,e.element())});return 0<r.length?We.some(r[0]):We.none()},filterItems:gt,justCells:function(e){var n=g(e.all(),function(e){return e.cells()});return S(n)}},ht=P("minRow","minCol","maxRow","maxCol"),vt=function(e,n){function t(e){return pe(e.element(),n)}var r=dt(e),o=pt.generate(r),i=function(e,i){var n=e.grid().columns(),u=e.grid().rows(),c=n,a=0,l=0;return k(e.access(),function(e){if(i(e)){var n=e.row(),t=n+e.rowspan()-1,r=e.column(),o=r+e.colspan()-1;n<u?u=n:a<t&&(a=t),r<c?c=r:l<o&&(l=o)}}),ht(u,c,a,l)}(o,t),u="th:not("+n+"),td:not("+n+")",c=at.filterFirstLayer(e,"th,td",function(e){return pe(e,u)});return p(c,Ln),function(e,n,t,r){for(var o,i,u,c=n.grid().columns(),a=n.grid().rows(),l=0;l<a;l++)for(var f=!1,s=0;s<c;s++){if(!(l<t.minRow()||l>t.maxRow()||s<t.minCol()||s>t.maxCol()))pt.getAt(n,l,s).filter(r).isNone()?(o=f,void 0,i=e[l].element(),u=un.fromTag("td"),_n(u,un.fromTag("br")),(o?_n:Oe)(i,u)):f=!0}}(r,o,i,t),function(e,n){var t=h(at.firstLayer(e,"tr"),function(e){return 0===e.dom().childElementCount});p(t,Ln),n.minCol()!==n.maxCol()&&n.minRow()!==n.maxRow()||p(at.firstLayer(e,"th,td"),function(e){K(e,"rowspan"),K(e,"colspan")}),K(e,"width"),K(e,"height"),ce(e,"width"),ce(e,"height")}(e,i),e};function bt(e){return Mt.get(e)}function wt(e){return Mt.getOption(e)}function yt(e,n){Mt.set(e,n)}function Ct(e){return"img"===nn(e)?1:wt(e).fold(function(){return Se(e).length},function(e){return e.length})}function St(e){return function(e){return wt(e).filter(function(e){return 0!==e.trim().length||-1<e.indexOf("\xa0")}).isSome()}(e)||l(Wt,nn(e))}function xt(e){return function(e,o){var i=function(e){for(var n=0;n<e.childNodes.length;n++){var t=un.fromDom(e.childNodes[n]);if(o(t))return We.some(t);var r=i(e.childNodes[n]);if(r.isSome())return r}return We.none()};return i(e.dom())}(e,St)}function Rt(e){return _t(e,St)}function Tt(e,n){return un.fromDom(e.dom().cloneNode(n))}function Ot(e){return Tt(e,!1)}function Dt(e){return Tt(e,!0)}function At(e,n){var t=function(e,n){var t=un.fromTag(n),r=X(e);return V(t,r),t}(e,n),r=Se(Dt(e));return Ee(t,r),t}function Et(){var e=un.fromTag("td");return _n(e,un.fromTag("br")),e}function Nt(e,n,t){var r=At(e,n);return k(t,function(e,n){null===e?K(r,n):q(r,n,e)}),r}function kt(e){return e}function It(e){return function(){return un.fromTag("tr",e.dom())}}function Bt(e,n){return n.column()>=e.startCol()&&n.column()+n.colspan()-1<=e.finishCol()&&n.row()>=e.startRow()&&n.row()+n.rowspan()-1<=e.finishRow()}function Pt(e,n,t){var r=pt.findItem(e,n,Pn),o=pt.findItem(e,t,Pn);return r.bind(function(n){return o.map(function(e){return function(e,n){return Xn(Math.min(e.row(),n.row()),Math.min(e.column(),n.column()),Math.max(e.row()+e.rowspan()-1,n.row()+n.rowspan()-1),Math.max(e.column()+e.colspan()-1,n.column()+n.colspan()-1))}(n,e)})})}var Mt=function $f(t,r){var n=function(e){return t(e)?We.from(e.dom().nodeValue):We.none()};return{get:function(e){if(!t(e))throw new Error("Can only get "+r+" value of a "+r+" node");return n(e).getOr("")},getOption:n,set:function(e,n){if(!t(e))throw new Error("Can only set raw "+r+" value of a "+r+" node");e.dom().nodeValue=n}}}(rn,"text"),Wt=["img","br"],_t=function(e,i){var u=function(e){for(var n=Se(e),t=n.length-1;0<=t;t--){var r=n[t];if(i(r))return We.some(r);var o=u(r);if(o.isSome())return o}return We.none()};return u(e)},Lt={cellOperations:function(i,e,u){return{row:It(e),cell:function(e){var n=ve(e.element()),t=un.fromTag(nn(e.element()),n.dom()),r=u.getOr(["strong","em","b","i","span","font","h1","h2","h3","h4","h5","h6","p","div"]),o=0<r.length?function(r,o,i){return xt(r).map(function(e){var n=i.join(","),t=Ie(e,n,function(e){return Pn(e,r)});return v(t,function(e,n){var t=Ot(n);return K(t,"contenteditable"),_n(e,t),t},o)}).getOr(o)}(e.element(),t,r):t;return _n(o,un.fromTag("br")),function(e,n){var t=e.dom(),r=n.dom();Z(t)&&Z(r)&&(r.style.cssText=t.style.cssText)}(e.element(),t),ce(t,"height"),1!==e.colspan()&&ce(e.element(),"width"),i(e.element(),t),t},replace:Nt,gap:Et}},paste:function(e){return{row:It(e),cell:Et,replace:kt,gap:Et}}},jt=function(e,n){var t=n.column(),r=n.column()+n.colspan()-1,o=n.row(),i=n.row()+n.rowspan()-1;return t<=e.finishCol()&&r>=e.startCol()&&o<=e.finishRow()&&i>=e.startRow()},zt=function(e,n){for(var t=!0,r=b(Bt,n),o=n.startRow();o<=n.finishRow();o++)for(var i=n.startCol();i<=n.finishCol();i++)t=t&&pt.getAt(e,o,i).exists(r);return t?We.some(n):We.none()},Ht=Pt,Ft=function(n,e,t){return Pt(n,e,t).bind(function(e){return zt(n,e)})},Ut=function(r,e,o,i){return pt.findItem(r,e,Pn).bind(function(e){var n=0<o?e.row()+e.rowspan()-1:e.row(),t=0<i?e.column()+e.colspan()-1:e.column();return pt.getAt(r,n+o,t+i).map(function(e){return e.element()})})},qt=function(t,e,n){return Ht(t,e,n).map(function(e){var n=pt.filterItems(t,b(jt,e));return g(n,function(e){return e.element()})})},Vt=function(e,n){return pt.findItem(e,n,function(e,n){return Mn(n,e)}).map(function(e){return e.element()})},Gt=function(e){var n=dt(e);return pt.generate(n)},Yt=function(t,r,o){return st.table(t).bind(function(e){var n=Gt(e);return Ut(n,t,r,o)})},Kt=function(e,n,t){var r=Gt(e);return qt(r,n,t)},Xt=function(e,n,t,r,o){var i=Gt(e),u=Pn(e,t)?We.some(n):Vt(i,n),c=Pn(e,o)?We.some(r):Vt(i,r);return u.bind(function(n){return c.bind(function(e){return qt(i,n,e)})})},$t=function(e,n,t){var r=Gt(e);return Ft(r,n,t)},Jt=["body","p","div","article","aside","figcaption","figure","footer","header","nav","section","ol","ul","li","table","thead","tbody","tfoot","caption","tr","td","th","h1","h2","h3","h4","h5","h6","blockquote","pre","address"];function Qt(){return{up:D({selector:Zn,closest:tt,predicate:Qn,all:we}),down:D({selector:Pe,predicate:$n}),styles:D({get:ie,getRaw:ue,set:re,remove:ce}),attrs:D({get:G,set:q,remove:K,copyTo:function(e,n){var t=X(e);V(n,t)}}),insert:D({before:Re,after:Te,afterAll:Ae,append:_n,appendAll:Ee,prepend:Oe,wrap:De}),remove:D({unwrap:ke,remove:Ln}),create:D({nu:un.fromTag,clone:function(e){return un.fromDom(e.dom().cloneNode(!1))},text:un.fromText}),query:D({comparePosition:function(e,n){return e.dom().compareDocumentPosition(n.dom())},prevSibling:ye,nextSibling:Ce}),property:D({children:Se,name:nn,parent:be,document:function(e){return e.dom().ownerDocument},isText:rn,isComment:F,isElement:tn,getText:bt,setText:yt,isBoundary:function(e){return!!tn(e)&&("body"===nn(e)||l(Jt,nn(e)))},isEmptyTag:function(e){return!!tn(e)&&l(["br","img","hr","input"],nn(e))},isNonEditable:function(e){return tn(e)&&"false"===G(e,"contenteditable")}}),eq:Pn,is:Wn}}function Zt(e,n,t){var r=e.property().children(n);return C(r,b(e.eq,t)).map(function(e){return{before:D(r.slice(0,e)),after:D(r.slice(e+1))}})}function er(e,n){return b(e.eq,n)}function nr(n,e,t,r){function o(n){return C(n,r).fold(function(){return n},function(e){return n.slice(0,e+1)})}void 0===r&&(r=s);var i=[e].concat(n.up().all(e)),u=[t].concat(n.up().all(t)),c=o(i),a=o(u),l=y(c,function(e){return m(a,er(n,e))});return{firstpath:D(c),secondpath:D(a),shared:D(l)}}function tr(e){return Zn(e,"table")}function rr(c,a,r){function l(n){return function(e){return r!==undefined&&r(e)||Pn(e,n)}}return Pn(c,a)?We.some(mr.create({boxes:We.some([c]),start:c,finish:a})):tr(c).bind(function(u){return tr(a).bind(function(i){if(Pn(u,i))return We.some(mr.create({boxes:Kt(u,c,a),start:c,finish:a}));if(Mn(u,i)){var e=0<(n=Ie(a,"td,th",l(u))).length?n[n.length-1]:a;return We.some(mr.create({boxes:Xt(u,c,u,a,i),start:c,finish:e}))}if(Mn(i,u)){var n,t=0<(n=Ie(c,"td,th",l(i))).length?n[n.length-1]:c;return We.some(mr.create({boxes:Xt(i,c,u,a,i),start:c,finish:t}))}return dr.ancestors(c,a).shared().bind(function(e){return tt(e,"table",r).bind(function(e){var n=Ie(a,"td,th",l(e)),t=0<n.length?n[n.length-1]:a,r=Ie(c,"td,th",l(e)),o=0<r.length?r[r.length-1]:c;return We.some(mr.create({boxes:Xt(e,c,u,a,i),start:o,finish:t}))})})})})}function or(e,n){return Or.cata(n.get(),D([]),o,D([e]))}function ir(e){return{element:D(e),mergable:We.none,unmergable:We.none,selection:D([e])}}var ur=P("left","right"),cr=P("first","second","splits"),ar=function(r,o,e,n){var t=o(r,e);return v(n,function(e,n){var t=o(r,n);return lr(r,e,t)},t)},lr=function(n,e,t){return e.bind(function(e){return t.filter(b(n.eq,e))})},fr={sharedOne:function(e,n,t){return 0<t.length?function(e,n,t,r){return r(e,n,t[0],t.slice(1))}(e,n,t,ar):We.none()},subset:function(n,e,t){var r=nr(n,e,t);return r.shared().bind(function(e){return function(o,i,e,n){var u=o.property().children(i);if(o.eq(i,e[0]))return We.some([e[0]]);if(o.eq(i,n[0]))return We.some([n[0]]);function t(e){var n=E(e),t=C(n,er(o,i)).getOr(-1),r=t<n.length-1?n[t+1]:n[t];return C(u,er(o,r))}var r=t(e),c=t(n);return r.bind(function(r){return c.map(function(e){var n=Math.min(r,e),t=Math.max(r,e);return u.slice(n,t+1)})})}(n,e,r.firstpath(),r.secondpath())})},ancestors:nr,breakToLeft:function(t,r,o){return Zt(t,r,o).map(function(e){var n=t.create().clone(r);return t.insert().appendAll(n,e.before().concat([o])),t.insert().appendAll(r,e.after()),t.insert().before(r,n),ur(n,r)})},breakToRight:function(t,r,e){return Zt(t,r,e).map(function(e){var n=t.create().clone(r);return t.insert().appendAll(n,e.after()),t.insert().after(r,n),ur(r,n)})},breakPath:function(i,e,u,c){var a=function(e,n,o){var t=cr(e,We.none(),o);return u(e)?cr(e,n,o):i.property().parent(e).bind(function(r){return c(i,r,e).map(function(e){var n=[{first:e.left,second:e.right}],t=u(r)?r:e.left();return a(t,We.some(e.right()),o.concat(n))})}).getOr(t)};return a(e,We.none(),[])}},sr=Qt(),dr={sharedOne:function(t,e){return fr.sharedOne(sr,function(e,n){return t(n)},e)},subset:function(e,n){return fr.subset(sr,e,n)},ancestors:function(e,n,t){return fr.ancestors(sr,e,n,t)},breakToLeft:function(e,n){return fr.breakToLeft(sr,e,n)},breakToRight:function(e,n){return fr.breakToRight(sr,e,n)},breakPath:function(e,n,r){return fr.breakPath(sr,e,n,function(e,n,t){return r(n,t)})}},mr={create:$e(["boxes","start","finish"],[])},gr=rr,pr=function(e,n){var t=Pe(e,n);return 0<t.length?We.some(t):We.none()},hr=function(e,n,t,r,o){return function(e,n){return y(e,function(e){return pe(e,n)})}(e,o).bind(function(e){return Yt(e,n,t).bind(function(e){return function(n,t){return Zn(n,"table").bind(function(e){return nt(e,t).bind(function(e){return rr(e,n).bind(function(n){return n.boxes().map(function(e){return{boxes:D(e),start:D(n.start()),finish:D(n.finish())}})})})})}(e,r)})})},vr=function(e,n,r){return nt(e,n).bind(function(t){return nt(e,r).bind(function(n){return dr.sharedOne(tr,[t,n]).map(function(e){return{first:D(t),last:D(n),table:D(e)}})})})},br=function(e,n){return pr(e,n)},wr=function(o,e,n){return vr(o,e,n).bind(function(t){function e(e){return Pn(o,e)}var n=Zn(t.first(),"thead,tfoot,tbody,table",e),r=Zn(t.last(),"thead,tfoot,tbody,table",e);return n.bind(function(n){return r.bind(function(e){return Pn(n,e)?$t(t.table(),t.first(),t.last()):We.none()})})})},yr="data-mce-selected",Cr="data-mce-first-selected",Sr="data-mce-last-selected",xr={selected:D(yr),selectedSelector:D("td[data-mce-selected],th[data-mce-selected]"),attributeSelector:D("[data-mce-selected]"),firstSelected:D(Cr),firstSelectedSelector:D("td[data-mce-first-selected],th[data-mce-first-selected]"),lastSelected:D(Sr),lastSelectedSelector:D("td[data-mce-last-selected],th[data-mce-last-selected]")},Rr=function(u){if(!je(u))throw new Error("cases must be an array");if(0===u.length)throw new Error("there must be at least one case");var c=[],t={};return p(u,function(e,r){var n=Ge(e);if(1!==n.length)throw new Error("one and only one name per case");var o=n[0],i=e[o];if(t[o]!==undefined)throw new Error("duplicate key detected:"+o);if("cata"===o)throw new Error("cannot have a case named cata (sorry)");if(!je(i))throw new Error("case arguments must be an array");c.push(o),t[o]=function(){var e=arguments.length;if(e!==i.length)throw new Error("Wrong number of arguments to case "+o+". Expected "+i.length+" ("+i+"), got "+e);for(var t=new Array(e),n=0;n<t.length;n++)t[n]=arguments[n];return{fold:function(){if(arguments.length!==u.length)throw new Error("Wrong number of arguments to fold. Expected "+u.length+", got "+arguments.length);return arguments[r].apply(null,t)},match:function(e){var n=Ge(e);if(c.length!==n.length)throw new Error("Wrong number of arguments to match. Expected: "+c.join(",")+"\nActual: "+n.join(","));if(!A(c,function(e){return l(n,e)}))throw new Error("Not all branches were specified when using match. Specified: "+n.join(", ")+"\nRequired: "+c.join(", "));return e[o].apply(null,t)},log:function(e){f.console.log(e,{constructors:c,constructor:o,params:t})}}}}),t},Tr=Rr([{none:[]},{multiple:["elements"]},{single:["selection"]}]),Or={cata:function(e,n,t,r){return e.fold(n,t,r)},none:Tr.none,multiple:Tr.multiple,single:Tr.single},Dr=function(t,e){return Or.cata(e.get(),We.none,function(n,e){return 0===n.length?We.none():wr(t,xr.firstSelectedSelector(),xr.lastSelectedSelector()).bind(function(e){return 1<n.length?We.some({bounds:D(e),cells:D(n)}):We.none()})},We.none)},Ar=function(e,n){var t=or(e,n);return 0<t.length&&A(t,function(e){return Y(e,"rowspan")&&1<parseInt(G(e,"rowspan"),10)||Y(e,"colspan")&&1<parseInt(G(e,"colspan"),10)})?We.some(t):We.none()},Er=or,Nr=P("element","clipboard","generators"),kr={noMenu:ir,forMenu:function(e,n,t){return{element:D(t),mergable:D(Dr(n,e)),unmergable:D(Ar(t,e)),selection:D(Er(t,e))}},notCell:function(e){return ir(e)},paste:Nr,pasteRows:function(e,n,t,r,o){return{element:D(t),mergable:We.none,unmergable:We.none,selection:D(Er(t,e)),clipboard:D(r),generators:D(o)}}},Ir={registerEvents:function(c,e,a,l){c.on("BeforeGetContent",function(n){!0===n.selection&&Or.cata(e.get(),T,function(e){n.preventDefault(),function(e){return st.table(e[0]).map(Dt).map(function(e){return[vt(e,xr.attributeSelector())]})}(e).each(function(e){n.content="text"===n.format?function(e){return g(e,function(e){return e.dom().innerText}).join("")}(e):function(n,e){return g(e,function(e){return n.selection.serializer.serialize(e.dom(),{})}).join("")}(c,e)})},T)}),c.on("BeforeSetContent",function(u){!0===u.selection&&!0===u.paste&&We.from(c.dom.getParent(c.selection.getStart(),"th,td")).each(function(e){var i=un.fromDom(e);st.table(i).each(function(n){var e=h(function(e,n){var t=(n||f.document).createElement("div");return t.innerHTML=e,Se(un.fromDom(t))}(u.content),function(e){return"meta"!==nn(e)});if(1===e.length&&"table"===nn(e[0])){u.preventDefault();var t=un.fromDom(c.getDoc()),r=Lt.paste(t),o=kr.paste(i,e[0],r);a.pasteCells(n,o).each(function(e){c.selection.setRng(e),c.focus(),l.clear(n)})}})})})}};function Br(r,o){function e(e){var n=o(e);if(n<=0||null===n){var t=ie(e,r);return parseFloat(t)||0}return n}function i(o,e){return w(e,function(e,n){var t=ie(o,n),r=t===undefined?0:parseInt(t,10);return isNaN(r)?e:e+r},0)}return{set:function(e,n){if(!Fe(n)&&!n.match(/^[0-9]+$/))throw new Error(r+".set accepts only positive integer values. Value was "+n);var t=e.dom();Z(t)&&(t.style[r]=n+"px")},get:e,getOuter:e,aggregate:i,max:function(e,n,t){var r=i(e,t);return r<n?n-r:0}}}function Pr(e){return eo.get(e)}function Mr(e){return eo.getOuter(e)}function Wr(e){return no.get(e)}function _r(e){return no.getOuter(e)}function Lr(e,n,t){return function(e,n){var t=parseFloat(e);return isNaN(t)?n:t}(ie(e,n),t)}function jr(e,n){re(e,"height",n+"px")}function zr(e,n,t,r){var o=parseInt(e,10);return function(e,n){return $(e,n,e.length-n.length)}(e,"%")&&"table"!==nn(n)?function(e,t,r,n){var o=st.table(e).map(function(e){var n=r(e);return Math.floor(t/100*n)}).getOr(t);return n(e,o),o}(n,o,t,r):o}function Hr(e){var n=function(e){return ue(e,"height").getOrThunk(function(){return ro(e)+"px"})}(e);return n?zr(n,e,Pr,jr):Pr(e)}function Fr(e){return ue(e,"width").fold(function(){return We.from(G(e,"width"))},function(e){return We.some(e)})}function Ur(e,n){return e/n.pixelWidth()*100}function qr(e,n){return e!==undefined?e:n!==undefined?n:0}function Vr(e){var n=e.dom().ownerDocument,t=n.body,r=n.defaultView,o=n.documentElement;if(t===e.dom())return so(t.offsetLeft,t.offsetTop);var i=qr(r.pageYOffset,o.scrollTop),u=qr(r.pageXOffset,o.scrollLeft),c=qr(o.clientTop,t.clientTop),a=qr(o.clientLeft,t.clientLeft);return mo(e).translate(u-a,i-c)}function Gr(e){return Vr(e).left()+_r(e)}function Yr(e){return Vr(e).left()}function Kr(e,n){return po(e,Yr(n))}function Xr(e,n){return po(e,Gr(n))}function $r(e){return Vr(e).top()}function Jr(e,n){return go(e,$r(n))}function Qr(e,n){return go(e,$r(n)+Mr(n))}function Zr(t,n,r){if(0===r.length)return[];var e=g(r.slice(1),function(e,n){return e.map(function(e){return t(n,e)})}),o=r[r.length-1].map(function(e){return n(r.length-1,e)});return e.concat([o])}var eo=Br("height",function(e){var n=e.dom();return ne(e)?n.getBoundingClientRect().height:n.offsetHeight}),no=Br("width",function(e){return e.dom().offsetWidth}),to=ge(),ro=function(e){return to.browser.isIE()||to.browser.isEdge()?function(e){var n=Lr(e,"padding-top",0),t=Lr(e,"padding-bottom",0),r=Lr(e,"border-top-width",0),o=Lr(e,"border-bottom-width",0),i=e.dom().getBoundingClientRect().height;return"border-box"===ie(e,"box-sizing")?i:i-n-t-(r+o)}(e):Lr(e,"height",Pr(e))},oo=/(\d+(\.\d+)?)(\w|%)*/,io=/(\d+(\.\d+)?)%/,uo=/(\d+(\.\d+)?)px|em/,co=function(e,n){return Y(e,n)?parseInt(G(e,n),10):1},ao={percentageBasedSizeRegex:D(io),pixelBasedSizeRegex:D(uo),setPixelWidth:function(e,n){re(e,"width",n+"px")},setPercentageWidth:function(e,n){re(e,"width",n+"%")},setHeight:jr,getPixelWidth:function(n,t){return Fr(n).fold(function(){return Wr(n)},function(e){return function(e,n,t){var r=uo.exec(n);if(null!==r)return parseInt(r[1],10);var o=io.exec(n);return null===o?Wr(e):function(e,n){return e/100*n.pixelWidth()}(parseFloat(o[1]),t)}(n,e,t)})},getPercentageWidth:function(n,t){return Fr(n).fold(function(){var e=Wr(n);return Ur(e,t)},function(e){return function(e,n,t){var r=io.exec(n);if(null!==r)return parseFloat(r[1]);var o=Wr(e);return Ur(o,t)}(n,e,t)})},getGenericWidth:function(e){return Fr(e).bind(function(e){var n=oo.exec(e);return null!==n?We.some({width:D(parseFloat(n[1])),unit:D(n[3])}):We.none()})},setGenericWidth:function(e,n,t){re(e,"width",n+t)},getHeight:function(e){return function(e,n,t){return t(e)/co(e,n)}(e,"rowspan",Hr)},getRawWidth:Fr},lo=function(t,r){ao.getGenericWidth(t).each(function(e){var n=e.width()/2;ao.setGenericWidth(t,n,e.unit()),ao.setGenericWidth(r,n,e.unit())})},fo=function(t,r){return{left:D(t),top:D(r),translate:function(e,n){return fo(t+e,r+n)}}},so=fo,mo=function(e){var n=e.dom(),t=n.ownerDocument.body;return t===n?so(t.offsetLeft,t.offsetTop):ne(e)?function(e){var n=e.getBoundingClientRect();return so(n.left,n.top)}(n):so(0,0)},go=P("row","y"),po=P("col","x"),ho={height:{delta:o,positions:function(e){return Zr(Jr,Qr,e)},edge:$r},rtl:{delta:function(e){return-e},edge:Gr,positions:function(e){return Zr(Xr,Kr,e)}},ltr:{delta:o,edge:Yr,positions:function(e){return Zr(Kr,Xr,e)}}},vo={ltr:ho.ltr,rtl:ho.rtl};function bo(n){function t(e){return n(e).isRtl()?vo.rtl:vo.ltr}return{delta:function(e,n){return t(n).delta(e,n)},edge:function(e){return t(e).edge(e)},positions:function(e,n){return t(n).positions(e,n)}}}function wo(e){for(var n=[],t=function(e){n.push(e)},r=0;r<e.length;r++)e[r].each(t);return n}function yo(e,n,t,r){t===r?K(e,n):q(e,n,t)}function Co(e,n){var t=G(e,n);return t===undefined||""===t?[]:t.split(" ")}function So(e){return e.dom().classList!==undefined}function xo(e,n){return function(e,n,t){var r=Co(e,n).concat([t]);return q(e,n,r.join(" ")),!0}(e,"class",n)}function Ro(e,n){return function(e,n,t){var r=h(Co(e,n),function(e){return e!==t});return 0<r.length?q(e,n,r.join(" ")):K(e,n),!1}(e,"class",n)}function To(e,n){So(e)?e.dom().classList.add(n):xo(e,n)}function Oo(e){0===(So(e)?e.dom().classList:function(e){return Co(e,"class")}(e)).length&&K(e,"class")}function Do(e,n){return So(e)&&e.dom().classList.contains(n)}function Ao(e,n){for(var t=[],r=e;r<n;r++)t.push(r);return t}function Eo(n,t){if(t<0||t>=n.length-1)return We.none();var e=n[t].fold(function(){var e=E(n.slice(0,t));return N(e,function(e,n){return e.map(function(e){return{value:e,delta:n+1}})})},function(e){return We.some({value:e,delta:0})}),r=n[t+1].fold(function(){var e=n.slice(t+1);return N(e,function(e,n){return e.map(function(e){return{value:e,delta:n+1}})})},function(e){return We.some({value:e,delta:1})});return e.bind(function(t){return r.map(function(e){var n=e.delta+t.delta;return Math.abs(e.value-t.value)/n})})}function No(e){var n=e.replace(/\./g,"-");return{resolve:function(e){return n+"-"+e}}}function ko(e){var n=Pe(e.parent(),"."+iu);p(n,Ln)}function Io(t,e,r){var o=t.origin();p(e,function(e,n){e.each(function(e){var n=r(o,e);To(n,iu),_n(t.parent(),n)})})}function Bo(e,n,t,r,o,i){var u=Vr(n);!function(e,n,r,o){Io(e,n,function(e,n){var t=ou(n.row(),r.left()-e.left(),n.y()-e.top(),o,7);return To(t,uu),t})}(e,0<t.length?o.positions(t,n):[],u,_r(n)),function(e,n,r,o){Io(e,n,function(e,n){var t=ru(n.col(),n.x()-e.left(),r.top()-e.top(),7,o);return To(t,cu),t})}(e,0<r.length?i.positions(r,n):[],u,Mr(n))}function Po(e,n){var t=Pe(e.parent(),"."+iu);p(t,n)}function Mo(e,n){return e.cells()[n]}function Wo(e,n){if(0===e.length)return 0;var t=e[0];return C(e,function(e){return!n(t.element(),e.element())}).fold(function(){return e.length},function(e){return e})}function _o(e,t){return g(e,function(e){var n=function(e){return N(e,function(e){return be(e.element()).map(function(e){var n=be(e).isNone();return Vn(e,n)})}).getOrThunk(function(){return Vn(t.row(),!0)})}(e.details());return Gn(n.element(),e.details(),e.section(),n.isNew())})}function Lo(e,n){var t=vu(e,Pn);return _o(t,n)}function jo(e,n){var t=S(g(e.all(),function(e){return e.cells()}));return y(t,function(e){return Pn(n,e.element())})}function zo(c,a,l,f,s){return function(t,r,e,o,i){var n=dt(r),u=pt.generate(n);return a(u,e).map(function(e){var n=function(e,n){return bu(e,n,!1)}(u,o),t=c(n,e,Pn,s(o)),r=Lo(t.grid(),o);return{grid:D(r),cursor:t.cursor}}).fold(function(){return We.none()},function(e){var n=Ji(r,e.grid());return l(r,e.grid(),i),f(r),au(t,r,ho.height,i),We.some({cursor:e.cursor,newRows:n.newRows,newCells:n.newCells})})}}function Ho(n,e){return st.cell(e.element()).bind(function(e){return jo(n,e)})}function Fo(n,e){var t=g(e.selection(),function(e){return st.cell(e).bind(function(e){return jo(n,e)})}),r=wo(t);return 0<r.length?We.some({cells:r,generators:e.generators,clipboard:e.clipboard}):We.none()}function Uo(n,e){var t=g(e.selection(),function(e){return st.cell(e).bind(function(e){return jo(n,e)})}),r=wo(t);return 0<r.length?We.some(r):We.none()}function qo(e,n){return g(e,function(){return Vn(n.cell(),!0)})}function Vo(n,e,t){return n.concat(function(e,n){for(var t=[],r=0;r<e;r++)t.push(n(r));return t}(e,function(e){return pu.setCells(n[n.length-1],qo(n[n.length-1].cells(),t))}))}function Go(e,n,t){return g(e,function(e){return pu.setCells(e,e.cells().concat(qo(Ao(0,n),t)))})}function Yo(e,t,r,n){return g(e,function(e){return pu.mapCells(e,function(e){return function(n){return m(t,function(e){return r(n.element(),e.element())})}(e)?Vn(n(e.element(),r),!0):e})})}function Ko(e,n,t,r){return pu.getCellElement(e[n],t)!==undefined&&0<n&&r(pu.getCellElement(e[n-1],t),pu.getCellElement(e[n],t))}function Xo(e,n,t){return 0<n&&t(pu.getCellElement(e,n-1),pu.getCellElement(e,n))}function $o(e,n){return Y(e,n)&&1<parseInt(G(e,n),10)}function Jo(e,n,t){return ue(e,n).fold(function(){return t(e)+"px"},function(e){return e})}function Qo(e,n){return Jo(e,"width",function(e){return ao.getPixelWidth(e,n)})}function Zo(e){return Jo(e,"height",ao.getHeight)}function ei(e,n,t,r,o){var i=eu(e),u=g(i,function(e){return e.map(n.edge)});return g(i,function(e,n){return e.filter(d(Hu.hasColspan)).fold(function(){var e=Eo(u,n);return r(e)},function(e){return t(e,o)})})}function ni(e){return e.map(function(e){return e+"px"}).getOr("")}function ti(e,n,t,r){var o=nu(e),i=g(o,function(e){return e.map(n.edge)});return g(o,function(e,n){return e.filter(d(Hu.hasRowspan)).fold(function(){var e=Eo(i,n);return r(e)},function(e){return t(e)})})}function ri(e,n,t){for(var r=0,o=e;o<n;o++)r+=t[o]!==undefined?t[o]:0;return r}function oi(e){var n=o;return{width:D(e),pixelWidth:D(e),getWidths:Fu.getPixelWidths,getCellDelta:n,singleColumnWidth:function(e,n){return[Math.max(Hu.minWidth(),e+n)-e]},minCellWidth:Hu.minWidth,setElementWidth:ao.setPixelWidth,setTableWidth:function(e,n,t){var r=v(n,function(e,n){return e+n},0);ao.setPixelWidth(e,r)}}}function ii(e,n){var t=ao.percentageBasedSizeRegex().exec(n);if(null!==t)return function(e,n){var o=parseFloat(e),t=Wr(n);return{width:D(o),pixelWidth:D(t),getWidths:Fu.getPercentageWidths,getCellDelta:function(e){return e/t*100},singleColumnWidth:function(e,n){return[100-e]},minCellWidth:function(){return Hu.minWidth()/t*100},setElementWidth:ao.setPercentageWidth,setTableWidth:function(e,n,t){var r=t/100*o;ao.setPercentageWidth(e,o+r)}}}(t[1],e);var r=ao.pixelBasedSizeRegex().exec(n);if(null!==r){var o=parseInt(r[1],10);return oi(o)}var i=Wr(e);return oi(i)}function ui(e){return pt.generate(e)}function ci(e){var n=dt(e);return ui(n)}function ai(n,e){var t=h(e,function(e){return!l(n,e)});0<t.length&&_(t)}function li(e){return function(e,n){return $u(e,n,{validate:He,label:"function"})}(ai,e)}function fi(e){var n=Y(e,"colspan")?parseInt(G(e,"colspan"),10):1,t=Y(e,"rowspan")?parseInt(G(e,"rowspan"),10):1;return{element:D(e),colspan:D(n),rowspan:D(t)}}function si(e,n){var t=e.property().name(n);return l(nc,t)}function di(e,n){return l(["br","img","hr","input"],e.property().name(n))}function mi(e){0===st.cells(e).length&&Ln(e)}function gi(e,n,t){return sc(e,n,t).orThunk(function(){return sc(e,0,0)})}function pi(e,n,t){return fc(e,sc(e,n,t))}function hi(e){return w(e,function(e,n){return m(e,function(e){return e.row()===n.row()})?e:e.concat([n])},[]).sort(function(e,n){return e.row()-n.row()})}function vi(e){return w(e,function(e,n){return m(e,function(e){return e.column()===n.column()})?e:e.concat([n])},[]).sort(function(e,n){return e.column()-n.column()})}function bi(e,n,t){var r=mt(e,t),o=pt.generate(r);return bu(o,n,!0)}function wi(e){return e.getBoundingClientRect().width}function yi(e){return e.getBoundingClientRect().height}function Ci(e){return/^[0-9]+$/.test(e)&&(e+="px"),e}function Si(e){var n=Pe(e,"td[data-mce-style],th[data-mce-style]");K(e,"data-mce-style"),p(n,function(e){K(e,"data-mce-style")})}function xi(e){return e.getParam("table_default_attributes",yc,"object")}function Ri(e){return e.getParam("table_default_styles",wc,"object")}function Ti(e){return e.getParam("table_cell_advtab",!0,"boolean")}function Oi(e){return e.getParam("table_row_advtab",!0,"boolean")}function Di(e){return e.getParam("table_advtab",!0,"boolean")}function Ai(e){return e.getParam("table_style_by_css",!1,"boolean")}function Ei(e){return e.getParam("table_class_list",[],"array")}function Ni(e){return!1===e.getParam("table_responsive_width")}function ki(e,n){return e.fire("newrow",{node:n})}function Ii(e,n){return e.fire("newcell",{node:n})}function Bi(e,n,t,r){e.fire("ObjectResizeStart",{target:n,width:t,height:r})}function Pi(e,n,t,r){e.fire("ObjectResized",{target:n,width:t,height:r})}function Mi(n,e){function t(e){return Q(e,"rgb")?n.toHex(e):e}return{borderwidth:ue(un.fromDom(e),"border-width").getOr(""),borderstyle:ue(un.fromDom(e),"border-style").getOr(""),bordercolor:ue(un.fromDom(e),"border-color").map(t).getOr(""),backgroundcolor:ue(un.fromDom(e),"background-color").map(t).getOr("")}}function Wi(e,n,t,r,o){var i={};return Dc.each(e.split(" "),function(e){r.formatter.matchNode(o,n+e)&&(i[t]=e)}),i[t]||(i[t]=""),i}function _i(e,n){e.setAttrib("scope",n.scope),e.setAttrib("class",n["class"]),e.setStyle("width",Ci(n.width)),e.setStyle("height",Ci(n.height))}function Li(e,n){e.setStyle("background-color",n.backgroundcolor),e.setStyle("border-color",n.bordercolor),e.setStyle("border-style",n.borderstyle),e.setStyle("border-width",Ci(n.borderwidth))}function ji(e,n,t){var r=e.dom,o=t.celltype&&n[0].nodeName.toLowerCase()!==t.celltype?r.rename(n[0],t.celltype):n[0],i=qc.normal(r,o);_i(i,t),Ti(e)&&Li(i,t),Nc(e,o),kc(e,o),t.halign&&Ac(e,o,t.halign),t.valign&&Ec(e,o,t.valign)}function zi(t,e,r){var o=t.dom;Dc.each(e,function(e){r.celltype&&e.nodeName.toLowerCase()!==r.celltype&&(e=o.rename(e,r.celltype));var n=qc.ifTruthy(o,e);_i(n,r),Ti(t)&&Li(n,r),r.halign&&Ac(t,e,r.halign),r.valign&&Ec(t,e,r.valign)})}function Hi(e,n,t){var r=t.getData();t.close(),e.undoManager.transact(function(){(1===n.length?ji:zi)(e,n,r),e.focus()})}function Fi(t,e,r,n){var o=t.dom,i=n.getData();n.close();var u=1===e.length?qc.normal:qc.ifTruthy;t.undoManager.transact(function(){Dc.each(e,function(e){i.type!==e.parentNode.nodeName.toLowerCase()&&function(e,n,t){var r=e.getParent(n,"table"),o=n.parentNode,i=e.select(t,r)[0];i||(i=e.create(t),r.firstChild?"CAPTION"===r.firstChild.nodeName?e.insertAfter(i,r.firstChild):r.insertBefore(i,r.firstChild):r.appendChild(i)),"tbody"===t&&"THEAD"===o.nodeName&&i.firstChild?i.insertBefore(n,i.firstChild):i.appendChild(n),o.hasChildNodes()||e.remove(o)}(t.dom,e,i.type);var n=u(o,e);n.setAttrib("scope",i.scope),n.setAttrib("class",i["class"]),n.setStyle("height",Ci(i.height)),Oi(t)&&function(e,n){e.setStyle("background-color",n.backgroundcolor),e.setStyle("border-color",n.bordercolor),e.setStyle("border-style",n.borderstyle)}(n,i),i.align!==r.align&&(Nc(t,e),Ac(t,e,i.align))}),t.focus()})}function Ui(e,n,t,r,o){void 0===o&&(o=$c);var i=un.fromTag("table");oe(i,o.styles),V(i,o.attributes);var u=un.fromTag("tbody");_n(i,u);for(var c=[],a=0;a<e;a++){for(var l=un.fromTag("tr"),f=0;f<n;f++){var s=a<t||f<r?un.fromTag("th"):un.fromTag("td");f<r&&q(s,"scope","row"),a<t&&q(s,"scope","col"),_n(s,un.fromTag("br")),o.percentages&&re(s,"width",100/n+"%"),_n(l,s)}c.push(l)}return Ee(u,c),i}function qi(e,n){e.selection.select(n.dom(),!0),e.selection.collapse(!0)}function Vi(t,r,e){var o,i=t.dom,u=e.getData();e.close(),""===u["class"]&&delete u["class"],t.undoManager.transact(function(){if(!r){var e=parseInt(u.cols,10)||1,n=parseInt(u.rows,10)||1;r=Jc(t,e,n)}!function(e,n,t){var r=e.dom,o={},i={};if(o["class"]=t["class"],i.height=Ci(t.height),r.getAttrib(n,"width")&&!Ai(e)?o.width=function(e){return e?e.replace(/px$/,""):""}(t.width):i.width=Ci(t.width),Ai(e)?(i["border-width"]=Ci(t.border),i["border-spacing"]=Ci(t.cellspacing)):(o.border=t.border,o.cellpadding=t.cellpadding,o.cellspacing=t.cellspacing),Ai(e)&&n.children)for(var u=0;u<n.children.length;u++)Zc(r,n.children[u],{"border-width":Ci(t.border),padding:Ci(t.cellpadding)}),Di(e)&&Zc(r,n.children[u],{"border-color":t.bordercolor});Di(e)&&(i["background-color"]=t.backgroundcolor,i["border-color"]=t.bordercolor,i["border-style"]=t.borderstyle),o.style=r.serializeStyle(sn(sn({},Ri(e)),i)),r.setAttribs(n,sn(sn({},xi(e)),o))}(t,r,u),(o=i.select("caption",r)[0])&&!u.caption&&i.remove(o),!o&&u.caption&&((o=i.create("caption")).innerHTML=Xc.ie?"\xa0":'<br data-mce-bogus="1"/>',r.insertBefore(o,r.firstChild)),""===u.align?Nc(t,r):Ac(t,r,u.align),t.focus(),t.addVisual()})}function Gi(n){return function(e){return We.from(e.dom.getParent(e.selection.getStart(),n)).map(un.fromDom)}}function Yi(e){function n(){e.stopPropagation()}function t(){e.preventDefault()}var r=un.fromDom(e.target),o=O(t,n);return function(e,n,t,r,o,i,u){return{target:D(e),x:D(n),y:D(t),stop:r,prevent:o,kill:i,raw:D(u)}}(r,e.clientX,e.clientY,n,t,o,e)}function Ki(e,n,t,r,o){var i=function(n,t){return function(e){n(e)&&t(Yi(e))}}(t,r);return e.dom().addEventListener(n,i,o),{unbind:b(la,e,n,i,o)}}function Xi(e,n,t){return function(e,n,t,r){return Ki(e,n,t,r,!1)}(e,n,fa,t)}var $i=function(e){var n=dt(e);return pt.generate(n).grid()},Ji=function(o,e){function n(e,n){0<e.length?function(e,n){var t=et(o,n).getOrThunk(function(){var e=un.fromTag(n,ve(o).dom());return _n(o,e),e});Ne(t);var r=g(e,function(e){e.isNew()&&i.push(e.element());var n=e.element();return Ne(n),p(e.cells(),function(e){e.isNew()&&u.push(e.element()),yo(e.element(),"colspan",e.colspan(),1),yo(e.element(),"rowspan",e.rowspan(),1),_n(n,e.element())}),n});Ee(t,r)}(e,n):function(e){et(o,e).each(Ln)}(n)}var i=[],u=[],t=[],r=[],c=[];return p(e,function(e){switch(e.section()){case"thead":t.push(e);break;case"tbody":r.push(e);break;case"tfoot":c.push(e)}}),n(t,"thead"),n(r,"tbody"),n(c,"tfoot"),{newRows:D(i),newCells:D(u)}},Qi=function(e){return g(e,function(e){var t=Ot(e.element());return p(e.cells(),function(e){var n=Dt(e.element());yo(n,"colspan",e.colspan(),1),yo(n,"rowspan",e.rowspan(),1),_n(t,n)}),t})},Zi=function(e,n,t){var r=e();return y(r,n).orThunk(function(){return We.from(r[0]).orThunk(t)}).map(function(e){return e.element()})},eu=function(t){var e=t.grid(),n=Ao(0,e.columns()),r=Ao(0,e.rows());return g(n,function(n){return Zi(function(){return x(r,function(e){return pt.getAt(t,e,n).filter(function(e){return e.column()===n}).fold(D([]),function(e){return[e]})})},function(e){return 1===e.colspan()},function(){return pt.getAt(t,0,n)})})},nu=function(t){var e=t.grid(),n=Ao(0,e.rows()),r=Ao(0,e.columns());return g(n,function(n){return Zi(function(){return x(r,function(e){return pt.getAt(t,n,e).filter(function(e){return e.row()===n}).fold(D([]),function(e){return[e]})})},function(e){return 1===e.rowspan()},function(){return pt.getAt(t,n,0)})})},tu={resolve:No("ephox-snooker").resolve},ru=function(e,n,t,r,o){var i=un.fromTag("div");return oe(i,{position:"absolute",left:n-r/2+"px",top:t+"px",height:o+"px",width:r+"px"}),V(i,{"data-column":e,role:"presentation"}),i},ou=function(e,n,t,r,o){var i=un.fromTag("div");return oe(i,{position:"absolute",left:n+"px",top:t-o/2+"px",height:o+"px",width:r+"px"}),V(i,{"data-row":e,role:"presentation"}),i},iu=tu.resolve("resizer-bar"),uu=tu.resolve("resizer-rows"),cu=tu.resolve("resizer-cols"),au=function(e,n,t,r){ko(e);var o=dt(n),i=pt.generate(o),u=nu(i),c=eu(i);Bo(e,n,u,c,t,r)},lu=function(e){Po(e,function(e){re(e,"display","none")})},fu=function(e){Po(e,function(e){re(e,"display","block")})},su=ko,du=function(e){return Do(e,uu)},mu=function(e){return Do(e,cu)},gu=function(e,n){return Yn(n,e.section())},pu={addCell:function(e,n,t){var r=e.cells(),o=r.slice(0,n),i=r.slice(n),u=o.concat([t]).concat(i);return gu(e,u)},setCells:gu,mutateCell:function(e,n,t){e.cells()[n]=t},getCell:Mo,getCellElement:function(e,n){return Mo(e,n).element()},mapCells:function(e,n){var t=e.cells(),r=g(t,n);return Yn(r,e.section())},cellLength:function(e){return e.cells().length}},hu=function(e,n,t,r){var o=function(e,n){return e[n]}(e,n).cells().slice(t),i=Wo(o,r),u=function(e,n){return g(e,function(e){return pu.getCell(e,n)})}(e,t).slice(n),c=Wo(u,r);return{colspan:D(i),rowspan:D(c)}},vu=function(o,i){var u=g(o,function(e,n){return g(e.cells(),function(e,n){return!1})});return g(o,function(e,r){var n=x(e.cells(),function(e,n){if(!1!==u[r][n])return[];var t=hu(o,r,n,i);return function(e,n,t,r){for(var o=e;o<e+t;o++)for(var i=n;i<n+r;i++)u[o][i]=!0}(r,n,t.rowspan(),t.colspan()),[Fn(e.element(),t.rowspan(),t.colspan(),e.isNew())]});return Kn(n,e.section())})},bu=function(e,n,t){for(var r=[],o=0;o<e.grid().rows();o++){for(var i=[],u=0;u<e.grid().columns();u++){var c=pt.getAt(e,o,u).map(function(e){return Vn(e.element(),t)}).getOrThunk(function(){return Vn(n.gap(),!0)});i.push(c)}var a=Yn(i,e.all()[o].section());r.push(a)}return r},wu=function(t){return{is:function(e){return t===e},isValue:i,isError:s,getOr:D(t),getOrThunk:D(t),getOrDie:D(t),or:function(e){return wu(t)},orThunk:function(e){return wu(t)},fold:function(e,n){return n(t)},map:function(e){return wu(e(t))},mapError:function(e){return wu(t)},each:function(e){e(t)},bind:function(e){return e(t)},exists:function(e){return e(t)},forall:function(e){return e(t)},toOption:function(){return We.some(t)}}},yu=function(t){return{is:s,isValue:s,isError:i,getOr:o,getOrThunk:function(e){return e()},getOrDie:function(){return function(e){return function(){throw new Error(e)}}(String(t))()},or:function(e){return e},orThunk:function(e){return e()},fold:function(e,n){return e(t)},map:function(e){return yu(t)},mapError:function(e){return yu(e(t))},each:T,bind:function(e){return yu(t)},exists:s,forall:i,toOption:We.none}},Cu={value:wu,error:yu,fromOption:function(e,n){return e.fold(function(){return yu(n)},wu)}},Su=function(e,n,t){if(e.row()>=n.length||e.column()>pu.cellLength(n[0]))return Cu.error("invalid start address out of table bounds, row: "+e.row()+", column: "+e.column());var r=n.slice(e.row()),o=r[0].cells().slice(e.column()),i=pu.cellLength(t[0]),u=t.length;return Cu.value({rowDelta:D(r.length-u),colDelta:D(o.length-i)})},xu=function(e,n){var t=pu.cellLength(e[0]),r=pu.cellLength(n[0]);return{rowDelta:D(0),colDelta:D(t-r)}},Ru=function(e,n,t){var r=n.colDelta()<0?Go:o;return(n.rowDelta()<0?Vo:o)(r(e,Math.abs(n.colDelta()),t),Math.abs(n.rowDelta()),t)},Tu=function(e,n,t,r){if(0===e.length)return e;for(var o=n.startRow();o<=n.finishRow();o++)for(var i=n.startCol();i<=n.finishCol();i++)pu.mutateCell(e[o],i,Vn(r(),!1));return e},Ou=function(e,n,t,r){for(var o=!0,i=0;i<e.length;i++)for(var u=0;u<pu.cellLength(e[0]);u++){var c=t(pu.getCellElement(e[i],u),n);!0===c&&!1===o?pu.mutateCell(e[i],u,Vn(r(),!0)):!0===c&&(o=!1)}return e},Du=function(i,t,u,c){if(0<t&&t<i.length){var e=function(e,t){return w(e,function(e,n){return m(e,function(e){return t(e.element(),n.element())})?e:e.concat([n])},[])}(i[t-1].cells(),u);p(e,function(r){for(var o=We.none(),e=function(t){for(var e=function(n){var e=i[t].cells()[n];u(e.element(),r.element())&&(o.isNone()&&(o=We.some(c())),o.each(function(e){pu.mutateCell(i[t],n,Vn(e,!0))}))},n=0;n<pu.cellLength(i[0]);n++)e(n)},n=t;n<i.length;n++)e(n)})}return i},Au=function(t,r,o,i,u){return Su(t,r,o).map(function(e){var n=Ru(r,e,i);return function(e,n,t,r,o){for(var i,u,c,a,l,f=e.row(),s=e.column(),d=f+t.length,m=s+pu.cellLength(t[0]),g=f;g<d;g++)for(var p=s;p<m;p++){i=n,u=g,c=p,l=a=void 0,a=b(o,pu.getCell(i[u],c).element()),l=i[u],1<i.length&&1<pu.cellLength(l)&&(0<c&&a(pu.getCellElement(l,c-1))||c<l.cells().length-1&&a(pu.getCellElement(l,c+1))||0<u&&a(pu.getCellElement(i[u-1],c))||u<i.length-1&&a(pu.getCellElement(i[u+1],c)))&&Ou(n,pu.getCellElement(n[g],p),o,r.cell);var h=pu.getCellElement(t[g-f],p-s),v=r.replace(h);pu.mutateCell(n[g],p,Vn(v,!0))}return n}(t,n,o,i,u)})},Eu=function(e,n,t,r,o){Du(n,e,o,r.cell);var i=xu(t,n),u=Ru(t,i,r),c=xu(n,u),a=Ru(n,c,r);return a.slice(0,e).concat(u).concat(a.slice(e,a.length))},Nu=function(t,r,e,o,i){var n=t.slice(0,r),u=t.slice(r),c=pu.mapCells(t[e],function(e,n){return 0<r&&r<t.length&&o(pu.getCellElement(t[r-1],n),pu.getCellElement(t[r],n))?pu.getCell(t[r],n):Vn(i(e.element(),o),!0)});return n.concat([c]).concat(u)},ku=function(e,t,r,o,i){return g(e,function(e){var n=0<t&&t<pu.cellLength(e)&&o(pu.getCellElement(e,t-1),pu.getCellElement(e,t))?pu.getCell(e,t):Vn(i(pu.getCellElement(e,r),o),!0);return pu.addCell(e,t,n)})},Iu=function(e,r,o,i,u){var c=o+1;return g(e,function(e,n){var t=n===r?Vn(u(pu.getCellElement(e,o),i),!0):pu.getCell(e,o);return pu.addCell(e,c,t)})},Bu=function(e,n,t,r,o){var i=n+1,u=e.slice(0,i),c=e.slice(i),a=pu.mapCells(e[n],function(e,n){return n===t?Vn(o(e.element(),r),!0):e});return u.concat([a]).concat(c)},Pu=function(e,n,t){return e.slice(0,n).concat(e.slice(t+1))},Mu=function(e,t,r){var n=g(e,function(e){var n=e.cells().slice(0,t).concat(e.cells().slice(r+1));return Yn(n,e.section())});return h(n,function(e){return 0<e.cells().length})},Wu=function(t,r,o,e){var n=x(t,function(e,n){return Ko(t,n,r,o)||Xo(e,r,o)?[]:[pu.getCell(e,r)]});return Yo(t,n,o,e)},_u=function(t,r,o,e){var i=t[r],n=x(i.cells(),function(e,n){return Ko(t,r,n,o)||Xo(i,n,o)?[]:[e]});return Yo(t,n,o,e)},Lu=Rr([{none:[]},{only:["index"]},{left:["index","next"]},{middle:["prev","index","next"]},{right:["prev","index"]}]),ju=sn({},Lu),zu=function(e,n,i,u){function c(e){return g(e,D(0))}function r(e,n){if(0<=i){var t=Math.max(u.minCellWidth(),a[n]-i);return c(a.slice(0,e)).concat([i,t-a[n]]).concat(c(a.slice(n+1)))}var r=Math.max(u.minCellWidth(),a[e]+i),o=a[e]-r;return c(a.slice(0,e)).concat([r-a[e],o]).concat(c(a.slice(n+1)))}var a=e.slice(0),t=function(e,n){return 0===e.length?ju.none():1===e.length?ju.only(0):0===n?ju.left(0,1):n===e.length-1?ju.right(n-1,n):0<n&&n<e.length-1?ju.middle(n-1,n,n+1):ju.none()}(e,n),o=D(c(a)),l=r;return t.fold(o,function(e){return u.singleColumnWidth(a[e],i)},l,function(e,n,t){return r(n,t)},function(e,n){if(0<=i)return c(a.slice(0,n)).concat([i]);var t=Math.max(u.minCellWidth(),a[n]+i);return c(a.slice(0,n)).concat([t-a[n]])})},Hu={hasColspan:function(e){return $o(e,"colspan")},hasRowspan:function(e){return $o(e,"rowspan")},minWidth:D(10),minHeight:D(10),getInt:function(e,n){return parseInt(ie(e,n),10)}},Fu={getRawWidths:function(e,n,t){return ei(e,n,Qo,ni,t)},getPixelWidths:function(e,n,t){return ei(e,n,ao.getPixelWidth,function(e){return e.getOrThunk(t.minCellWidth)},t)},getPercentageWidths:function(e,n,t){return ei(e,n,ao.getPercentageWidth,function(e){return e.fold(function(){return t.minCellWidth()},function(e){return e/t.pixelWidth()*100})},t)},getPixelHeights:function(e,n){return ti(e,n,ao.getHeight,function(e){return e.getOrThunk(Hu.minHeight)})},getRawHeights:function(e,n){return ti(e,n,Zo,ni)}},Uu=function(e,t){var n=pt.justCells(e);return g(n,function(e){var n=ri(e.column(),e.column()+e.colspan(),t);return{element:e.element,width:D(n),colspan:e.colspan}})},qu=function(e,t){var n=pt.justCells(e);return g(n,function(e){var n=ri(e.row(),e.row()+e.rowspan(),t);return{element:e.element,height:D(n),rowspan:e.rowspan}})},Vu=function(e,t){return g(e.all(),function(e,n){return{element:e.element,height:D(t[n])}})},Gu=function(n){return ao.getRawWidth(n).fold(function(){var e=Wr(n);return oi(e)},function(e){return ii(n,e)})},Yu=function(e,n,t,r){var o=Gu(e),i=o.getCellDelta(n),u=ci(e),c=o.getWidths(u,r,o),a=zu(c,t,i,o),l=g(a,function(e,n){return e+c[n]}),f=Uu(u,l);p(f,function(e){o.setElementWidth(e.element(),e.width())}),t===u.grid().columns()-1&&o.setTableWidth(e,l,i)},Ku=function(e,t,r,n){var o=ci(e),i=Fu.getPixelHeights(o,n),u=g(i,function(e,n){return r===n?Math.max(t+e,Hu.minHeight()):e}),c=qu(o,u),a=Vu(o,u);p(a,function(e){ao.setHeight(e.element(),e.height())}),p(c,function(e){ao.setHeight(e.element(),e.height())});var l=function(e){return v(e,function(e,n){return e+n},0)}(u);ao.setHeight(e,l)},Xu=function(e,n,t){var r=Gu(e),o=ui(n),i=r.getWidths(o,t,r),u=Uu(o,i);p(u,function(e){r.setElementWidth(e.element(),e.width())}),0<u.length&&r.setTableWidth(e,i,r.getCellDelta(0))},$u=function(r,o,i){if(0===o.length)throw new Error("You must specify at least one required field.");return L("required",o),j(o),function(n){var t=Ge(n);A(o,function(e){return l(t,e)})||W(o,t),r(o,t);var e=h(o,function(e){return!i.validate(n[e],e)});return 0<e.length&&function(e,n){throw new Error("All values need to be of type: "+n+". Keys ("+M(e).join(", ")+") were not.")}(e,i.label),n}},Ju=li(["cell","row","replace","gap"]),Qu=function(n,t){void 0===t&&(t=fi),Ju(n);function r(e){return function(e){return n.cell(e)}(t(e))}function o(e){var n=r(e);return i.get().isNone()&&i.set(We.some(n)),u=We.some({item:e,replacement:n}),n}var i=R(We.none()),u=We.none();return{getOrInit:function(n,t){return u.fold(function(){return o(n)},function(e){return t(n,e.item)?e.replacement:o(n)})},cursor:i.get}},Zu=function(c,a){return function(r){var o=R(We.none());Ju(r);function i(e){var n={scope:c},t=r.replace(e,a,n);return u.push({item:e,sub:t}),o.get().isNone()&&o.set(We.some(t)),t}var u=[];return{replaceOrInit:function(n,t){return function(n,t){return y(u,function(e){return t(e.item,n)})}(n,t).fold(function(){return i(n)},function(e){return t(n,e.item)?e.sub:i(n)})},cursor:o.get}}},ec=function(t){Ju(t);var e=R(We.none());return{combine:function(n){return e.get().isNone()&&e.set(We.some(n)),function(){var e=t.cell({element:D(n),colspan:D(1),rowspan:D(1)});return ce(e,"width"),ce(n,"width"),e}},cursor:e.get}},nc=["body","p","div","article","aside","figcaption","figure","footer","header","nav","section","ol","ul","table","thead","tfoot","tbody","caption","tr","td","th","h1","h2","h3","h4","h5","h6","blockquote","pre","address"],tc=si,rc=function(e,n){var t=e.property().name(n);return l(["ol","ul"],t)},oc=di,ic=Qt(),uc=function(e){return tc(ic,e)},cc=function(e){return rc(ic,e)},ac=function(e){return oc(ic,e)},lc=function(e){function o(e){return"br"===nn(e)}function t(r){return Rt(r).bind(function(n){var t=function(e){return Ce(e).map(function(e){return!!uc(e)||!!ac(e)&&"img"!==nn(e)}).getOr(!1)}(n);return be(n).map(function(e){return!0===t||function(e){return"li"===nn(e)||Qn(e,cc).isSome()}(e)||o(n)||uc(e)&&!Pn(r,e)?[]:[un.fromTag("br")]})}).getOr([])}var n,r=0===(n=x(e,function(e){var n=Se(e);return function(e){return A(e,function(e){return o(e)||rn(e)&&0===bt(e).trim().length})}(n)?[]:n.concat(t(e))})).length?[un.fromTag("br")]:n;Ne(e[0]),Ee(e[0],r)},fc=P("grid","cursor"),sc=function(e,n,t){return We.from(e[n]).bind(function(e){return We.from(e.cells()[t]).bind(function(e){return We.from(e.element())})})},dc=Xu,mc={insertRowBefore:zo(function(e,n,t,r){var o=n.row(),i=n.row(),u=Nu(e,i,o,t,r.getOrInit);return pi(u,i,n.column())},Ho,T,T,Qu),insertRowsBefore:zo(function(e,n,t,r){var o=n[0].row(),i=n[0].row(),u=hi(n),c=w(u,function(e,n){return Nu(e,i,o,t,r.getOrInit)},e);return pi(c,i,n[0].column())},Uo,T,T,Qu),insertRowAfter:zo(function(e,n,t,r){var o=n.row(),i=n.row()+n.rowspan(),u=Nu(e,i,o,t,r.getOrInit);return pi(u,i,n.column())},Ho,T,T,Qu),insertRowsAfter:zo(function(e,n,t,r){var o=hi(n),i=o[o.length-1].row(),u=o[o.length-1].row()+o[o.length-1].rowspan(),c=w(o,function(e,n){return Nu(e,u,i,t,r.getOrInit)},e);return pi(c,u,n[0].column())},Uo,T,T,Qu),insertColumnBefore:zo(function(e,n,t,r){var o=n.column(),i=n.column(),u=ku(e,i,o,t,r.getOrInit);return pi(u,n.row(),i)},Ho,dc,T,Qu),insertColumnsBefore:zo(function(e,n,t,r){var o=vi(n),i=o[0].column(),u=o[0].column(),c=w(o,function(e,n){return ku(e,u,i,t,r.getOrInit)},e);return pi(c,n[0].row(),u)},Uo,dc,T,Qu),insertColumnAfter:zo(function(e,n,t,r){var o=n.column(),i=n.column()+n.colspan(),u=ku(e,i,o,t,r.getOrInit);return pi(u,n.row(),i)},Ho,dc,T,Qu),insertColumnsAfter:zo(function(e,n,t,r){var o=n[n.length-1].column(),i=n[n.length-1].column()+n[n.length-1].colspan(),u=vi(n),c=w(u,function(e,n){return ku(e,i,o,t,r.getOrInit)},e);return pi(c,n[0].row(),i)},Uo,dc,T,Qu),splitCellIntoColumns:zo(function(e,n,t,r){var o=Iu(e,n.row(),n.column(),t,r.getOrInit);return pi(o,n.row(),n.column())},Ho,dc,T,Qu),splitCellIntoRows:zo(function(e,n,t,r){var o=Bu(e,n.row(),n.column(),t,r.getOrInit);return pi(o,n.row(),n.column())},Ho,T,T,Qu),eraseColumns:zo(function(e,n,t,r){var o=vi(n),i=Mu(e,o[0].column(),o[o.length-1].column()),u=gi(i,n[0].row(),n[0].column());return fc(i,u)},Uo,dc,mi,Qu),eraseRows:zo(function(e,n,t,r){var o=hi(n),i=Pu(e,o[0].row(),o[o.length-1].row()),u=gi(i,n[0].row(),n[0].column());return fc(i,u)},Uo,T,mi,Qu),makeColumnHeader:zo(function(e,n,t,r){var o=Wu(e,n.column(),t,r.replaceOrInit);return pi(o,n.row(),n.column())},Ho,T,T,Zu("row","th")),unmakeColumnHeader:zo(function(e,n,t,r){var o=Wu(e,n.column(),t,r.replaceOrInit);return pi(o,n.row(),n.column())},Ho,T,T,Zu(null,"td")),makeRowHeader:zo(function(e,n,t,r){var o=_u(e,n.row(),t,r.replaceOrInit);return pi(o,n.row(),n.column())},Ho,T,T,Zu("col","th")),unmakeRowHeader:zo(function(e,n,t,r){var o=_u(e,n.row(),t,r.replaceOrInit);return pi(o,n.row(),n.column())},Ho,T,T,Zu(null,"td")),mergeCells:zo(function(e,n,t,r){var o=n.cells();lc(o);var i=Tu(e,n.bounds(),t,D(o[0]));return fc(i,We.from(o[0]))},function(e,n){return n.mergable()},T,T,ec),unmergeCells:zo(function(e,n,t,r){var o=v(n,function(e,n){return Ou(e,n,t,r.combine(n))},e);return fc(o,We.from(n[0]))},function(e,n){return n.unmergable()},dc,T,ec),pasteCells:zo(function(e,t,n,r){var o,i,u,c,a=(o=t.clipboard(),i=t.generators(),u=dt(o),c=pt.generate(u),bu(c,i,!0)),l=zn(t.row(),t.column());return Au(l,e,a,t.generators(),n).fold(function(){return fc(e,We.some(t.element()))},function(e){var n=gi(e,t.row(),t.column());return fc(e,n)})},function(n,t){return st.cell(t.element()).bind(function(e){return jo(n,e).map(function(e){return sn(sn({},e),{generators:t.generators,clipboard:t.clipboard})})})},dc,T,Qu),pasteRowsBefore:zo(function(e,n,t,r){var o=e[n.cells[0].row()],i=n.cells[0].row(),u=bi(n.clipboard(),n.generators(),o),c=Eu(i,e,u,n.generators(),t),a=gi(c,n.cells[0].row(),n.cells[0].column());return fc(c,a)},Fo,T,T,Qu),pasteRowsAfter:zo(function(e,n,t,r){var o=e[n.cells[0].row()],i=n.cells[n.cells.length-1].row()+n.cells[n.cells.length-1].rowspan(),u=bi(n.clipboard(),n.generators(),o),c=Eu(i,e,u,n.generators(),t),a=gi(c,n.cells[0].row(),n.cells[0].column());return fc(c,a)},Fo,T,T,Qu)},gc=function(e){return un.fromDom(e.getBody())},pc=function(n){return function(e){return Pn(e,gc(n))}},hc={isRtl:D(!1)},vc={isRtl:D(!0)},bc={directionAt:function(e){return"rtl"===function(e){return"rtl"===ie(e,"direction")?"rtl":"ltr"}(e)?vc:hc}},wc={"border-collapse":"collapse",width:"100%"},yc={border:"1"},Cc=function(e){return e.getParam("table_tab_navigation",!0,"boolean")},Sc=function(e){var n=e.getParam("table_clone_elements");return Le(n)?We.some(n.split(/[ ,]/)):Array.isArray(n)?We.some(n):We.none()},xc=function(e,n,t,r,o){e.fire("TableSelectionChange",{cells:n,start:t,finish:r,otherCells:o})},Rc=function(e){e.fire("TableSelectionClear")},Tc=function(f,e){function t(e){return"table"===nn(gc(e))}function n(u,c,a,l){return function(e,n){Si(e);var t=l(),r=un.fromDom(f.getDoc()),o=bo(bc.directionAt),i=Lt.cellOperations(a,r,s);return c(e)?u(t,e,n,i,o).bind(function(e){return p(e.newRows(),function(e){ki(f,e.dom())}),p(e.newCells(),function(e){Ii(f,e.dom())}),e.cursor().map(function(e){var n=f.dom.createRng();return n.setStart(e.dom(),0),n.setEnd(e.dom(),0),n})}):We.none()}}var s=Sc(f);return{deleteRow:n(mc.eraseRows,function(e){var n=$i(e);return!1===t(f)||1<n.rows()},T,e),deleteColumn:n(mc.eraseColumns,function(e){var n=$i(e);return!1===t(f)||1<n.columns()},T,e),insertRowsBefore:n(mc.insertRowsBefore,i,T,e),insertRowsAfter:n(mc.insertRowsAfter,i,T,e),insertColumnsBefore:n(mc.insertColumnsBefore,i,lo,e),insertColumnsAfter:n(mc.insertColumnsAfter,i,lo,e),mergeCells:n(mc.mergeCells,i,T,e),unmergeCells:n(mc.unmergeCells,i,T,e),pasteRowsBefore:n(mc.pasteRowsBefore,i,T,e),pasteRowsAfter:n(mc.pasteRowsAfter,i,T,e),pasteCells:n(mc.pasteCells,i,T,e)}},Oc=function(e,n,r){var t=dt(e),o=pt.generate(t);return Uo(o,n).map(function(e){var n=bu(o,r,!1).slice(e[0].row(),e[e.length-1].row()+e[e.length-1].rowspan()),t=Lo(n,r);return Qi(t)})},Dc=tinymce.util.Tools.resolve("tinymce.util.Tools"),Ac=function(e,n,t){t&&e.formatter.apply("align"+t,{},n)},Ec=function(e,n,t){t&&e.formatter.apply("valign"+t,{},n)},Nc=function(n,t){Dc.each("left center right".split(" "),function(e){n.formatter.remove("align"+e,{},t)})},kc=function(n,t){Dc.each("top middle bottom".split(" "),function(e){n.formatter.remove("valign"+e,{},t)})},Ic=function(o,e,i){var n;return n=function(e,n){for(var t=0;t<n.length;t++){var r=o.getStyle(n[t],i);if(void 0===e&&(e=r),e!==r)return""}return e}(n,o.select("td,th",e))},Bc=b(Wi,"left center right"),Pc=b(Wi,"top middle bottom"),Mc=function(e,r,n){var o=function(e,t){return t=t||[],Dc.each(e,function(e){var n={text:e.text||e.title};e.menu?n.menu=o(e.menu):(n.value=e.value,r&&r(n)),t.push(n)}),t};return o(e,n||[])},Wc=function(e){var o=e[0],n=e.slice(1),t=Ge(o);return p(n,function(e){p(t,function(r){k(e,function(e,n){var t=o[r];""!==t&&r===n&&t!==e&&(o[r]="")})})}),o},_c=function(e){var n=[{name:"borderstyle",type:"selectbox",label:"Border style",items:[{text:"Select...",value:""},{text:"Solid",value:"solid"},{text:"Dotted",value:"dotted"},{text:"Dashed",value:"dashed"},{text:"Double",value:"double"},{text:"Groove",value:"groove"},{text:"Ridge",value:"ridge"},{text:"Inset",value:"inset"},{text:"Outset",value:"outset"},{text:"None",value:"none"},{text:"Hidden",value:"hidden"}]},{name:"bordercolor",type:"colorinput",label:"Border color"},{name:"backgroundcolor",type:"colorinput",label:"Background color"}];return{title:"Advanced",name:"advanced",items:"cell"===e?[{name:"borderwidth",type:"input",label:"Border width"}].concat(n):n}},Lc=function(e,n,t){var r,o,i,u=e.dom;return sn(sn({width:u.getStyle(n,"width")||u.getAttrib(n,"width"),height:u.getStyle(n,"height")||u.getAttrib(n,"height"),cellspacing:u.getStyle(n,"border-spacing")||u.getAttrib(n,"cellspacing"),cellpadding:u.getAttrib(n,"cellpadding")||Ic(e.dom,n,"padding"),border:(r=u,o=n,i=ue(un.fromDom(o),"border-width"),Ai(e)&&i.isSome()?i.getOr(""):r.getAttrib(o,"border")||Ic(e.dom,o,"border-width")||Ic(e.dom,o,"border")),caption:!!u.select("caption",n)[0],"class":u.getAttrib(n,"class","")},Bc("align","align",e,n)),t?Mi(u,n):{})},jc=function(e,n,t){var r=e.dom;return sn(sn({height:r.getStyle(n,"height")||r.getAttrib(n,"height"),scope:r.getAttrib(n,"scope"),"class":r.getAttrib(n,"class",""),align:"",type:n.parentNode.nodeName.toLowerCase()},Bc("align","align",e,n)),t?Mi(r,n):{})},zc=function(e,n,t){var r=e.dom;return sn(sn(sn({width:r.getStyle(n,"width")||r.getAttrib(n,"width"),height:r.getStyle(n,"height")||r.getAttrib(n,"height"),scope:r.getAttrib(n,"scope"),celltype:n.nodeName.toLowerCase(),"class":r.getAttrib(n,"class","")},Bc("align","halign",e,n)),Pc("valign","valign",e,n)),t?Mi(r,n):{})},Hc=function(e,n){var t,r,o,i,u=Ri(e),c=xi(e),a=e.dom,l=n?(t=a,r=B(u,"border-style").getOr(""),o=B(u,"border-color").getOr(""),i=B(u,"background-color").getOr(""),{borderstyle:r,bordercolor:f(o),backgroundcolor:f(i)}):{};function f(e){return Q(e,"rgb")?t.toHex(e):e}var s,d,m;return sn(sn(sn(sn(sn(sn({},{height:"",width:"100%",cellspacing:"",cellpadding:"",caption:!1,"class":"",align:"",border:""}),u),c),l),(m=u["border-width"],Ai(e)&&m?{border:m}:B(c,"border").fold(function(){return{}},function(e){return{border:e}}))),(s=B(u,"border-spacing").or(B(c,"cellspacing")).fold(function(){return{}},function(e){return{cellspacing:e}}),d=B(u,"border-padding").or(B(c,"cellpadding")).fold(function(){return{}},function(e){return{cellpadding:e}}),sn(sn({},s),d)))},Fc=[{name:"width",type:"input",label:"Width"},{name:"height",type:"input",label:"Height"},{name:"celltype",type:"selectbox",label:"Cell type",items:[{text:"Cell",value:"td"},{text:"Header cell",value:"th"}]},{name:"scope",type:"selectbox",label:"Scope",items:[{text:"None",value:""},{text:"Row",value:"row"},{text:"Column",value:"col"},{text:"Row group",value:"rowgroup"},{text:"Column group",value:"colgroup"}]},{name:"halign",type:"selectbox",label:"H Align",items:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},{name:"valign",type:"selectbox",label:"V Align",items:[{text:"None",value:""},{text:"Top",value:"top"},{text:"Middle",value:"middle"},{text:"Bottom",value:"bottom"}]}],Uc=function(e){return function(n){var e=function(e){return e.getParam("table_cell_class_list",[],"array")}(n),t=Mc(e,function(e){e.value&&(e.textStyle=function(){return n.formatter.getCssText({block:"tr",classes:[e.value]})})});return 0<e.length?We.some({name:"class",type:"selectbox",label:"Class",items:t}):We.none()}(e).fold(function(){return Fc},function(e){return Fc.concat(e)})},qc={normal:function(t,r){return{setAttrib:function(e,n){t.setAttrib(r,e,n)},setStyle:function(e,n){t.setStyle(r,e,n)}}},ifTruthy:function(t,r){return{setAttrib:function(e,n){n&&t.setAttrib(r,e,n)},setStyle:function(e,n){n&&t.setStyle(r,e,n)}}}},Vc=function(n){var e,t=[];if(t=n.dom.select("td[data-mce-selected],th[data-mce-selected]"),e=n.dom.getParent(n.selection.getStart(),"td,th"),!t.length&&e&&t.push(e),e=e||t[0]){var r=Dc.map(t,function(e){return zc(n,e,Ti(n))}),o=Wc(r),i={type:"tabpanel",tabs:[{title:"General",name:"general",items:Uc(n)},_c("cell")]},u={type:"panel",items:[{type:"grid",columns:2,items:Uc(n)}]};n.windowManager.open({title:"Cell Properties",size:"normal",body:Ti(n)?i:u,buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:o,onSubmit:b(Hi,n,t)})}},Gc=[{type:"selectbox",name:"type",label:"Row type",items:[{text:"Header",value:"thead"},{text:"Body",value:"tbody"},{text:"Footer",value:"tfoot"}]},{type:"selectbox",name:"align",label:"Alignment",items:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},{label:"Height",name:"height",type:"input"}],Yc=function(e){return function(n){var e=function(e){return e.getParam("table_row_class_list",[],"array")}(n),t=Mc(e,function(e){e.value&&(e.textStyle=function(){return n.formatter.getCssText({block:"tr",classes:[e.value]})})});return 0<e.length?We.some({name:"class",type:"selectbox",label:"Class",items:t}):We.none()}(e).fold(function(){return Gc},function(e){return Gc.concat(e)})},Kc=function(n){var e,t,r=n.dom,o=[];if((e=r.getParent(n.selection.getStart(),"table"))&&(t=r.getParent(n.selection.getStart(),"td,th"),Dc.each(e.rows,function(n){Dc.each(n.cells,function(e){if((r.getAttrib(e,"data-mce-selected")||e===t)&&o.indexOf(n)<0)return o.push(n),!1})}),o[0])){var i=Dc.map(o,function(e){return jc(n,e,Oi(n))}),u=Wc(i),c={type:"tabpanel",tabs:[{title:"General",name:"general",items:Yc(n)},_c("row")]},a={type:"panel",items:[{type:"grid",columns:2,items:Yc(n)}]};n.windowManager.open({title:"Row Properties",size:"normal",body:Oi(n)?c:a,buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:u,onSubmit:b(Fi,n,o,u)})}},Xc=tinymce.util.Tools.resolve("tinymce.Env"),$c={styles:{"border-collapse":"collapse",width:"100%"},attributes:{border:"1"},percentages:!0},Jc=function(n,e,t){var r=Ri(n),o={styles:r,attributes:xi(n),percentages:function(e){return Le(e)&&-1!==e.indexOf("%")}(r.width)&&!Ni(n)},i=Ui(t,e,0,0,o);q(i,"data-mce-id","__mce");var u=function(e){var n=un.fromTag("div"),t=un.fromDom(e.dom().cloneNode(!0));return _n(n,t),function(e){return e.dom().innerHTML}(n)}(i);return n.insertContent(u),nt(gc(n),'table[data-mce-id="__mce"]').map(function(e){return Ni(n)&&re(e,"width",ie(e,"width")),K(e,"data-mce-id"),function(n,e){p(Pe(e,"tr"),function(e){ki(n,e.dom()),p(Pe(e,"th,td"),function(e){Ii(n,e.dom())})})}(n,e),function(e,n){nt(n,"td,th").each(b(qi,e))}(n,e),e.dom()}).getOr(null)},Qc=function(n,e,t){var r=t?[{type:"input",name:"cols",label:"Cols",inputMode:"numeric"},{type:"input",name:"rows",label:"Rows",inputMode:"numeric"}]:[],o=function(e){return e.getParam("table_appearance_options",!0,"boolean")}(n)?[{type:"input",name:"cellspacing",label:"Cell spacing",inputMode:"numeric"},{type:"input",name:"cellpadding",label:"Cell padding",inputMode:"numeric"},{type:"input",name:"border",label:"Border width"},{type:"label",label:"Caption",items:[{type:"checkbox",name:"caption",label:"Show caption"}]}]:[],i=e?[{type:"selectbox",name:"class",label:"Class",items:Mc(Ei(n),function(e){e.value&&(e.textStyle=function(){return n.formatter.getCssText({block:"table",classes:[e.value]})})})}]:[];return r.concat([{type:"input",name:"width",label:"Width"},{type:"input",name:"height",label:"Height"}]).concat(o).concat([{type:"selectbox",name:"align",label:"Alignment",items:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]}]).concat(i)},Zc=function(e,n,t,r){if("TD"===n.tagName||"TH"===n.tagName)Le(t)?e.setStyle(n,t,r):e.setStyle(n,t);else if(n.children)for(var o=0;o<n.children.length;o++)Zc(e,n.children[o],t,r)},ea=function(e,n){var t,r=e.dom,o=Hc(e,Di(e));!1===n?(t=r.getParent(e.selection.getStart(),"table"))?o=Lc(e,t,Di(e)):Di(e)&&(o.borderstyle="",o.bordercolor="",o.backgroundcolor=""):(o.cols="1",o.rows="1",Di(e)&&(o.borderstyle="",o.bordercolor="",o.backgroundcolor=""));var i=0<Ei(e).length;i&&o["class"]&&(o["class"]=o["class"].replace(/\s*mce\-item\-table\s*/g,""));var u={type:"grid",columns:2,items:Qc(e,i,n)},c=Di(e)?{type:"tabpanel",tabs:[{title:"General",name:"general",items:[u]},_c("table")]}:{type:"panel",items:[u]};e.windowManager.open({title:"Table Properties",size:"normal",body:c,onSubmit:b(Vi,e,t),buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:o})},na=Gi("th,td"),ta=Gi("th,td,caption"),ra=Dc.each,oa={registerCommands:function(c,n,a,l,t){function f(e){return st.table(e,s)}function i(e){return{width:wi(e.dom()),height:wi(e.dom())}}function r(o){na(c).each(function(r){f(r).each(function(n){var e=kr.forMenu(l,n,r),t=i(n);o(n,e).each(function(e){!function(e,n,t){var r=i(t);n.width===r.width&&n.height===r.height||(Bi(e,t.dom(),n.width,n.height),Pi(e,t.dom(),r.width,r.height))}(c,t,n),c.selection.setRng(e),c.focus(),a.clear(n),Si(n)})})})}function o(e){return na(c).map(function(o){return f(o).bind(function(e){var n=un.fromDom(c.getDoc()),t=kr.forMenu(l,e,o),r=Lt.cellOperations(T,n,We.none());return Oc(e,t,r)})})}function u(u){t.get().each(function(e){var i=g(e,function(e){return Dt(e)});na(c).each(function(o){f(o).each(function(n){var e=un.fromDom(c.getDoc()),t=Lt.paste(e),r=kr.pasteRows(l,n,o,i,t);u(n,r).each(function(e){c.selection.setRng(e),c.focus(),a.clear(n)})})})})}var s=pc(c);ra({mceTableSplitCells:function(){r(n.unmergeCells)},mceTableMergeCells:function(){r(n.mergeCells)},mceTableInsertRowBefore:function(){r(n.insertRowsBefore)},mceTableInsertRowAfter:function(){r(n.insertRowsAfter)},mceTableInsertColBefore:function(){r(n.insertColumnsBefore)},mceTableInsertColAfter:function(){r(n.insertColumnsAfter)},mceTableDeleteCol:function(){r(n.deleteColumn)},mceTableDeleteRow:function(){r(n.deleteRow)},mceTableCutRow:function(e){o().each(function(e){t.set(e),r(n.deleteRow)})},mceTableCopyRow:function(e){o().each(function(e){t.set(e)})},mceTablePasteRowBefore:function(e){u(n.pasteRowsBefore)},mceTablePasteRowAfter:function(e){u(n.pasteRowsAfter)},mceTableDelete:function(){ta(c).each(function(e){st.table(e,s).filter(d(s)).each(function(e){var n=un.fromText("");if(Te(e,n),Ln(e),c.dom.isEmpty(c.getBody()))c.setContent(""),c.selection.setCursorLocation();else{var t=c.dom.createRng();t.setStart(n.dom(),0),t.setEnd(n.dom(),0),c.selection.setRng(t),c.nodeChanged()}})})}},function(e,n){c.addCommand(n,e)}),ra({mceInsertTable:b(ea,c,!0),mceTableProps:b(ea,c,!1),mceTableRowProps:b(Kc,c),mceTableCellProps:b(Vc,c)},function(e,n){c.addCommand(n,function(){e()})})}},ia=function(e){var n=We.from(e.dom().documentElement).map(un.fromDom).getOr(e);return{parent:D(n),view:D(e),origin:D(so(0,0))}},ua=function(e,n){return{parent:D(n),view:D(e),origin:D(so(0,0))}},ca=function(e){var r=P.apply(null,e),o=[];return{bind:function(e){if(e===undefined)throw new Error("Event bind error: undefined handler");o.push(e)},unbind:function(n){o=h(o,function(e){return e!==n})},trigger:function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];var t=r.apply(null,e);p(o,function(e){e(t)})}}},aa={create:function(e){return{registry:I(e,function(e){return{bind:e.bind,unbind:e.unbind}}),trigger:I(e,function(e){return e.trigger})}}},la=function(e,n,t,r){e.dom().removeEventListener(n,t,r)},fa=D(!0),sa={resolve:No("ephox-dragster").resolve},da=li(["compare","extract","mutate","sink"]),ma=li(["element","start","stop","destroy"]),ga=li(["forceDrop","drop","move","delayDrop"]),pa=da({compare:function(e,n){return so(n.left()-e.left(),n.top()-e.top())},extract:function(e){return We.some(so(e.x(),e.y()))},sink:function(e,n){var t=function(e){var n=sn({layerClass:sa.resolve("blocker")},e),t=un.fromTag("div");q(t,"role","presentation"),oe(t,{position:"fixed",left:"0px",top:"0px",width:"100%",height:"100%"}),To(t,sa.resolve("blocker")),To(t,n.layerClass);return{element:function(){return t},destroy:function(){Ln(t)}}}(n),r=Xi(t.element(),"mousedown",e.forceDrop),o=Xi(t.element(),"mouseup",e.drop),i=Xi(t.element(),"mousemove",e.move),u=Xi(t.element(),"mouseout",e.delayDrop);return ma({element:t.element,start:function(e){_n(e,t.element())},stop:function(){Ln(t.element())},destroy:function(){t.destroy(),o.unbind(),i.unbind(),u.unbind(),r.unbind()}})},mutate:function(e,n){e.mutate(n.left(),n.top())}});function ha(){var r=We.none(),t=aa.create({move:ca(["info"])});return{onEvent:function(e,n){n.extract(e).each(function(e){(function(n,t){var e=r.map(function(e){return n.compare(e,t)});return r=We.some(t),e})(n,e).each(function(e){t.trigger.move(e)})})},reset:function(){r=We.none()},events:t.registry}}function va(){var e=function r(){return{onEvent:T,reset:T}}(),n=ha(),t=e;return{on:function(){t.reset(),t=n},off:function(){t.reset(),t=e},isOn:function(){return t===n},onEvent:function(e,n){t.onEvent(e,n)},events:n.events}}function ba(){var t=aa.create({drag:ca(["xDelta","yDelta","target"])}),r=We.none(),e=function(){var t=aa.create({drag:ca(["xDelta","yDelta"])});return{mutate:function(e,n){t.trigger.drag(e,n)},events:t.registry}}();return e.events.drag.bind(function(n){r.each(function(e){t.trigger.drag(n.xDelta(),n.yDelta(),e)})}),{assign:function(e){r=We.some(e)},get:function(){return r},mutate:e.mutate,events:t.registry}}function wa(e){return"true"===G(e,"contenteditable")}function ya(o,n,i){function e(e,n){return We.from(G(e,n))}var t=ba(),r=pl(t,{}),u=We.none();function c(e,n){return Hu.getInt(e,n)-parseInt(G(e,"data-initial-"+n),10)}function a(e,n){m.trigger.startAdjust(),t.assign(e),q(e,"data-initial-"+n,parseInt(ie(e,n),10)),To(e,hl),re(e,"opacity","0.2"),r.go(o.parent())}function l(e){return Pn(e,o.view())}function f(e){return tt(e,"table",l).filter(function(e){return function(e,n){return tt(e,"[contenteditable]",n)}(e,l).exists(wa)})}t.events.drag.bind(function(t){e(t.target(),"data-row").each(function(e){var n=Hu.getInt(t.target(),"top");re(t.target(),"top",n+t.yDelta()+"px")}),e(t.target(),"data-column").each(function(e){var n=Hu.getInt(t.target(),"left");re(t.target(),"left",n+t.xDelta()+"px")})}),r.events.stop.bind(function(){t.get().each(function(r){u.each(function(t){e(r,"data-row").each(function(e){var n=c(r,"top");K(r,"data-initial-top"),m.trigger.adjustHeight(t,n,parseInt(e,10))}),e(r,"data-column").each(function(e){var n=c(r,"left");K(r,"data-initial-left"),m.trigger.adjustWidth(t,n,parseInt(e,10))}),au(o,t,i,n)})})});var s=Xi(o.parent(),"mousedown",function(e){du(e.target())&&a(e.target(),"top"),mu(e.target())&&a(e.target(),"left")}),d=Xi(o.view(),"mouseover",function(e){f(e.target()).fold(function(){ne(e.target())&&su(o)},function(e){u=We.some(e),au(o,e,i,n)})}),m=aa.create({adjustHeight:ca(["table","delta","row"]),adjustWidth:ca(["table","delta","column"]),startAdjust:ca([])});return{destroy:function(){s.unbind(),d.unbind(),r.destroy(),su(o)},refresh:function(e){au(o,e,i,n)},on:r.on,off:r.off,hideBars:b(lu,o),showBars:b(fu,o),events:m.registry}}function Ca(e,n){return wi(e.dom())/wi(n.dom())*100+"%"}function Sa(t,e){return st.table(t,e).bind(function(e){var n=st.cells(e);return C(n,function(e){return Pn(t,e)}).map(function(e){return{index:D(e),all:D(n)}})})}function xa(e,n,t){var r=e.document.createRange();return function(t,e){e.fold(function(e){t.setStartBefore(e.dom())},function(e,n){t.setStart(e.dom(),n)},function(e){t.setStartAfter(e.dom())})}(r,n),function(t,e){e.fold(function(e){t.setEndBefore(e.dom())},function(e,n){t.setEnd(e.dom(),n)},function(e){t.setEndAfter(e.dom())})}(r,t),r}function Ra(e,n,t,r,o){var i=e.document.createRange();return i.setStart(n.dom(),t),i.setEnd(r.dom(),o),i}function Ta(e){return{left:D(e.left),top:D(e.top),right:D(e.right),bottom:D(e.bottom),width:D(e.width),height:D(e.height)}}function Oa(e,n,t){return n(un.fromDom(t.startContainer),t.startOffset,un.fromDom(t.endContainer),t.endOffset)}function Da(e,n){return function(e,n){var t=n.ltr();return t.collapsed?n.rtl().filter(function(e){return!1===e.collapsed}).map(function(e){return kl.rtl(un.fromDom(e.endContainer),e.endOffset,un.fromDom(e.startContainer),e.startOffset)}).getOrThunk(function(){return Oa(0,kl.ltr,t)}):Oa(0,kl.ltr,t)}(0,function(o,e){return e.match({domRange:function(e){return{ltr:D(e),rtl:We.none}},relative:function(e,n){return{ltr:ee(function(){return xa(o,e,n)}),rtl:ee(function(){return We.some(xa(o,n,e))})}},exact:function(e,n,t,r){return{ltr:ee(function(){return Ra(o,e,n,t,r)}),rtl:ee(function(){return We.some(Ra(o,t,r,e,n))})}}})}(e,n))}function Aa(e,n,t){return n>=e.left&&n<=e.right&&t>=e.top&&t<=e.bottom}function Ea(t,r,e,n,o){function i(e){var n=t.dom().createRange();return n.setStart(r.dom(),e),n.collapse(!0),n}var u=bt(r).length,c=function(e,n,t,r,o){if(0===o)return 0;if(n===r)return o-1;for(var i=r,u=1;u<o;u++){var c=e(u),a=Math.abs(n-c.left);if(t<=c.bottom){if(t<c.top||i<a)return u-1;i=a}}return 0}(function(e){return i(e).getBoundingClientRect()},e,n,o.right,u);return i(c)}function Na(e,n){return n-e.left<e.right-n}function ka(e,n,t){var r=e.dom().createRange();return r.selectNode(n.dom()),r.collapse(t),r}function Ia(n,e,t){var r=n.dom().createRange();r.selectNode(e.dom());var o=r.getBoundingClientRect(),i=Na(o,t);return(!0===i?xt:Rt)(e).map(function(e){return ka(n,e,i)})}function Ba(e,n,t){var r=n.dom().getBoundingClientRect(),o=Na(r,t);return We.some(ka(e,n,o))}function Pa(e,n,t,r){var o=e.dom().createRange();o.selectNode(n.dom());var i=o.getBoundingClientRect();return function(e,n,t,r){var o=e.dom().createRange();o.selectNode(n.dom());var i=o.getBoundingClientRect(),u=Math.max(i.left,Math.min(i.right,t)),c=Math.max(i.top,Math.min(i.bottom,r));return Bl(e,n,u,c)}(e,n,Math.max(i.left,Math.min(i.right,t)),Math.max(i.top,Math.min(i.bottom,r)))}function Ma(e,n){var t=nn(e);return"input"===t?Dl.after(e):l(["br","img"],t)?0===n?Dl.before(e):Dl.after(e):Dl.on(e,n)}function Wa(e,n){var t=e.fold(Dl.before,Ma,Dl.after),r=n.fold(Dl.before,Ma,Dl.after);return El.relative(t,r)}function _a(e,n,t,r){var o=Ma(e,n),i=Ma(t,r);return El.relative(o,i)}function La(e,n,t,r){var o=function(e,n,t,r){var o=ve(e).dom().createRange();return o.setStart(e.dom(),n),o.setEnd(t.dom(),r),o}(e,n,t,r),i=Pn(e,t)&&n===r;return o.collapsed&&!i}function ja(e,n){We.from(e.getSelection()).each(function(e){e.removeAllRanges(),e.addRange(n)})}function za(e,n,t,r,o){var i=Ra(e,n,t,r,o);ja(e,i)}function Ha(u,e){return Da(u,e).match({ltr:function(e,n,t,r){za(u,e,n,t,r)},rtl:function(e,n,t,r){var o=u.getSelection();if(o.setBaseAndExtent)o.setBaseAndExtent(e.dom(),n,t.dom(),r);else if(o.extend)try{!function(e,n,t,r,o,i){n.collapse(t.dom(),r),n.extend(o.dom(),i)}(0,o,e,n,t,r)}catch(i){za(u,t,r,e,n)}else za(u,t,r,e,n)}})}function Fa(e,n,t,r,o){var i=_a(n,t,r,o);Ha(e,i)}function Ua(e,n,t){var r=Wa(n,t);Ha(e,r)}function qa(e){function n(e,n,t,r){return Ra(o,e,n,t,r)}var o=El.getWin(e).dom(),t=function(e){return e.match({domRange:function(e){var n=un.fromDom(e.startContainer),t=un.fromDom(e.endContainer);return _a(n,e.startOffset,t,e.endOffset)},relative:Wa,exact:_a})}(e);return Da(o,t).match({ltr:n,rtl:n})}function Va(e){var n=un.fromDom(e.anchorNode),t=un.fromDom(e.focusNode);return La(n,e.anchorOffset,t,e.focusOffset)?We.some(Tl.create(n,e.anchorOffset,t,e.focusOffset)):function(e){if(0<e.rangeCount){var n=e.getRangeAt(0),t=e.getRangeAt(e.rangeCount-1);return We.some(Tl.create(un.fromDom(n.startContainer),n.startOffset,un.fromDom(t.endContainer),t.endOffset))}return We.none()}(e)}function Ga(e,n){var t=function(e,n){var t=e.document.createRange();return Nl(t,n),t}(e,n);ja(e,t)}function Ya(e){return function(e){return We.from(e.getSelection()).filter(function(e){return 0<e.rangeCount}).bind(Va)}(e).map(function(e){return El.exact(e.start(),e.soffset(),e.finish(),e.foffset())})}function Ka(e,n){return function(e){var n=e.getClientRects(),t=0<n.length?n[0]:e.getBoundingClientRect();return 0<t.width||0<t.height?We.some(t).map(Ta):We.none()}(Il(e,n))}function Xa(e,n,t){return function(e,n,t){var r=un.fromDom(e.document);return Pl(r,n,t).map(function(e){return Tl.create(un.fromDom(e.startContainer),e.startOffset,un.fromDom(e.endContainer),e.endOffset)})}(e,n,t)}function $a(e,n,t,r){return Wl(e,n,xl(t),r)}function Ja(e,n,t,r){return Wl(e,n,Rl(t),r)}function Qa(e,n){var t=El.exact(n,0,n,0);return qa(t)}function Za(e,n){return function(e){return 0===e.length?We.none():We.some(e[e.length-1])}(Pe(n,"tr")).bind(function(e){return nt(e,"td,th").map(function(e){return Qa(0,e)})})}function el(e,n,t,r){return void 0===r&&(r=Vl),e.property().parent(n).map(function(e){return ql(e,r)})}function nl(n){return function(e){return 0===n.property().children(e).length}}function tl(e,n){return function(e,n,t){return Ql(e,n,nl(e),t)}(ef,e,n)}function rl(e,n){return function(e,n,t){return Zl(e,n,nl(e),t)}(ef,e,n)}function ol(e){return tt(e,"tr")}function il(e){return"br"===nn(e)}function ul(n,e,t,r){return function(e,n){return xe(e,n).filter(il).orThunk(function(){return xe(e,n-1).filter(il)})}(e,t).bind(function(e){return r.traverse(e).fold(function(){return cf(e,r.gather,n).map(r.relative)},function(e){return function(r){return be(r).bind(function(n){var t=Se(n);return uf(t,r).map(function(e){return of(n,t,r,e)})})}(e).map(function(e){return Dl.on(e.parent(),e.index())})})})}function cl(e){return sf.nu({left:e.left,top:e.top,right:e.right,bottom:e.bottom})}function al(e,n){return We.some(e.getRect(n))}function ll(n,e,t){return function(e,n,t){return Jn(function(e,n){return n(e)},Qn,e,n,t)}(e,uc).fold(D(!1),function(e){return mf(n,e).exists(function(e){return function(e,n){return e.left()<n.left()||Math.abs(n.right()-e.left())<1||e.left()>n.right()}(t,e)})})}function fl(n,t,e){var r=n.move(e,5),o=vf(t,n,e,r,100).getOr(r);return function(e,n,t){return e.point(n)>t.getInnerHeight()?We.some(e.point(n)-t.getInnerHeight()):e.point(n)<0?We.some(-e.point(n)):We.none()}(n,o,t).fold(function(){return t.situsFromPoint(o.left(),n.point(o))},function(e){return t.scrollBy(0,e),t.situsFromPoint(o.left(),n.point(o)-e)})}function sl(e,n){return function(e,n,t){return Qn(e,n,t).isSome()}(e,function(e){return be(e).exists(function(e){return Pn(e,n)})})}function dl(n,r,o,e,i){return tt(e,"td,th",r).bind(function(t){return tt(t,"table",r).bind(function(e){return sl(i,e)?Rf(n,r,o).bind(function(n){return tt(n.finish(),"td,th",r).map(function(e){return{start:D(t),finish:D(e),range:D(n)}})}):We.none()})})}function ml(e,n){return tt(e,"td,th",n)}var gl=function(n,t,e){function r(){l.stop(),u.isOn()&&(u.off(),i.trigger.stop())}var o=!1,i=aa.create({start:ca([]),stop:ca([])}),u=va(),c=function(t,r){var o=null;return{cancel:function(){null!==o&&(f.clearTimeout(o),o=null)},throttle:function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];null!==o&&f.clearTimeout(o),o=f.setTimeout(function(){t.apply(null,e),o=null},r)}}}(r,200);u.events.move.bind(function(e){t.mutate(n,e.info())});function a(t){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];o&&t.apply(null,e)}}var l=t.sink(ga({forceDrop:r,drop:a(r),move:a(function(e){c.cancel(),u.onEvent(e,t)}),delayDrop:a(c.throttle)}),e);return{element:l.element,go:function(e){l.start(e),u.on(),i.trigger.start()},on:function(){o=!0},off:function(){o=!1},destroy:function(){l.destroy()},events:i.registry}},pl=function(e,n){void 0===n&&(n={});var t=n.mode!==undefined?n.mode:pa;return gl(e,t,n)},hl=tu.resolve("resizer-bar-dragging"),vl=function(e,t){var r=ho.height,n=ya(e,t,r),o=aa.create({beforeResize:ca(["table"]),afterResize:ca(["table"]),startDrag:ca([])});return n.events.adjustHeight.bind(function(e){o.trigger.beforeResize(e.table());var n=r.delta(e.delta(),e.table());Ku(e.table(),n,e.row(),r),o.trigger.afterResize(e.table())}),n.events.startAdjust.bind(function(e){o.trigger.startDrag()}),n.events.adjustWidth.bind(function(e){o.trigger.beforeResize(e.table());var n=t.delta(e.delta(),e.table());Yu(e.table(),n,e.column(),t),o.trigger.afterResize(e.table())}),{on:n.on,off:n.off,hideBars:n.hideBars,showBars:n.showBars,destroy:n.destroy,events:o.registry}},bl=function(e,n){return e.inline?ua(gc(e),function(){var e=un.fromTag("div");return oe(e,{position:"static",height:"0",width:"0",padding:"0",margin:"0",border:"0"}),_n(cn(),e),e}()):ia(un.fromDom(e.getDoc()))},wl=function(e,n){e.inline&&Ln(n.parent())},yl=function(u){function c(e){return"TABLE"===e.nodeName}function r(e){var n=u.dom.getStyle(e,"width")||u.dom.getAttrib(e,"width");return We.from(n).filter(function(e){return 0<e.length})}function e(){return i}var a,l,o=We.none(),i=We.none(),f=We.none(),s=/(\d+(\.\d+)?)%/;return u.on("init",function(){var e=bo(bc.directionAt),n=bl(u);if(f=We.some(n),function(e){var n=e.getParam("object_resizing",!0);return Le(n)?"table"===n:n}(u)&&function(e){return e.getParam("table_resize_bars",!0,"boolean")}(u)){var t=vl(n,e);t.on(),t.events.startDrag.bind(function(e){o=We.some(u.selection.getRng())}),t.events.beforeResize.bind(function(e){var n=e.table().dom();Bi(u,n,wi(n),yi(n))}),t.events.afterResize.bind(function(e){var n=e.table(),t=n.dom();Si(n),o.each(function(e){u.selection.setRng(e),u.focus()}),Pi(u,t,wi(t),yi(t)),u.undoManager.add()}),i=We.some(t)}}),u.on("ObjectResizeStart",function(e){var n=e.target;if(c(n)){var t=r(n).map(function(e){return s.test(e)}).getOr(!1);t&&Ni(u)?function(e){re(un.fromDom(e),"width",wi(e).toString()+"px")}(n):!t&&function(e){return!0===e.getParam("table_responsive_width")}(u)&&function(e){var n=un.fromDom(e);be(n).map(function(e){return Ca(n,e)}).each(function(e){re(n,"width",e),p(Pe(n,"tr"),function(n){p(Se(n),function(e){re(e,"width",Ca(e,n))})})})}(n),a=e.width,l=r(n).getOr("")}}),u.on("ObjectResized",function(e){var n=e.target;if(c(n)){var t=n;if(s.test(l)){var r=parseFloat(s.exec(l)[1]),o=e.width*r/a;u.dom.setStyle(t,"width",o+"%")}else{var i=[];Dc.each(t.rows,function(e){Dc.each(e.cells,function(e){var n=u.dom.getStyle(e,"width",!0);i.push({cell:e,width:n})})}),Dc.each(i,function(e){u.dom.setStyle(e.cell,"width",e.width),u.dom.setAttrib(e.cell,"width",null)})}}}),u.on("SwitchMode",function(){e().each(function(e){u.readonly?e.hideBars():e.showBars()})}),{lazyResize:e,lazyWire:function(){return f.getOr(ia(un.fromDom(u.getBody())))},destroy:function(){i.each(function(e){e.destroy()}),f.each(function(e){wl(u,e)})}}},Cl=Rr([{none:["current"]},{first:["current"]},{middle:["current","target"]},{last:["current"]}]),Sl=sn(sn({},Cl),{none:function(e){return void 0===e&&(e=undefined),Cl.none(e)}}),xl=function(n,e){return Sa(n,e).fold(function(){return Sl.none(n)},function(e){return e.index()+1<e.all().length?Sl.middle(n,e.all()[e.index()+1]):Sl.last(n)})},Rl=function(n,e){return Sa(n,e).fold(function(){return Sl.none()},function(e){return 0<=e.index()-1?Sl.middle(n,e.all()[e.index()-1]):Sl.first(n)})},Tl={create:P("start","soffset","finish","foffset")},Ol=Rr([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),Dl={before:Ol.before,on:Ol.on,after:Ol.after,cata:function(e,n,t,r){return e.fold(n,t,r)},getStart:function(e){return e.fold(o,o,o)}},Al=Rr([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),El={domRange:Al.domRange,relative:Al.relative,exact:Al.exact,exactFromRange:function(e){return Al.exact(e.start(),e.soffset(),e.finish(),e.foffset())},getWin:function(e){return function(e){return un.fromDom(e.dom().ownerDocument.defaultView)}(function(e){return e.match({domRange:function(e){return un.fromDom(e.startContainer)},relative:function(e,n){return Dl.getStart(e)},exact:function(e,n,t,r){return e}})}(e))},range:Tl.create},Nl=function(e,n){e.selectNodeContents(n.dom())},kl=Rr([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),Il=function(i,e){return Da(i,e).match({ltr:function(e,n,t,r){var o=i.document.createRange();return o.setStart(e.dom(),n),o.setEnd(t.dom(),r),o},rtl:function(e,n,t,r){var o=i.document.createRange();return o.setStart(t.dom(),r),o.setEnd(e.dom(),n),o}})},Bl=(kl.ltr,kl.rtl,function(e,n,t,r){return rn(n)?function(n,t,r,o){var e=n.dom().createRange();e.selectNode(t.dom());var i=e.getClientRects();return N(i,function(e){return Aa(e,r,o)?We.some(e):We.none()}).map(function(e){return Ea(n,t,r,o,e)})}(e,n,t,r):function(n,e,t,r){var o=n.dom().createRange(),i=Se(e);return N(i,function(e){return o.selectNode(e.dom()),Aa(o.getBoundingClientRect(),t,r)?Bl(n,e,t,r):We.none()})}(e,n,t,r)}),Pl=document.caretPositionFromPoint?function(t,e,n){return We.from(t.dom().caretPositionFromPoint(e,n)).bind(function(e){if(null===e.offsetNode)return We.none();var n=t.dom().createRange();return n.setStart(e.offsetNode,e.offset),n.collapse(),We.some(n)})}:document.caretRangeFromPoint?function(e,n,t){return We.from(e.dom().caretRangeFromPoint(n,t))}:function(t,r,o){return un.fromPoint(t,r,o).bind(function(e){function n(){return function(e,n,t){return(0===Se(n).length?Ba:Ia)(e,n,t)}(t,e,r)}return 0===Se(e).length?n():Pa(t,e,r,o).orThunk(n)})},Ml=tinymce.util.Tools.resolve("tinymce.util.VK"),Wl=function(r,e,n,o,t){return n.fold(We.none,We.none,function(e,n){return xt(n).map(function(e){return Qa(0,e)})},function(t){return st.table(t,e).bind(function(e){var n=kr.noMenu(t);return r.undoManager.transact(function(){o.insertRowsAfter(e,n)}),Za(0,e)})})},_l=["table","li","dl"],Ll={handle:function(n,t,r,o){if(n.keyCode===Ml.TAB){var i=gc(t),u=function(e){var n=nn(e);return Pn(e,i)||l(_l,n)},e=t.selection.getRng();if(e.collapsed){var c=un.fromDom(e.startContainer);st.cell(c,u).each(function(e){n.preventDefault(),(n.shiftKey?Ja:$a)(t,u,e,r,o).each(function(e){t.selection.setRng(e)})})}}}},jl={create:P("selection","kill")},zl=function(e,n,t,r){return{start:D(Dl.on(e,n)),finish:D(Dl.on(t,r))}},Hl={convertToRange:function(e,n){var t=Il(e,n);return Tl.create(un.fromDom(t.startContainer),t.startOffset,un.fromDom(t.endContainer),t.endOffset)},makeSitus:zl},Fl=function(t,e,r,n,o){return Pn(r,n)?We.none():gr(r,n,e).bind(function(e){var n=e.boxes().getOr([]);return 0<n.length?(o(t,n,e.start(),e.finish()),We.some(jl.create(We.some(Hl.makeSitus(r,0,r,Ct(r))),!0))):We.none()})},Ul={sync:function(t,r,e,n,o,i,u){return Pn(e,o)&&n===i?We.none():tt(e,"td,th",r).bind(function(n){return tt(o,"td,th",r).bind(function(e){return Fl(t,r,n,e,u)})})},detect:Fl,update:function(e,n,t,r,o){return hr(r,e,n,o.firstSelectedSelector(),o.lastSelectedSelector()).map(function(e){return o.clearBeforeUpdate(t),o.selectRange(t,e.boxes(),e.start(),e.finish()),e.boxes()})}},ql=P("item","mode"),Vl=function(e,n,t,r){return void 0===r&&(r=Gl),t.sibling(e,n).map(function(e){return ql(e,r)})},Gl=function(e,n,t,r){void 0===r&&(r=Gl);var o=e.property().children(n);return t.first(o).map(function(e){return ql(e,r)})},Yl=[{current:el,next:Vl,fallback:We.none()},{current:Vl,next:Gl,fallback:We.some(el)},{current:Gl,next:Gl,fallback:We.some(Vl)}],Kl=function(n,t,r,o,e){return void 0===e&&(e=Yl),y(e,function(e){return e.current===r}).bind(function(e){return e.current(n,t,o,e.next).orThunk(function(){return e.fallback.bind(function(e){return Kl(n,t,e,o)})})})},Xl=function(){return{sibling:function(e,n){return e.query().prevSibling(n)},first:function(e){return 0<e.length?We.some(e[e.length-1]):We.none()}}},$l=function(){return{sibling:function(e,n){return e.query().nextSibling(n)},first:function(e){return 0<e.length?We.some(e[0]):We.none()}}},Jl=function(n,e,t,r,o,i){return Kl(n,e,r,o).bind(function(e){return i(e.item())?We.none():t(e.item())?We.some(e.item()):Jl(n,e.item(),t,e.mode(),o,i)})},Ql=function(e,n,t,r){return Jl(e,n,t,Vl,Xl(),r)},Zl=function(e,n,t,r){return Jl(e,n,t,Vl,$l(),r)},ef=Qt(),nf=P("element","offset"),tf=(P("element","deltaOffset"),P("element","start","finish"),P("begin","end"),P("element","text"),Rr([{none:["message"]},{success:[]},{failedUp:["cell"]},{failedDown:["cell"]}])),rf=sn(sn({},tf),{verify:function(t,n,e,r,o,i,u){return tt(r,"td,th",u).bind(function(e){return tt(n,"td,th",u).map(function(n){return Pn(e,n)?Pn(r,e)&&Ct(e)===o?i(n):tf.none("in same cell"):dr.sharedOne(ol,[e,n]).fold(function(){return function(e,n,t){var r=e.getRect(n),o=e.getRect(t);return o.right>r.left&&o.left<r.right}(t,n,e)?tf.success():i(n)},function(e){return i(n)})})}).getOr(tf.none("default"))},cata:function(e,n,t,r,o){return e.fold(n,t,r,o)}}),of=(P("ancestor","descendants","element","index"),P("parent","children","element","index")),uf=function(e,n){return C(e,b(Pn,n))},cf=function(e,n,t){return n(e,t).bind(function(e){return rn(e)&&0===bt(e).trim().length?cf(e,n,t):We.some(e)})},af=function(e,n,t,r){return(il(n)?function(e,n,t){return t.traverse(n).orThunk(function(){return cf(n,t.gather,e)}).map(t.relative)}(e,n,r):ul(e,n,t,r)).map(function(e){return{start:D(e),finish:D(e)}})},lf=function(e){return rf.cata(e,function(e){return We.none()},function(){return We.none()},function(e){return We.some(nf(e,0))},function(e){return We.some(nf(e,Ct(e)))})},ff=$e(["left","top","right","bottom"],[]),sf={nu:ff,moveUp:function(e,n){return ff({left:e.left(),top:e.top()-n,right:e.right(),bottom:e.bottom()-n})},moveDown:function(e,n){return ff({left:e.left(),top:e.top()+n,right:e.right(),bottom:e.bottom()+n})},moveBottomTo:function(e,n){var t=e.bottom()-e.top();return ff({left:e.left(),top:n-t,right:e.right(),bottom:n})},moveTopTo:function(e,n){var t=e.bottom()-e.top();return ff({left:e.left(),top:n,right:e.right(),bottom:n+t})},getTop:function(e){return e.top()},getBottom:function(e){return e.bottom()},translate:function(e,n,t){return ff({left:e.left()+n,top:e.top()+t,right:e.right()+n,bottom:e.bottom()+t})},toString:function(e){return"("+e.left()+", "+e.top()+") -> ("+e.right()+", "+e.bottom()+")"}},df=function(e,n,t){return tn(n)?al(e,n).map(cl):rn(n)?function(e,n,t){return 0<=t&&t<Ct(n)?e.getRangedRect(n,t,n,t+1):0<t?e.getRangedRect(n,t-1,n,t):We.none()}(e,n,t).map(cl):We.none()},mf=function(e,n){return tn(n)?al(e,n).map(cl):rn(n)?e.getRangedRect(n,0,n,Ct(n)).map(cl):We.none()},gf=Rr([{none:[]},{retry:["caret"]}]),pf={point:sf.getTop,adjuster:function(e,n,t,r,o){var i=sf.moveUp(o,5);return Math.abs(t.top()-r.top())<1?gf.retry(i):t.bottom()<o.top()?gf.retry(i):t.bottom()===o.top()?gf.retry(sf.moveUp(o,1)):ll(e,n,o)?gf.retry(sf.translate(i,5,0)):gf.none()},move:sf.moveUp,gather:tl},hf={point:sf.getBottom,adjuster:function(e,n,t,r,o){var i=sf.moveDown(o,5);return Math.abs(t.bottom()-r.bottom())<1?gf.retry(i):t.top()>o.bottom()?gf.retry(i):t.top()===o.bottom()?gf.retry(sf.moveDown(o,1)):ll(e,n,o)?gf.retry(sf.translate(i,5,0)):gf.none()},move:sf.moveDown,gather:rl},vf=function(t,r,o,i,u){return 0===u?We.some(i):function(e,n,t){return e.elementFromPoint(n,t).filter(function(e){return"table"===nn(e)}).isSome()}(t,i.left(),r.point(i))?function(e,n,t,r,o){return vf(e,n,t,n.move(r,5),o)}(t,r,o,i,u-1):t.situsFromPoint(i.left(),r.point(i)).bind(function(e){return e.start().fold(We.none,function(n){return mf(t,n).bind(function(e){return r.adjuster(t,n,e,o,i).fold(We.none,function(e){return vf(t,r,o,e,u-1)})}).orThunk(function(){return We.some(i)})},We.none)})},bf={tryUp:b(fl,pf),tryDown:b(fl,hf),ieTryUp:function(e,n){return e.situsFromPoint(n.left(),n.top()-5)},ieTryDown:function(e,n){return e.situsFromPoint(n.left(),n.bottom()+5)},getJumpSize:D(5)},wf=ge(),yf=function(r,o,i,u,c,a){return 0===a?We.none():xf(r,o,i,u,c).bind(function(e){var n=r.fromSitus(e),t=rf.verify(r,i,u,n.finish(),n.foffset(),c.failure,o);return rf.cata(t,function(){return We.none()},function(){return We.some(e)},function(e){return Pn(i,e)&&0===u?Cf(r,i,u,sf.moveUp,c):yf(r,o,e,0,c,a-1)},function(e){return Pn(i,e)&&u===Ct(e)?Cf(r,i,u,sf.moveDown,c):yf(r,o,e,Ct(e),c,a-1)})})},Cf=function(n,e,t,r,o){return df(n,e,t).bind(function(e){return Sf(n,o,r(e,bf.getJumpSize()))})},Sf=function(e,n,t){return wf.browser.isChrome()||wf.browser.isSafari()||wf.browser.isFirefox()||wf.browser.isEdge()?n.otherRetry(e,t):wf.browser.isIE()?n.ieRetry(e,t):We.none()},xf=function(n,e,t,r,o){return df(n,t,r).bind(function(e){return Sf(n,o,e)})},Rf=function(n,t,r){return function(o,i,u){return o.getSelection().bind(function(r){return af(i,r.finish(),r.foffset(),u).fold(function(){return We.some(nf(r.finish(),r.foffset()))},function(e){var n=o.fromSitus(e),t=rf.verify(o,r.finish(),r.foffset(),n.finish(),n.foffset(),u.failure,i);return lf(t)})})}(n,t,r).bind(function(e){return yf(n,t,e.element(),e.offset(),r,20).map(n.fromSitus)})},Tf=ge(),Of=function(e,n,t,r,o,i){return Tf.browser.isIE()?We.none():i(r,n).orThunk(function(){return dl(e,n,t,r,o).map(function(e){var n=e.range();return jl.create(We.some(Hl.makeSitus(n.start(),n.soffset(),n.finish(),n.foffset())),!0)})})},Df=function(e,n,t,r,o,i,u){return dl(e,t,r,o,i).bind(function(e){return Ul.detect(n,t,e.start(),e.finish(),u)})},Af=function(e,r){return tt(e,"tr",r).bind(function(t){return tt(t,"table",r).bind(function(e){var n=Pe(e,"tr");return Pn(t,n[0])?function(e,n,t){return Ql(ef,e,n,t)}(e,function(e){return Rt(e).isSome()},r).map(function(e){var n=Ct(e);return jl.create(We.some(Hl.makeSitus(e,n,e,n)),!0)}):We.none()})})},Ef=function(e,r){return tt(e,"tr",r).bind(function(t){return tt(t,"table",r).bind(function(e){var n=Pe(e,"tr");return Pn(t,n[n.length-1])?function(e,n,t){return Zl(ef,e,n,t)}(e,function(e){return xt(e).isSome()},r).map(function(e){return jl.create(We.some(Hl.makeSitus(e,0,e,0)),!0)}):We.none()})})};function Nf(n){return function(e){return e===n}}function kf(c){return{elementFromPoint:function(e,n){return un.fromPoint(un.fromDom(c.document),e,n)},getRect:function(e){return e.dom().getBoundingClientRect()},getRangedRect:function(e,n,t,r){var o=El.exact(e,n,t,r);return Ka(c,o).map(_f)},getSelection:function(){return Ya(c).map(function(e){return Hl.convertToRange(c,e)})},fromSitus:function(e){var n=El.relative(e.start(),e.finish());return Hl.convertToRange(c,n)},situsFromPoint:function(e,n){return Xa(c,e,n).map(function(e){return zl(e.start(),e.soffset(),e.finish(),e.foffset())})},clearSelection:function(){!function(e){e.getSelection().removeAllRanges()}(c)},collapseSelection:function(u){void 0===u&&(u=!1),Ya(c).each(function(e){return e.fold(function(e){return e.collapse(u)},function(e,n){var t=u?e:n;Ua(c,t,t)},function(e,n,t,r){var o=u?e:t,i=u?n:r;Fa(c,o,i,o,i)})})},setSelection:function(e){Fa(c,e.start(),e.soffset(),e.finish(),e.foffset())},setRelativeSelection:function(e,n){Ua(c,e,n)},selectContents:function(e){Ga(c,e)},getInnerHeight:function(){return c.innerHeight},getScrollY:function(){return function(e){var n=e!==undefined?e.dom():f.document,t=n.body.scrollLeft||n.documentElement.scrollLeft,r=n.body.scrollTop||n.documentElement.scrollTop;return so(t,r)}(un.fromDom(c.document)).top()},scrollBy:function(e,n){!function(e,n,t){(t!==undefined?t.dom():f.document).defaultView.scrollBy(e,n)}(e,n,un.fromDom(c.document))}}}function If(n,e){p(e,function(e){!function(e,n){So(e)?e.dom().classList.remove(n):Ro(e,n);Oo(e)}(n,e)})}var Bf={down:{traverse:Ce,gather:rl,relative:Dl.before,otherRetry:bf.tryDown,ieRetry:bf.ieTryDown,failure:rf.failedDown},up:{traverse:ye,gather:tl,relative:Dl.before,otherRetry:bf.tryUp,ieRetry:bf.ieTryUp,failure:rf.failedUp}},Pf=Nf(38),Mf=Nf(40),Wf={ltr:{isBackward:Nf(37),isForward:Nf(39)},rtl:{isBackward:Nf(39),isForward:Nf(37)},isUp:Pf,isDown:Mf,isNavigation:function(e){return 37<=e&&e<=40}},_f=function(e){return{left:e.left(),top:e.top(),right:e.right(),bottom:e.bottom(),width:e.width(),height:e.height()}},Lf=(ge().browser.isSafari(),P("rows","cols")),jf={mouse:function(e,n,t,r){var o=function c(o,i,n,u){function t(){r=We.none()}var r=We.none();return{mousedown:function(e){u.clear(i),r=ml(e.target(),n)},mouseover:function(e){r.each(function(r){u.clearBeforeUpdate(i),ml(e.target(),n).each(function(t){gr(r,t,n).each(function(e){var n=e.boxes().getOr([]);(1<n.length||1===n.length&&!Pn(r,t))&&(u.selectRange(i,n,e.start(),e.finish()),o.selectContents(t))})})})},mouseup:function(e){r.each(t)}}}(kf(e),n,t,r);return{mousedown:o.mousedown,mouseover:o.mouseover,mouseup:o.mouseup}},keyboard:function(e,l,f,s){function d(){return s.clear(l),We.none()}var m=kf(e);return{keydown:function(e,n,t,r,o,i){var u=e.raw(),c=u.which,a=!0===u.shiftKey;return pr(l,s.selectedSelector()).fold(function(){return Wf.isDown(c)&&a?b(Df,m,l,f,Bf.down,r,n,s.selectRange):Wf.isUp(c)&&a?b(Df,m,l,f,Bf.up,r,n,s.selectRange):Wf.isDown(c)?b(Of,m,f,Bf.down,r,n,Ef):Wf.isUp(c)?b(Of,m,f,Bf.up,r,n,Af):We.none},function(n){function e(e){return function(){return N(e,function(e){return Ul.update(e.rows(),e.cols(),l,n,s)}).fold(function(){return vr(l,s.firstSelectedSelector(),s.lastSelectedSelector()).map(function(e){var n=Wf.isDown(c)||i.isForward(c)?Dl.after:Dl.before;return m.setRelativeSelection(Dl.on(e.first(),0),n(e.table())),s.clear(l),jl.create(We.none(),!0)})},function(e){return We.some(jl.create(We.none(),!0))})}}return Wf.isDown(c)&&a?e([Lf(1,0)]):Wf.isUp(c)&&a?e([Lf(-1,0)]):i.isBackward(c)&&a?e([Lf(0,-1),Lf(-1,0)]):i.isForward(c)&&a?e([Lf(0,1),Lf(1,0)]):Wf.isNavigation(c)&&!1==a?d:We.none})()},keyup:function(t,r,o,i,u){return pr(l,s.selectedSelector()).fold(function(){var e=t.raw(),n=e.which;return!1==(!0===e.shiftKey)?We.none():Wf.isNavigation(n)?Ul.sync(l,f,r,o,i,u,s.selectRange):We.none()},We.none)}}},external:function(e,r,n,o){var i=kf(e);return function(e,t){o.clearBeforeUpdate(r),gr(e,t,n).each(function(e){var n=e.boxes().getOr([]);o.selectRange(r,n,e.start(),e.finish()),i.selectContents(t),i.collapseSelection()})}}},zf={byClass:function(o){function i(e){var n=Pe(e,o.selectedSelector());p(n,t)}var u=function(n){return function(e){To(e,n)}}(o.selected()),t=function(n){return function(e){If(e,n)}}([o.selected(),o.lastSelected(),o.firstSelected()]);return{clearBeforeUpdate:i,clear:i,selectRange:function(e,n,t,r){i(e),p(n,u),To(t,o.firstSelected()),To(r,o.lastSelected())},selectedSelector:o.selectedSelector,firstSelectedSelector:o.firstSelectedSelector,lastSelectedSelector:o.lastSelectedSelector}},byAttr:function(o,i,n){function t(e){K(e,o.selected()),K(e,o.firstSelected()),K(e,o.lastSelected())}function u(e){q(e,o.selected(),"1")}function c(e){r(e),n()}var r=function(e){var n=Pe(e,o.selectedSelector());p(n,t)};return{clearBeforeUpdate:r,clear:c,selectRange:function(e,n,t,r){c(e),p(n,u),q(t,o.firstSelected(),"1"),q(r,o.lastSelected(),"1"),i(n,t,r)},selectedSelector:o.selectedSelector,firstSelectedSelector:o.firstSelectedSelector,lastSelectedSelector:o.lastSelectedSelector}}},Hf={getOtherCells:function(e,n,t){var r=dt(e),o=pt.generate(r);return Uo(o,n).map(function(e){var n=bu(o,t,!1);return{upOrLeftCells:function(e,t,n){var r=e.slice(0,t[t.length-1].row()+1),o=Lo(r,n);return x(o,function(e){var n=e.cells().slice(0,t[t.length-1].column()+1);return g(n,function(e){return e.element()})})}(n,e,t),downOrRightCells:function(e,t,n){var r=e.slice(t[0].row()+t[0].rowspan()-1,e.length),o=Lo(r,n);return x(o,function(e){var n=e.cells().slice(t[0].column()+t[0].colspan()-1,+e.cells().length);return g(n,function(e){return e.element()})})}(n,e,t)}})}},Ff=function(e){return!1===Do(un.fromDom(e.target),"ephox-snooker-resizer-bar")};function Uf(w,y,e){var C=$e(["mousedown","mouseover","mouseup","keyup","keydown"],[]),S=We.none(),a=Sc(w),x=zf.byAttr(xr,function(i,u,c){e.targets().each(function(o){st.table(u).each(function(e){var n=un.fromDom(w.getDoc()),t=Lt.cellOperations(T,n,a),r=Hf.getOtherCells(e,o,t);xc(w,i,u,c,r)})})},function(){Rc(w)});w.on("init",function(e){var r=w.getWin(),o=gc(w),n=pc(w),t=jf.mouse(r,o,n,x),c=jf.keyboard(r,o,n,x),i=jf.external(r,o,n,x);w.on("TableSelectorChange",function(e){i(e.start,e.finish)});function a(e,n){!function(e){return!0===e.raw().shiftKey}(e)||(n.kill()&&e.kill(),n.selection().each(function(e){var n=El.relative(e.start(),e.finish()),t=Il(r,n);w.selection.setRng(t)}))}function u(e){var n=v(e);if(n.raw().shiftKey&&Wf.isNavigation(n.raw().which)){var t=w.selection.getRng(),r=un.fromDom(t.startContainer),o=un.fromDom(t.endContainer);c.keyup(n,r,t.startOffset,o,t.endOffset).each(function(e){a(n,e)})}}function l(e){var n=v(e);y().each(function(e){e.hideBars()});var t=w.selection.getRng(),r=un.fromDom(w.selection.getStart()),o=un.fromDom(t.startContainer),i=un.fromDom(t.endContainer),u=bc.directionAt(r).isRtl()?Wf.rtl:Wf.ltr;c.keydown(n,o,t.startOffset,i,t.endOffset,u).each(function(e){a(n,e)}),y().each(function(e){e.showBars()})}function f(e){return e.hasOwnProperty("x")&&e.hasOwnProperty("y")}function s(e){return 0===e.button}function d(e){s(e)&&Ff(e)&&t.mousedown(v(e))}function m(e){(function(e){return e.buttons===undefined||0!=(1&e.buttons)})(e)&&Ff(e)&&t.mouseover(v(e))}function g(e){s(e)&&Ff(e)&&t.mouseup(v(e))}var p,h,v=function(e){function n(){e.stopPropagation()}function t(){e.preventDefault()}var r=un.fromDom(e.target),o=O(t,n);return{target:D(r),x:D(f(e)?e.x:null),y:D(f(e)?e.y:null),stop:n,prevent:t,kill:o,raw:D(e)}},b=(p=R(un.fromDom(o)),h=R(0),{touchEnd:function(e){var n=un.fromDom(e.target);if("td"===nn(n)||"th"===nn(n)){var t=p.get(),r=h.get();Pn(t,n)&&e.timeStamp-r<300&&(e.preventDefault(),i(n,n))}p.set(n),h.set(e.timeStamp)}});w.on("mousedown",d),w.on("mouseover",m),w.on("mouseup",g),w.on("touchend",b.touchEnd),w.on("keyup",u),w.on("keydown",l),w.on("NodeChange",function(){var e=w.selection,n=un.fromDom(e.getStart()),t=un.fromDom(e.getEnd());dr.sharedOne(st.table,[n,t]).fold(function(){x.clear(o)},T)}),S=We.some(C({mousedown:d,mouseover:m,mouseup:g,keyup:u,keydown:l}))});return{clear:x.clear,destroy:function(){S.each(function(e){})}}}var qf=function(n){return{get:function(){var e=gc(n);return br(e,xr.selectedSelector()).fold(function(){return n.selection.getStart()===undefined?Or.none():Or.single(n.selection)},function(e){return Or.multiple(e)})}}},Vf=function(e,t){function n(){return ta(e).bind(function(n){return st.table(n).map(function(e){return"caption"===nn(n)?kr.notCell(n):kr.forMenu(t,e,n)})})}function r(){i.set(ee(n)()),p(u.get(),function(e){return e()})}function o(n,t){function r(){return i.get().fold(function(){n.setDisabled(!0)},function(e){n.setDisabled(t(e))})}return r(),u.set(u.get().concat([r])),function(){u.set(h(u.get(),function(e){return e!==r}))}}var i=R(We.none()),u=R([]);return e.on("NodeChange TableSelectorChange",r),{onSetupTable:function(e){return o(e,function(e){return!1})},onSetupCellOrRow:function(e){return o(e,function(e){return"caption"===nn(e.element())})},onSetupMergeable:function(e){return o(e,function(e){return e.mergable().isNone()})},onSetupUnmergeable:function(e){return o(e,function(e){return e.unmergable().isNone()})},resetTargets:r,targets:function(){return i.get()}}},Gf={addButtons:function(n,e){n.ui.registry.addMenuButton("table",{tooltip:"Table",icon:"table",fetch:function(e){return e("inserttable | cell row column | advtablesort | tableprops deletetable")}});function t(e){return function(){return n.execCommand(e)}}n.ui.registry.addButton("tableprops",{tooltip:"Table properties",onAction:t("mceTableProps"),icon:"table",onSetup:e.onSetupTable}),n.ui.registry.addButton("tabledelete",{tooltip:"Delete table",onAction:t("mceTableDelete"),icon:"table-delete-table",onSetup:e.onSetupTable}),n.ui.registry.addButton("tablecellprops",{tooltip:"Cell properties",onAction:t("mceTableCellProps"),icon:"table-cell-properties",onSetup:e.onSetupCellOrRow}),n.ui.registry.addButton("tablemergecells",{tooltip:"Merge cells",onAction:t("mceTableMergeCells"),icon:"table-merge-cells",onSetup:e.onSetupMergeable}),n.ui.registry.addButton("tablesplitcells",{tooltip:"Split cell",onAction:t("mceTableSplitCells"),icon:"table-split-cells",onSetup:e.onSetupUnmergeable}),n.ui.registry.addButton("tableinsertrowbefore",{tooltip:"Insert row before",onAction:t("mceTableInsertRowBefore"),icon:"table-insert-row-above",onSetup:e.onSetupCellOrRow}),n.ui.registry.addButton("tableinsertrowafter",{tooltip:"Insert row after",onAction:t("mceTableInsertRowAfter"),icon:"table-insert-row-after",onSetup:e.onSetupCellOrRow}),n.ui.registry.addButton("tabledeleterow",{tooltip:"Delete row",onAction:t("mceTableDeleteRow"),icon:"table-delete-row",onSetup:e.onSetupCellOrRow}),n.ui.registry.addButton("tablerowprops",{tooltip:"Row properties",onAction:t("mceTableRowProps"),icon:"table-row-properties",onSetup:e.onSetupCellOrRow}),n.ui.registry.addButton("tableinsertcolbefore",{tooltip:"Insert column before",onAction:t("mceTableInsertColBefore"),icon:"table-insert-column-before",onSetup:e.onSetupCellOrRow}),n.ui.registry.addButton("tableinsertcolafter",{tooltip:"Insert column after",onAction:t("mceTableInsertColAfter"),icon:"table-insert-column-after",onSetup:e.onSetupCellOrRow}),n.ui.registry.addButton("tabledeletecol",{tooltip:"Delete column",onAction:t("mceTableDeleteCol"),icon:"table-delete-column",onSetup:e.onSetupCellOrRow}),n.ui.registry.addButton("tablecutrow",{tooltip:"Cut row",onAction:t("mceTableCutRow"),icon:"temporary-placeholder",onSetup:e.onSetupCellOrRow}),n.ui.registry.addButton("tablecopyrow",{tooltip:"Copy row",onAction:t("mceTableCopyRow"),icon:"temporary-placeholder",onSetup:e.onSetupCellOrRow}),n.ui.registry.addButton("tablepasterowbefore",{tooltip:"Paste row before",onAction:t("mceTablePasteRowBefore"),icon:"temporary-placeholder",onSetup:e.onSetupCellOrRow}),n.ui.registry.addButton("tablepasterowafter",{tooltip:"Paste row after",onAction:t("mceTablePasteRowAfter"),icon:"temporary-placeholder",onSetup:e.onSetupCellOrRow}),n.ui.registry.addButton("tableinsertdialog",{tooltip:"Insert table",onAction:t("mceInsertTable"),icon:"table"})},addToolbars:function(n){var e=function(e){return e.getParam("table_toolbar","tableprops tabledelete | tableinsertrowbefore tableinsertrowafter tabledeleterow | tableinsertcolbefore tableinsertcolafter tabledeletecol")}(n);0<e.length&&n.ui.registry.addContextToolbar("table",{predicate:function(e){return n.dom.is(e,"table")&&n.getBody().contains(e)},items:e,scope:"node",position:"node"})}},Yf={addMenuItems:function(r,e){function n(e){return function(){return r.execCommand(e)}}function t(e){var n=e.numRows,t=e.numColumns;r.undoManager.transact(function(){Jc(r,t,n)}),r.addVisual()}var o={text:"Table properties",onSetup:e.onSetupTable,onAction:n("mceTableProps")},i={text:"Delete table",icon:"table-delete-table",onSetup:e.onSetupTable,onAction:n("mceTableDelete")},u=[{type:"menuitem",text:"Insert row before",icon:"table-insert-row-above",onAction:n("mceTableInsertRowBefore"),onSetup:e.onSetupCellOrRow},{type:"menuitem",text:"Insert row after",icon:"table-insert-row-after",onAction:n("mceTableInsertRowAfter"),onSetup:e.onSetupCellOrRow},{type:"menuitem",text:"Delete row",icon:"table-delete-row",onAction:n("mceTableDeleteRow"),onSetup:e.onSetupCellOrRow},{type:"menuitem",text:"Row properties",icon:"table-row-properties",onAction:n("mceTableRowProps"),onSetup:e.onSetupCellOrRow},{type:"separator"},{type:"menuitem",text:"Cut row",onAction:n("mceTableCutRow"),onSetup:e.onSetupCellOrRow},{type:"menuitem",text:"Copy row",onAction:n("mceTableCopyRow"),onSetup:e.onSetupCellOrRow},{type:"menuitem",text:"Paste row before",onAction:n("mceTablePasteRowBefore"),onSetup:e.onSetupCellOrRow},{type:"menuitem",text:"Paste row after",onAction:n("mceTablePasteRowAfter"),onSetup:e.onSetupCellOrRow}],c={type:"nestedmenuitem",text:"Row",getSubmenuItems:function(){return u}},a=[{type:"menuitem",text:"Insert column before",icon:"table-insert-column-before",onAction:n("mceTableInsertColBefore"),onSetup:e.onSetupCellOrRow},{type:"menuitem",text:"Insert column after",icon:"table-insert-column-after",onAction:n("mceTableInsertColAfter"),onSetup:e.onSetupCellOrRow},{type:"menuitem",text:"Delete column",icon:"table-delete-column",onAction:n("mceTableDeleteCol"),onSetup:e.onSetupCellOrRow}],l={type:"nestedmenuitem",text:"Column",getSubmenuItems:function(){return a}},f=[{type:"menuitem",text:"Cell properties",icon:"table-cell-properties",onAction:n("mceTableCellProps"),onSetup:e.onSetupCellOrRow},{type:"menuitem",text:"Merge cells",icon:"table-merge-cells",onAction:n("mceTableMergeCells"),onSetup:e.onSetupMergeable},{type:"menuitem",text:"Split cell",icon:"table-split-cells",onAction:n("mceTableSplitCells"),onSetup:e.onSetupUnmergeable}],s={type:"nestedmenuitem",text:"Cell",getSubmenuItems:function(){return f}};!1===function(e){return e.getParam("table_grid",!0,"boolean")}(r)?r.ui.registry.addMenuItem("inserttable",{text:"Table",icon:"table",onAction:n("mceInsertTable")}):r.ui.registry.addNestedMenuItem("inserttable",{text:"Table",icon:"table",getSubmenuItems:function(){return[{type:"fancymenuitem",fancytype:"inserttable",onAction:t}]}}),r.ui.registry.addMenuItem("inserttabledialog",{text:"Insert table",icon:"table",onAction:n("mceInsertTable")}),r.ui.registry.addMenuItem("tableprops",o),r.ui.registry.addMenuItem("deletetable",i),r.ui.registry.addNestedMenuItem("row",c),r.ui.registry.addNestedMenuItem("column",l),r.ui.registry.addNestedMenuItem("cell",s),r.ui.registry.addContextMenu("table",{update:function(){return e.resetTargets(),e.targets().fold(function(){return""},function(e){return"caption"===nn(e.element())?"tableprops deletetable":"cell row column | advtablesort | tableprops deletetable"})}})}},Kf=function(t,n,e,r){return{insertTable:function(e,n){return Jc(t,e,n)},setClipboardRows:function(e){return function(e,n){var t=g(e,un.fromDom);n.set(We.from(t))}(e,n)},getClipboardRows:function(){return function(e){return e.get().fold(function(){},function(e){return g(e,function(e){return e.dom()})})}(n)},resizeHandler:e,selectionTargets:r}};function Xf(n){var e=qf(n),t=Vf(n,e),r=yl(n),o=Uf(n,r.lazyResize,t),i=Tc(n,r.lazyWire),u=R(We.none());return oa.registerCommands(n,i,o,e,u),Ir.registerEvents(n,e,i,o),Yf.addMenuItems(n,t),Gf.addButtons(n,t),Gf.addToolbars(n),n.on("PreInit",function(){n.serializer.addTempAttr(xr.firstSelected()),n.serializer.addTempAttr(xr.lastSelected())}),Cc(n)&&n.on("keydown",function(e){Ll.handle(e,n,i,r.lazyWire)}),n.on("remove",function(){r.destroy(),o.destroy()}),Kf(n,u,r,t)}!function Jf(){_e.add("table",Xf)}()}(window);