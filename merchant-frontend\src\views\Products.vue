<template>
  <div class="products-page">
    <el-card>
      <template #header>
        <div class="page-header">
          <h2>商品管理</h2>
          <el-button type="primary" @click="addProduct">
            <el-icon><Plus /></el-icon>
            添加商品
          </el-button>
        </div>
      </template>
      
      <div class="content">
        <p>商品管理功能开发中...</p>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const addProduct = () => {
  router.push('/products/add')
}
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.content {
  padding: 20px;
  text-align: center;
  color: #666;
}
</style>
