<template>
  <div class="products-page">
    <el-card>
      <template #header>
        <div class="page-header">
          <h2>商品管理</h2>
          <el-button type="primary" @click="addProduct">
            <el-icon><Plus /></el-icon>
            添加商品
          </el-button>
        </div>
      </template>

      <!-- 搜索和筛选 -->
      <div class="search-section">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-input
              v-model="searchForm.name"
              placeholder="搜索商品名称"
              clearable
              @change="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="4">
            <el-select
              v-model="searchForm.category"
              placeholder="商品分类"
              clearable
              @change="handleSearch"
            >
              <el-option label="全部分类" value="" />
              <el-option label="蔬菜类" value="vegetables" />
              <el-option label="水果类" value="fruits" />
              <el-option label="粮食类" value="grains" />
              <el-option label="肉类" value="meat" />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select
              v-model="searchForm.status"
              placeholder="商品状态"
              clearable
              @change="handleSearch"
            >
              <el-option label="全部状态" value="" />
              <el-option label="上架中" value="1" />
              <el-option label="已下架" value="0" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 商品列表 -->
      <el-table
        v-loading="loading"
        :data="productList"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="商品图片" width="100">
          <template #default="{ row }">
            <el-image
              :src="row.image || '/placeholder-product.jpg'"
              :preview-src-list="[row.image]"
              style="width: 60px; height: 60px"
              fit="cover"
            />
          </template>
        </el-table-column>
        <el-table-column prop="name" label="商品名称" min-width="150" />
        <el-table-column prop="category" label="分类" width="100">
          <template #default="{ row }">
            <el-tag>{{ getCategoryName(row.category) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="price" label="价格" width="100">
          <template #default="{ row }">
            <span class="price">¥{{ row.price }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="stock" label="库存" width="80">
          <template #default="{ row }">
            <span :class="{ 'low-stock': row.stock < 10 }">{{ row.stock }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="sales" label="销量" width="80" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-switch
              v-model="row.status"
              :active-value="1"
              :inactive-value="0"
              @change="handleStatusChange(row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="150">
          <template #default="{ row }">
            {{ formatDate(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="editProduct(row)">
              编辑
            </el-button>
            <el-button type="warning" size="small" @click="viewProduct(row)">
              查看
            </el-button>
            <el-button type="danger" size="small" @click="deleteProduct(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 批量操作 -->
      <div class="batch-actions" v-if="selectedProducts.length > 0">
        <el-alert
          :title="`已选择 ${selectedProducts.length} 个商品`"
          type="info"
          show-icon
          :closable="false"
        />
        <div class="actions">
          <el-button type="success" @click="batchOnline">批量上架</el-button>
          <el-button type="warning" @click="batchOffline">批量下架</el-button>
          <el-button type="danger" @click="batchDelete">批量删除</el-button>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search } from '@element-plus/icons-vue'
import { createApiClient, merchantApi } from '../../../shared/api.js'
import { formatDate } from '../../../shared/utils.js'

const router = useRouter()
const api = createApiClient()

// 响应式数据
const loading = ref(false)
const productList = ref([])
const selectedProducts = ref([])

// 搜索表单
const searchForm = reactive({
  name: '',
  category: '',
  status: ''
})

// 分页数据
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 分类映射
const categoryMap = {
  vegetables: '蔬菜类',
  fruits: '水果类',
  grains: '粮食类',
  meat: '肉类'
}

// 获取分类名称
const getCategoryName = (category) => {
  return categoryMap[category] || category
}

// 模拟商品数据
const mockProductList = [
  {
    id: 1,
    name: '新鲜有机西红柿',
    category: 'vegetables',
    price: 12.80,
    stock: 150,
    sales: 89,
    status: 1,
    image: 'https://via.placeholder.com/60x60?text=西红柿',
    createTime: '2025-07-01 10:30:00'
  },
  {
    id: 2,
    name: '优质苹果',
    category: 'fruits',
    price: 8.50,
    stock: 5,
    sales: 156,
    status: 1,
    image: 'https://via.placeholder.com/60x60?text=苹果',
    createTime: '2025-07-02 14:20:00'
  },
  {
    id: 3,
    name: '东北大米',
    category: 'grains',
    price: 25.00,
    stock: 80,
    sales: 45,
    status: 0,
    image: 'https://via.placeholder.com/60x60?text=大米',
    createTime: '2025-07-03 09:15:00'
  },
  {
    id: 4,
    name: '新鲜猪肉',
    category: 'meat',
    price: 35.00,
    stock: 25,
    sales: 78,
    status: 1,
    image: 'https://via.placeholder.com/60x60?text=猪肉',
    createTime: '2025-07-04 16:45:00'
  }
]

// 获取商品列表
const getProductList = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      ...searchForm
    }

    const response = await api.get(merchantApi.products.list, { params })

    if (response.data.code === 200) {
      productList.value = response.data.data.records || mockProductList
      pagination.total = response.data.data.total || mockProductList.length
    } else {
      // 使用模拟数据
      productList.value = mockProductList
      pagination.total = mockProductList.length
    }
  } catch (error) {
    console.error('获取商品列表失败:', error)
    // 使用模拟数据
    productList.value = mockProductList
    pagination.total = mockProductList.length
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  pagination.page = 1
  getProductList()
}

// 重置搜索
const resetSearch = () => {
  Object.assign(searchForm, {
    name: '',
    category: '',
    status: ''
  })
  handleSearch()
}

// 选择变化
const handleSelectionChange = (selection) => {
  selectedProducts.value = selection
}

// 状态变化
const handleStatusChange = async (row) => {
  try {
    const response = await api.put(merchantApi.products.updateStatus, {
      id: row.id,
      status: row.status
    })

    if (response.data.code === 200) {
      ElMessage.success(row.status ? '商品已上架' : '商品已下架')
    } else {
      // 回滚状态
      row.status = row.status ? 0 : 1
      ElMessage.error('状态更新失败')
    }
  } catch (error) {
    console.error('更新状态失败:', error)
    // 回滚状态
    row.status = row.status ? 0 : 1
    ElMessage.error('状态更新失败')
  }
}

// 分页处理
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.page = 1
  getProductList()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  getProductList()
}

// 操作方法
const addProduct = () => {
  router.push('/products/add')
}

const editProduct = (row) => {
  router.push(`/products/edit/${row.id}`)
}

const viewProduct = (row) => {
  router.push(`/products/view/${row.id}`)
}

const deleteProduct = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除商品"${row.name}"吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await api.delete(`${merchantApi.products.delete}/${row.id}`)

    if (response.data.code === 200) {
      ElMessage.success('删除成功')
      getProductList()
    } else {
      ElMessage.error('删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 批量操作
const batchOnline = async () => {
  const ids = selectedProducts.value.map(item => item.id)
  try {
    const response = await api.put(merchantApi.products.batchUpdateStatus, {
      ids,
      status: 1
    })

    if (response.data.code === 200) {
      ElMessage.success('批量上架成功')
      getProductList()
    } else {
      ElMessage.error('批量上架失败')
    }
  } catch (error) {
    console.error('批量上架失败:', error)
    ElMessage.error('批量上架失败')
  }
}

const batchOffline = async () => {
  const ids = selectedProducts.value.map(item => item.id)
  try {
    const response = await api.put(merchantApi.products.batchUpdateStatus, {
      ids,
      status: 0
    })

    if (response.data.code === 200) {
      ElMessage.success('批量下架成功')
      getProductList()
    } else {
      ElMessage.error('批量下架失败')
    }
  } catch (error) {
    console.error('批量下架失败:', error)
    ElMessage.error('批量下架失败')
  }
}

const batchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedProducts.value.length} 个商品吗？`,
      '批量删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const ids = selectedProducts.value.map(item => item.id)
    const response = await api.delete(merchantApi.products.batchDelete, {
      data: { ids }
    })

    if (response.data.code === 200) {
      ElMessage.success('批量删除成功')
      getProductList()
    } else {
      ElMessage.error('批量删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

// 组件挂载时获取数据
onMounted(() => {
  getProductList()
})
</script>

<style scoped>
.products-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0;
}

.search-section {
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 4px;
  margin-bottom: 20px;
}

.price {
  color: #e6a23c;
  font-weight: bold;
}

.low-stock {
  color: #f56c6c;
  font-weight: bold;
}

.batch-actions {
  margin: 20px 0;
  padding: 15px;
  background-color: #f0f9ff;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.batch-actions .actions {
  display: flex;
  gap: 10px;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

:deep(.el-table .el-table__cell) {
  padding: 8px 0;
}

:deep(.el-image) {
  border-radius: 4px;
}
</style>
