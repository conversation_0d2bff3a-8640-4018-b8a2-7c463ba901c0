<template>
  <div class="dashboard">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon user-icon">
              <el-icon><User /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.totalUsers }}</div>
              <div class="stats-label">总用户数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon merchant-icon">
              <el-icon><Shop /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.totalMerchants }}</div>
              <div class="stats-label">总商家数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon order-icon">
              <el-icon><ShoppingCart /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.totalOrders }}</div>
              <div class="stats-label">总订单数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon revenue-icon">
              <el-icon><Money /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">¥{{ stats.totalRevenue }}</div>
              <div class="stats-label">总交易额</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>用户增长趋势</span>
          </template>
          <div ref="userChartRef" class="chart-container"></div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>订单统计</span>
          </template>
          <div ref="orderChartRef" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 最近活动 -->
    <el-row :gutter="20" class="activity-row">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>待审核商家</span>
          </template>
          <el-table :data="pendingMerchants" style="width: 100%">
            <el-table-column prop="name" label="商家名称" />
            <el-table-column prop="contact" label="联系人" />
            <el-table-column prop="submitTime" label="提交时间" />
            <el-table-column label="操作" width="120">
              <template #default="scope">
                <el-button type="primary" size="small" @click="handleApprove(scope.row)">
                  审核
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>系统日志</span>
          </template>
          <el-timeline>
            <el-timeline-item
              v-for="log in systemLogs"
              :key="log.id"
              :timestamp="log.time"
              :type="log.type"
            >
              {{ log.content }}
            </el-timeline-item>
          </el-timeline>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
// import { createApiClient, adminApi } from '../../../shared/api.js'
import axios from 'axios'

// 临时API配置
const createApiClient = () => {
  return axios.create({
    baseURL: 'http://localhost:8080/nongchanpinxiaoshou',
    timeout: 10000
  })
}

const api = createApiClient()

const userChartRef = ref(null)
const orderChartRef = ref(null)
let userChart = null
let orderChart = null

// 统计数据
const stats = reactive({
  totalUsers: '12,580',
  totalMerchants: '1,245',
  totalOrders: '8,960',
  totalRevenue: '2,580,000'
})

// 待审核商家
const pendingMerchants = ref([
  {
    id: 1,
    name: '绿色农场',
    contact: '张三',
    submitTime: '2025-07-07 10:30'
  },
  {
    id: 2,
    name: '有机蔬菜园',
    contact: '李四',
    submitTime: '2025-07-07 09:15'
  },
  {
    id: 3,
    name: '山区果园',
    contact: '王五',
    submitTime: '2025-07-06 16:45'
  }
])

// 系统日志
const systemLogs = ref([
  {
    id: 1,
    content: '用户 user123 注册成功',
    time: '2025-07-07 14:30',
    type: 'success'
  },
  {
    id: 2,
    content: '商家 merchant456 提交审核申请',
    time: '2025-07-07 14:15',
    type: 'warning'
  },
  {
    id: 3,
    content: '系统定时任务执行完成',
    time: '2025-07-07 14:00',
    type: 'info'
  },
  {
    id: 4,
    content: '订单 order789 支付成功',
    time: '2025-07-07 13:45',
    type: 'success'
  }
])

// 初始化用户增长图表
const initUserChart = () => {
  if (!userChartRef.value) return
  
  userChart = echarts.init(userChartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月']
    },
    yAxis: {
      type: 'value'
    },
    series: [{
      data: [1200, 1800, 2400, 3200, 4100, 5300, 6800],
      type: 'line',
      smooth: true,
      itemStyle: {
        color: '#409eff'
      }
    }]
  }
  
  userChart.setOption(option)
}

// 初始化订单统计图表
const initOrderChart = () => {
  if (!orderChartRef.value) return
  
  orderChart = echarts.init(orderChartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'item'
    },
    series: [{
      type: 'pie',
      radius: '60%',
      data: [
        { value: 1048, name: '已完成' },
        { value: 735, name: '进行中' },
        { value: 580, name: '已取消' },
        { value: 484, name: '待支付' }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  }
  
  orderChart.setOption(option)
}

// 处理商家审核
const handleApprove = (merchant) => {
  ElMessage.info(`审核商家：${merchant.name}`)
}

// 加载数据
const loadData = async () => {
  try {
    // 这里可以调用实际的API
    // const response = await api.get(adminApi.system.dashboard)
    // 暂时使用模拟数据
  } catch (error) {
    console.error('加载数据失败:', error)
  }
}

onMounted(async () => {
  await loadData()
  await nextTick()
  initUserChart()
  initOrderChart()
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    userChart?.resize()
    orderChart?.resize()
  })
})

onUnmounted(() => {
  userChart?.dispose()
  orderChart?.dispose()
  window.removeEventListener('resize', () => {})
})
</script>

<style scoped>
.dashboard {
  padding: 0;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  height: 120px;
}

.stats-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 24px;
  color: white;
}

.user-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.merchant-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.order-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.revenue-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 5px;
}

.stats-label {
  font-size: 14px;
  color: #909399;
}

.charts-row {
  margin-bottom: 20px;
}

.chart-container {
  height: 300px;
}

.activity-row {
  margin-bottom: 20px;
}

:deep(.el-card__header) {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

:deep(.el-timeline-item__timestamp) {
  font-size: 12px;
  color: #909399;
}
</style>
