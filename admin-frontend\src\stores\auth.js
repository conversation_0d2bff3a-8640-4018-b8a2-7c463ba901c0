import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { createApiClient, apiMethods } from '../../../shared/api.js'

export const useAuthStore = defineStore('auth', () => {
  const token = ref(localStorage.getItem('admin_token') || '')
  const userInfo = ref(JSON.parse(localStorage.getItem('admin_user') || '{}'))
  
  const isAuthenticated = computed(() => !!token.value)

  // 登录
  const login = async (credentials) => {
    try {
      const apiClient = createApiClient()
      const response = await apiMethods.auth.login(apiClient, 'admin', {
        username: credentials.username,
        password: credentials.password,
        userType: 'admin'
      })

      if (response.code === 0) {
        token.value = response.data.token
        userInfo.value = response.data.userInfo

        localStorage.setItem('admin_token', token.value)
        localStorage.setItem('admin_user', JSON.stringify(userInfo.value))

        return { success: true }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      console.error('登录失败:', error)
      return { success: false, message: '登录失败，请检查网络连接' }
    }
  }
  
  // 登出
  const logout = async () => {
    try {
      await api.post(adminApi.auth.logout)
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      token.value = ''
      userInfo.value = {}
      localStorage.removeItem('admin_token')
      localStorage.removeItem('admin_user')
    }
  }
  
  // 获取用户信息
  const getUserInfo = async () => {
    try {
      const response = await api.get(adminApi.auth.profile)
      if (response.code === 200) {
        userInfo.value = response.data
        localStorage.setItem('admin_user', JSON.stringify(userInfo.value))
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
    }
  }
  
  return {
    token,
    userInfo,
    isAuthenticated,
    login,
    logout,
    getUserInfo
  }
})
