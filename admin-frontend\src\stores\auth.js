import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
// import { createApiClient, adminApi } from '../../../shared/api.js'
import axios from 'axios'

// 临时API配置
const API_BASE_URL = 'http://localhost:8080/nongchanpin<PERSON><PERSON><PERSON>'
const createApiClient = () => {
  return axios.create({
    baseURL: API_BASE_URL,
    timeout: 10000,
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

const adminApi = {
  auth: {
    login: '/api/auth/login',
    logout: '/api/auth/logout',
    profile: '/api/admin/profile'
  }
}

export const useAuthStore = defineStore('auth', () => {
  const token = ref(localStorage.getItem('admin_token') || '')
  const userInfo = ref(JSON.parse(localStorage.getItem('admin_user') || '{}'))
  
  const isAuthenticated = computed(() => !!token.value)
  
  const api = createApiClient()
  
  // 登录
  const login = async (credentials) => {
    try {
      const response = await api.post(adminApi.auth.login, {
        ...credentials,
        role: 'admin'
      })
      
      if (response.code === 200) {
        token.value = response.data.token
        userInfo.value = response.data.user
        
        localStorage.setItem('admin_token', token.value)
        localStorage.setItem('admin_user', JSON.stringify(userInfo.value))
        
        return { success: true }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      console.error('登录失败:', error)
      return { success: false, message: '登录失败，请检查网络连接' }
    }
  }
  
  // 登出
  const logout = async () => {
    try {
      await api.post(adminApi.auth.logout)
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      token.value = ''
      userInfo.value = {}
      localStorage.removeItem('admin_token')
      localStorage.removeItem('admin_user')
    }
  }
  
  // 获取用户信息
  const getUserInfo = async () => {
    try {
      const response = await api.get(adminApi.auth.profile)
      if (response.code === 200) {
        userInfo.value = response.data
        localStorage.setItem('admin_user', JSON.stringify(userInfo.value))
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
    }
  }
  
  return {
    token,
    userInfo,
    isAuthenticated,
    login,
    logout,
    getUserInfo
  }
})
