{"version": 3, "file": "utils.mjs", "sources": ["../../../../../../packages/components/popper/src/utils.ts"], "sourcesContent": ["import { unrefElement } from '@vueuse/core'\nimport { isClient } from '@element-plus/utils'\n\nimport type { ComponentPublicInstance } from 'vue'\nimport type { MaybeRef } from '@vueuse/core'\nimport type { Modifier } from '@popperjs/core'\nimport type { Measurable } from './constants'\nimport type { PopperCoreConfigProps } from './content'\n\nexport const buildPopperOptions = (\n  props: PopperCoreConfigProps,\n  modifiers: Modifier<any, any>[] = []\n) => {\n  const { placement, strategy, popperOptions } = props\n  const options = {\n    placement,\n    strategy,\n    ...popperOptions,\n    modifiers: [...genModifiers(props), ...modifiers],\n  }\n\n  deriveExtraModifiers(options, popperOptions?.modifiers)\n  return options\n}\n\nexport const unwrapMeasurableEl = (\n  $el: MaybeRef<Measurable | undefined | ComponentPublicInstance>\n) => {\n  if (!isClient) return\n  return unrefElement($el as HTMLElement)\n}\n\nfunction genModifiers(options: PopperCoreConfigProps) {\n  const { offset, gpuAcceleration, fallbackPlacements } = options\n  return [\n    {\n      name: 'offset',\n      options: {\n        offset: [0, offset ?? 12],\n      },\n    },\n    {\n      name: 'preventOverflow',\n      options: {\n        padding: {\n          top: 2,\n          bottom: 2,\n          left: 5,\n          right: 5,\n        },\n      },\n    },\n    {\n      name: 'flip',\n      options: {\n        padding: 5,\n        fallbackPlacements,\n      },\n    },\n    {\n      name: 'computeStyles',\n      options: {\n        gpuAcceleration,\n      },\n    },\n  ]\n}\n\nfunction deriveExtraModifiers(\n  options: any,\n  modifiers: PopperCoreConfigProps['popperOptions']['modifiers']\n) {\n  if (modifiers) {\n    options.modifiers = [...options.modifiers, ...(modifiers ?? [])]\n  }\n}\n"], "names": [], "mappings": ";;AAEY,MAAC,kBAAkB,GAAG,CAAC,KAAK,EAAE,SAAS,GAAG,EAAE,KAAK;AAC7D,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,aAAa,EAAE,GAAG,KAAK,CAAC;AACvD,EAAE,MAAM,OAAO,GAAG;AAClB,IAAI,SAAS;AACb,IAAI,QAAQ;AACZ,IAAI,GAAG,aAAa;AACpB,IAAI,SAAS,EAAE,CAAC,GAAG,YAAY,CAAC,KAAK,CAAC,EAAE,GAAG,SAAS,CAAC;AACrD,GAAG,CAAC;AACJ,EAAE,oBAAoB,CAAC,OAAO,EAAE,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC;AAC1F,EAAE,OAAO,OAAO,CAAC;AACjB,EAAE;AACU,MAAC,kBAAkB,GAAG,CAAC,GAAG,KAAK;AAC3C,EAAE,IAAI,CAAC,QAAQ;AACf,IAAI,OAAO;AACX,EAAE,OAAO,YAAY,CAAC,GAAG,CAAC,CAAC;AAC3B,EAAE;AACF,SAAS,YAAY,CAAC,OAAO,EAAE;AAC/B,EAAE,MAAM,EAAE,MAAM,EAAE,eAAe,EAAE,kBAAkB,EAAE,GAAG,OAAO,CAAC;AAClE,EAAE,OAAO;AACT,IAAI;AACJ,MAAM,IAAI,EAAE,QAAQ;AACpB,MAAM,OAAO,EAAE;AACf,QAAQ,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,IAAI,GAAG,MAAM,GAAG,EAAE,CAAC;AACjD,OAAO;AACP,KAAK;AACL,IAAI;AACJ,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,OAAO,EAAE;AACf,QAAQ,OAAO,EAAE;AACjB,UAAU,GAAG,EAAE,CAAC;AAChB,UAAU,MAAM,EAAE,CAAC;AACnB,UAAU,IAAI,EAAE,CAAC;AACjB,UAAU,KAAK,EAAE,CAAC;AAClB,SAAS;AACT,OAAO;AACP,KAAK;AACL,IAAI;AACJ,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,OAAO,EAAE;AACf,QAAQ,OAAO,EAAE,CAAC;AAClB,QAAQ,kBAAkB;AAC1B,OAAO;AACP,KAAK;AACL,IAAI;AACJ,MAAM,IAAI,EAAE,eAAe;AAC3B,MAAM,OAAO,EAAE;AACf,QAAQ,eAAe;AACvB,OAAO;AACP,KAAK;AACL,GAAG,CAAC;AACJ,CAAC;AACD,SAAS,oBAAoB,CAAC,OAAO,EAAE,SAAS,EAAE;AAClD,EAAE,IAAI,SAAS,EAAE;AACjB,IAAI,OAAO,CAAC,SAAS,GAAG,CAAC,GAAG,OAAO,CAAC,SAAS,EAAE,GAAG,SAAS,IAAI,IAAI,GAAG,SAAS,GAAG,EAAE,CAAC,CAAC;AACtF,GAAG;AACH;;;;"}