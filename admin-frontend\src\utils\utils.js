import storage from './storage';
import menu from './menu';
import base from './base';
/**
 * 格式化图片URL
 * @param {String} url 图片URL
 * @param {String} baseUrl 可选的基础URL，如果不提供则使用base.js中的URL
 * @returns {String} 格式化后的URL
 */
export function formatImageUrl(url, baseUrl) {
    if (!url) return '';
    
    // 使用base.js中的URL或传入的baseUrl
    baseUrl = baseUrl || base.get().url;
    
    // 修复http:/和https:/问题
    if (url.startsWith('http:/') && !url.startsWith('http://')) {
        url = url.replace('http:/', 'http://');
    }
    if (url.startsWith('https:/') && !url.startsWith('https://')) {
        url = url.replace('https:/', 'https://');
    }
    
    // 如果已经是完整URL
    if (url.startsWith('http://') || url.startsWith('https://')) {
        // 修复双斜杠问题
        return url.replace(/([^:])\/\//g, "$1/");
    }
    
    // 避免相对路径处理错误
    if (url.startsWith('/upload/') || url.startsWith('upload/')) {
        // 统一处理上传文件路径
        url = url.replace(/^\/?(upload\/.*)$/, "$1");
        return baseUrl + url;
    }
    
    // 处理baseUrl和url的拼接，避免双斜杠问题
    if (url.startsWith('/') && baseUrl.endsWith('/')) {
        // 如果URL以/开头且baseUrl以/结尾，移除URL开头的/
        url = url.substring(1);
    } else if (!url.startsWith('/') && !baseUrl.endsWith('/')) {
        // 如果URL不以/开头且baseUrl不以/结尾，添加/
        url = '/' + url;
    }
    
    // 拼接并修复可能的双斜杠问题
    let fullUrl = baseUrl + url;
    return fullUrl.replace(/([^:])\/\//g, "$1/");
}

/**
 * 是否有权限
 * @param {*} key
 */
export function isAuth(tableName,key) {
    let role = storage.get("role");
    if(!role){
        role = '管理员';
    }
    let menus = menu.list();
    for(let i=0;i<menus.length;i++){
        if(menus[i].roleName==role){
            for(let j=0;j<menus[i].backMenu.length;j++){
                for(let k=0;k<menus[i].backMenu[j].child.length;k++){
                    if(tableName==menus[i].backMenu[j].child[k].tableName){
                        let buttons = menus[i].backMenu[j].child[k].buttons.join(',');
                        return buttons.indexOf(key) !== -1 || false
                    }
                }
            }
        }
    }
    return false;
}

/**
 *  * 获取当前时间（yyyy-MM-dd hh:mm:ss）
 *   */
export function getCurDateTime() {
    let currentTime = new Date(),
    year = currentTime.getFullYear(),
    month = currentTime.getMonth() + 1 < 10 ? '0' + (currentTime.getMonth() + 1) : currentTime.getMonth() + 1,
    day = currentTime.getDate() < 10 ? '0' + currentTime.getDate() : currentTime.getDate(),
    hour = currentTime.getHours(),
    minute = currentTime.getMinutes(),
    second = currentTime.getSeconds();
    return year + "-" + month + "-" + day + " " +hour +":" +minute+":"+second;
}

/**
 *  * 获取当前日期（yyyy-MM-dd）
 *   */
export function getCurDate() {
    let currentTime = new Date(),
    year = currentTime.getFullYear(),
    month = currentTime.getMonth() + 1 < 10 ? '0' + (currentTime.getMonth() + 1) : currentTime.getMonth() + 1,
    day = currentTime.getDate() < 10 ? '0' + currentTime.getDate() : currentTime.getDate();
    return year + "-" + month + "-" + day;
}

/**
* 获取年
*/
export function getYearFormat(currentTime) {
	if(currentTime==null){
		currentTime = new Date();
	}
	var year = currentTime.getFullYear();
	return year;
}

/**
 * 获取月
 */
export function getMonthFormat(currentTime) {
	if(currentTime==null){
		currentTime = new Date();
	}
	var year = currentTime.getFullYear();
	var month = currentTime.getMonth() + 1 < 10 ? '0' + (currentTime.getMonth() + 1) : currentTime.getMonth() + 1;
	return year + "-" + month;
}

/**
 *日期格式化（yyyy-MM-dd）
 */
export function getDateFormat(currentTime) {
	if(currentTime==null){
		currentTime = new Date();
	}
	var year = currentTime.getFullYear();
	var month = currentTime.getMonth() + 1 < 10 ? '0' + (currentTime.getMonth() + 1) : currentTime.getMonth() + 1;
	var day = currentTime.getDate() < 10 ? '0' + currentTime.getDate() : currentTime.getDate();
	return year + "-" + month + "-" + day;
}


/**
 * 时间格式化（yyyy-MM-dd hh:mm:ss）
 */
export function getDatetimeFormat(currentTime) {
	if(currentTime==null){
		currentTime = new Date();
	}
	var year = currentTime.getFullYear();
	var month = currentTime.getMonth() + 1 < 10 ? '0' + (currentTime.getMonth() + 1) : currentTime.getMonth() + 1;
	var day = currentTime.getDate() < 10 ? '0' + currentTime.getDate() : currentTime.getDate();
	var hour = currentTime.getHours();
	var minute = currentTime.getMinutes();
	var second = currentTime.getSeconds();
	return year + "-" + month + "-" + day + " " +hour +":" +minute+":"+second;
}